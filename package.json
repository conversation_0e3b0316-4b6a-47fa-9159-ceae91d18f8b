{"devDependencies": {"@babel/core": "^7.21.3", "@babel/preset-env": "^7.20.2", "@fortawesome/fontawesome-free": "^5.13.1", "@symfony/webpack-encore": "^4.0.0", "bootstrap": "^4.5.0", "bs-custom-file-input": "^1.3.4", "core-js": "^3.0.0", "css-loader": "^6.7.3", "file-loader": "^6.0.0", "jquery-ui": "^1.12.1", "mini-css-extract-plugin": "^2.6.1", "popper.js": "^1.16.1", "regenerator-runtime": "^0.13.2", "sass-loader": "^13.0.0", "vue-loader": "15", "vue-template-compiler": "^2.6.12", "webpack": "^5.76.1", "webpack-cli": "^5.0.1", "webpack-notifier": "^1.6.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "node ./assets/downloadLocales.js && encore dev --watch", "build": "node ./assets/downloadLocales.js && encore dev --progress", "locales": "node ./scripts/locales", "deploy": "clear && node ./scripts/deploy", "tag-upgrade": "clear && node ./scripts/tag-upgrade"}, "dependencies": {"@kevinfaguiar/vue-twemoji-picker": "^5.7.4", "@vimeo/vimeo": "^2.1.0", "axios": "1.8.2", "date-fns": "^4.1.0", "dotenv-flow": "^4.1.0", "froala-editor": "^4.0.1", "highcharts": "^9.0.1", "highcharts-vue": "^1.3.5", "html2pdf": "^0.0.11", "inquirer": "^8.0.0", "inquirer-autocomplete-prompt": "^2.0.0", "jquery": "^3.5.1", "jspdf": "^2.5.1", "jw-vue-pagination": "^1.0.3", "node-polyfill-webpack-plugin": "^2.0.1", "node-ssh": "^13.1.0", "pdfjs-dist": "3.4.120", "process": "^0.11.10", "qrious": "^4.0.2", "sass": "^1.57.1", "select2": "^4.1.0-rc.0", "vue": "^2.6.12", "vue-advanced-cropper": "^1.3.4", "vue-alertify": "^1.1.0", "vue-check-view": "^0.3.0", "vue-doc-preview": "^0.3.2", "vue-froala-wysiwyg": "^4.0.17", "vue-html2pdf": "^1.8.0", "vue-i18n": "^8.28.2", "vue-multiselect": "^2.1.6", "vue-pdf": "^4.2.0", "vue-router": "^3.6.5", "vue-scrollto": "^2.20.0", "vue-toast-notification": "^0.6.1", "vue-vimeo-player": "^0.1.2", "vue-youtube": "^1.4.0", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vuex-pathify": "^1.5.1", "xlsx": "^0.18.5", "yarn": "^1.22.22"}, "engines": {"node": "^20.10.0"}}