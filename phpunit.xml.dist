<?xml version="1.0" encoding="UTF-8"?>

<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         backupGlobals="false"
         colors="true"
         bootstrap="tests/bootstrap.php"
         defaultTestSuite="Project Test Suite"
         displayDetailsOnPhpunitDeprecations="true"
         displayDetailsOnTestsThatTriggerWarnings="true"
         displayDetailsOnTestsThatTriggerDeprecations="true"
>
    <php>
        <ini name="display_errors" value="1" />
        <ini name="error_reporting" value="-1" />
        <ini name="memory_limit" value="512M" />
        <server name="APP_ENV" value="test" force="true" />
        <server name="SHELL_VERBOSITY" value="-1" />
        <server name="SYMFONY_PHPUNIT_REMOVE" value="" />
        <server name="SYMFONY_PHPUNIT_VERSION" value="9.5" />
        <env name="KERNEL_CLASS" value="App\Kernel" />
        <env name="SYMFONY_DEPRECATIONS_HELPER" value="weak" />
<!--        <env name="SYMFONY_DEPRECATIONS_HELPER" value="max[total]=999999" />-->
    </php>

    <testsuites>
        <testsuite name="Project Test Suite">
            <directory>tests</directory>
            <exclude>tests/Functional/Database</exclude>
        </testsuite>
        <testsuite name="only_migrations">
            <directory>tests/Functional/Database</directory>
        </testsuite>
    </testsuites>

    <extensions>
    </extensions>
</phpunit>
