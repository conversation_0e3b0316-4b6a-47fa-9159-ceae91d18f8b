<?php

declare(strict_types=1);

namespace App\Tests\Entity;

use App\Entity\Chapter;
use App\Entity\ChapterType;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class ChapterTest extends TestCase
{
    /**
     * @throws Exception
     */
    #[DataProvider('providerChapterIsActive')]
    public function testIsIsActiveReturnsExpectedValue(
        ?bool $chapterActive,
        ?bool $typeActive,
        ?bool $expected
    ): void {
        $chapter = new Chapter();

        $typeMock = $this->createMock(ChapterType::class);
        $typeMock->method('isActive')->willReturn($typeActive);
        $chapter->setType($typeMock);

        $chapter->setIsActive($chapterActive);

        $this->assertEquals($expected, $chapter->isIsActive());
    }

    public static function providerChapterIsActive(): \Generator
    {
        yield 'Chapter active and type active' => [
            'chapterActive' => true,
            'typeActive' => true,
            'expected' => true,
        ];
        yield 'Chapter active and type not active' => [
            'chapterActive' => true,
            'typeActive' => false,
            'expected' => false,
        ];
        yield 'Chapter not active and type active' => [
            'chapterActive' => false,
            'typeActive' => true,
            'expected' => false,
        ];
        yield 'Chapter not active and type not active' => [
            'chapterActive' => false,
            'typeActive' => false,
            'expected' => false,
        ];
        yield 'Chapter null and type active' => [
            'chapterActive' => null,
            'typeActive' => true,
            'expected' => null,
        ];

        yield 'Chapter null and type not active' => [
            'chapterActive' => null,
            'typeActive' => null,
            'expected' => null,
        ];
    }
}
