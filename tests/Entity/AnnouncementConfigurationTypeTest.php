<?php

namespace App\Tests\Entity;

use App\Entity\AnnouncementConfigurationType;
use App\Entity\ConfigurationClientAnnouncement;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class AnnouncementConfigurationTypeTest extends TestCase
{
    /**
     * Test the getType method with different scenarios
     */
    #[DataProvider('getTypeProvider')]
    public function testGetType(
        ?ConfigurationClientAnnouncement $configurationClientAnnouncement,
        ?string $announcementConfigurationTypeName,
        string $expected,
    ): void {
        $announcementConfigurationType = new AnnouncementConfigurationType();

        if ($announcementConfigurationTypeName !== null) {
            $announcementConfigurationType->setName($announcementConfigurationTypeName);
        }

        if ($configurationClientAnnouncement !== null) {
            $announcementConfigurationType->setConfigurationClientAnnouncement($configurationClientAnnouncement);
        }

        $this->assertEquals($expected, $announcementConfigurationType->getType());
    }

    public static function getTypeProvider(): \Generator
    {
        // Case 1: ConfigurationClientAnnouncement exists and has a name
        $configurationClientAnnouncement = new ConfigurationClientAnnouncement();
        $configurationClientAnnouncement->setName('Client Configuration Name');
        yield 'ConfigurationClientAnnouncement exists with name' => [
            $configurationClientAnnouncement,
            'Announcement Type Name',
            'Client Configuration Name'
        ];

        // Case 2: ConfigurationClientAnnouncement is null, fallback to AnnouncementConfigurationType name
        yield 'ConfigurationClientAnnouncement is null, use AnnouncementConfigurationType name' => [
            null,
            'Announcement Type Name',
            'Announcement Type Name'
        ];

        // Case 3: Both ConfigurationClientAnnouncement and AnnouncementConfigurationType name are null
        yield 'Both ConfigurationClientAnnouncement and AnnouncementConfigurationType name are null' => [
            null,
            null,
            ''
        ];

        // Case 4: ConfigurationClientAnnouncement exists but has null name,
        // fallback to AnnouncementConfigurationType name
        // Note: We can't test null name directly as setName() doesn't accept null, so we test empty string instead
        $configurationClientAnnouncementWithNullName = new ConfigurationClientAnnouncement();
        // We'll simulate null by not setting the name at all, which should return null from getName()
        yield 'ConfigurationClientAnnouncement exists but name is not set (null)' => [
            $configurationClientAnnouncementWithNullName,
            'Announcement Type Name',
            'Announcement Type Name'
        ];

        // Case 5: ConfigurationClientAnnouncement exists but has empty name, returns empty string (not fallback)
        $configurationClientAnnouncementWithEmptyName = new ConfigurationClientAnnouncement();
        $configurationClientAnnouncementWithEmptyName->setName('');
        yield 'ConfigurationClientAnnouncement exists but name is empty' => [
            $configurationClientAnnouncementWithEmptyName,
            'Announcement Type Name',
            '' // Empty string is returned, not the fallback
        ];

        // Case 6: ConfigurationClientAnnouncement has name but AnnouncementConfigurationType name is null
        $configurationClientAnnouncementWithName = new ConfigurationClientAnnouncement();
        $configurationClientAnnouncementWithName->setName('Client Configuration Name');
        yield 'ConfigurationClientAnnouncement has name, AnnouncementConfigurationType name is null' => [
            $configurationClientAnnouncementWithName,
            null,
            'Client Configuration Name'
        ];
    }

    /**
     * Test that the method handles edge cases gracefully
     */
    public function testGetTypeWithComplexScenarios(): void
    {
        $announcementConfigurationType = new AnnouncementConfigurationType();

        // Test with whitespace-only name
        $configurationClientAnnouncement = new ConfigurationClientAnnouncement();
        $configurationClientAnnouncement->setName('   ');
        $announcementConfigurationType->setConfigurationClientAnnouncement($configurationClientAnnouncement);
        $announcementConfigurationType->setName('Fallback Name');

        // Should return the whitespace name as it's not null or empty string
        $this->assertEquals('   ', $announcementConfigurationType->getType());
    }

    /**
     * Test the fallback chain works correctly
     */
    public function testGetTypeFallbackChain(): void
    {
        $announcementConfigurationType = new AnnouncementConfigurationType();

        // Step 1: No ConfigurationClientAnnouncement, no name -> empty string
        $this->assertEquals('', $announcementConfigurationType->getType());

        // Step 2: Add name to AnnouncementConfigurationType -> should return that name
        $announcementConfigurationType->setName('Type Name');
        $this->assertEquals('Type Name', $announcementConfigurationType->getType());

        // Step 3: Add ConfigurationClientAnnouncement with name -> should return client name
        $configurationClientAnnouncement = new ConfigurationClientAnnouncement();
        $configurationClientAnnouncement->setName('Client Name');
        $announcementConfigurationType->setConfigurationClientAnnouncement($configurationClientAnnouncement);
        $this->assertEquals('Client Name', $announcementConfigurationType->getType());

        // Step 4: Set client name to empty string -> should return empty string (not fallback)
        $configurationClientAnnouncement->setName('');
        $this->assertEquals('', $announcementConfigurationType->getType());
    }
}
