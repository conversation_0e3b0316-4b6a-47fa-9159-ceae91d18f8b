<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Common\TimeZones;

use App\Service\SettingsService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Backend\BackendTimeZonesEndpoints;
use Symfony\Component\HttpFoundation\Response;

class GetTimeZonesFunctionalTest extends FunctionalTestCase
{
    public function testGetTimeZonesOkResponse(): void
    {
        $userToken = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendTimeZonesEndpoints::timeZonesEndpoint(),
            bearerToken: $userToken,
        );
        $responseData = $this->extractResponseData($response);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertNotEmpty($responseData);

        foreach ($responseData as $timeZone) {
            $this->assertArrayHasKey('value', $timeZone);
            $this->assertArrayHasKey('default', $timeZone);
            $this->assertIsString($timeZone['value']);
            $this->assertIsBool($timeZone['default']);
        }

        // Check if there is at least one default timezone.
        $defaultTimeZones = array_filter($responseData, function ($timeZone) {
            return true === $timeZone['default'];
        });

        $this->assertNotEmpty($defaultTimeZones);
    }

    public function testTimeZonesUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendTimeZonesEndpoints::timeZonesEndpoint(),
            bearerToken: '',
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testTimeZonesDefaultTimeZoneNotInList(): void
    {
        $settingsService = self::getContainer()->get(SettingsService::class);
        $defaultTimeZone = $settingsService->get('app.default_timezone');

        $settingsService->setSetting('app.default_timezone', 'Europe/London');

        $userToken = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendTimeZonesEndpoints::timeZonesEndpoint(),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());

        $settingsService->setSetting('app.default_timezone', $defaultTimeZone);
    }
}
