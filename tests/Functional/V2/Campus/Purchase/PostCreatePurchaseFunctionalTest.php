<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Campus\Purchase;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Campus\CampusPurchaseEndpoints;
use App\Tests\Functional\HelperTrait\PurchasableItemHelperTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Shared\Purchase\PurchaseStatusTransformer;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PostCreatePurchaseFunctionalTest extends FunctionalTestCase
{
    use PurchasableItemHelperTrait;

    private const string VALID_UUID_1 = '550e8400-e29b-41d4-a716-************';
    private const string INVALID_UUID = 'invalid-uuid';

    private User $testUser;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->testUser = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'test.purchase.' . uniqid() . '@example.com',
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        if (!empty($this->testUser)) {
            $this->hardDeleteUsersByIds([$this->testUser->getId()]);
        }

        parent::tearDown();
    }

    /**
     * @throws InvalidUuidException
     */
    public function testValidUuidStructure(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $requestBody = [
            'items' => [
                UuidMother::create()->value(),
                UuidMother::create()->value(),
            ],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody,
            bearerToken: $userToken
        );

        // The request should pass validation (UUIDs are valid)
        // It might fail later due to missing purchasable items, but that's a business logic error, not validation
        $this->assertContains($response->getStatusCode(), [
            Response::HTTP_CREATED,
            Response::HTTP_BAD_REQUEST,
            Response::HTTP_NOT_FOUND,
            Response::HTTP_UNPROCESSABLE_ENTITY,
        ]);

        $this->assertJson($response->getContent());

        // If it's a validation error, it should have the proper structure
        if (Response::HTTP_BAD_REQUEST === $response->getStatusCode()) {
            $responseData = json_decode($response->getContent(), true);
            $this->assertArrayHasKey('error', $responseData);
            $this->assertEquals(1, $responseData['error']);
        }
    }

    #[DataProvider('invalidDataProvider')]
    public function testValidationErrors(
        array $requestBody,
        int $expectedStatusCode,
    ): void {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals('Validation failed', $responseData['message']);
    }

    public function testUserRoleAccess(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $requestBody = [
            'items' => [self::VALID_UUID_1],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody,
            bearerToken: $userToken
        );

        // Should have access with ROLE_USER
        $this->assertContains($response->getStatusCode(), [Response::HTTP_CREATED, Response::HTTP_UNPROCESSABLE_ENTITY]);

        // If it's a 422, it means validation failed but access was granted
        // If it's a 201, it means the purchase was created successfully
        if (Response::HTTP_UNPROCESSABLE_ENTITY === $response->getStatusCode()) {
            $responseData = json_decode($response->getContent(), true);
            $this->assertArrayHasKey('error', $responseData);
        }
    }

    public function testUnauthenticatedAccess(): void
    {
        $requestBody = [
            'items' => [self::VALID_UUID_1],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testEmptyRequestBody(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: [],
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals('Validation failed', $responseData['message']);
    }

    public function testMalformedJsonRequest(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $this->client->request(
            'POST',
            'http://localhost' . CampusPurchaseEndpoints::purchasesEndpoint(),
            [],
            [],
            ['HTTP_Authorization' => 'Bearer ' . $userToken],
            'invalid-json'
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }

    public static function invalidDataProvider(): \Generator
    {
        yield 'missing items field' => [
            'requestBody' => [],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'null items field' => [
            'requestBody' => ['items' => null],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'empty items array' => [
            'requestBody' => ['items' => []],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'invalid uuid format' => [
            'requestBody' => ['items' => [self::INVALID_UUID]],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'mixed valid and invalid uuids' => [
            'requestBody' => ['items' => [self::VALID_UUID_1, self::INVALID_UUID]],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'non-string items' => [
            'requestBody' => ['items' => [123, 456]],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testAdminRoleAccess(): void
    {
        // Create admin user
        $adminUser = $this->createAndGetUser(
            roles: [User::ROLE_ADMIN],
            email: 'admin.test.' . uniqid() . '@example.com',
        );

        $userToken = $this->loginAndGetTokenForUser($adminUser);

        $requestBody = [
            'items' => [self::VALID_UUID_1],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody,
            bearerToken: $userToken
        );

        // Should have access with ROLE_ADMIN
        $this->assertContains($response->getStatusCode(), [Response::HTTP_CREATED, Response::HTTP_UNPROCESSABLE_ENTITY]);

        // Clean up admin user
        $this->hardDeleteUsersByIds([$adminUser->getId()]);
    }

    public function testPurchaseWithNonPurchasableItem(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $requestBody = [
            'items' => [self::VALID_UUID_1],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testPurchaseWithInactivePurchasableItem(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $purchasableItem = $this->createAndGetPurchasableItem(
            isActive: false
        );

        $requestBody = [
            'items' => [$purchasableItem->getId()->value()],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testPurchaseWithValidPurchasableItem(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $purchasableItem = $this->createAndGetPurchasableItem();

        $requestBody = [
            'items' => [$purchasableItem->getId()->value()],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: CampusPurchaseEndpoints::purchasesEndpoint(),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());

        $decodedResponse = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $decodedResponse);
        $data = $decodedResponse['data'];
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('user_id', $data);
        $this->assertEquals($this->testUser->getId(), $data['user_id']);
        $this->assertArrayHasKey('status', $data);
        $this->assertEquals(PurchaseStatus::Pending, PurchaseStatusTransformer::fromString($data['status']));
        $this->assertArrayHasKey('amount', $data);
        $this->assertEquals($purchasableItem->getPrice()->value(), $data['amount']);
        $this->assertArrayHasKey('currency_code', $data);
        $this->assertEquals($purchasableItem->getPrice()->currency()->code()->name, $data['currency_code']);
        $this->assertArrayHasKey('tax_rate', $data);
        $this->assertEquals(0.21, $data['tax_rate']);
        $this->assertArrayHasKey('tax_amount', $data);
        $this->assertEquals((int) round($purchasableItem->getPrice()->value() * 0.21), $data['tax_amount']);
        $this->assertArrayHasKey('items', $data);
        $this->assertCount(1, $data['items']);
        $item = $data['items'][0];
        $this->assertArrayHasKey('id', $item);
        $this->assertArrayHasKey('price_amount', $item);
        $this->assertEquals($purchasableItem->getPrice()->value(), $item['price_amount']);
        $this->assertArrayHasKey('price_currency', $item);
        $this->assertEquals($purchasableItem->getPrice()->currency()->code()->name, $item['price_currency']);
    }
}
