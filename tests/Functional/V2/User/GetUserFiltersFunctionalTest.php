<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\User;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use App\Tests\Functional\HelperTrait\FilterHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\ManagerFilterFixtureTrait;
use App\Tests\Functional\V2\Fixtures\UserFilterFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetUserFiltersFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use FilterHelperTrait;
    use UserFilterFixtureTrait;
    use ManagerFilterFixtureTrait;

    private const string EMAIL_MANAGER = '<EMAIL>';
    private const string EMAIL_USER = '<EMAIL>';
    private ?User $manager;
    private ?User $tutor;

    private array $userIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->manager = $this->createAndGetUser(roles: [User::ROLE_MANAGER], email: self::EMAIL_MANAGER);
        $this->tutor = $this->createAndGetUser(roles: [User::ROLE_TUTOR], email: self::EMAIL_USER);

        $this->userIds[] = $this->manager->getId();
        $this->userIds[] = $this->tutor->getId();
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::userFiltersEndpoint(1)
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken(email: self::EMAIL_USER);
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::userFiltersEndpoint(1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken(email: self::EMAIL_MANAGER);
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::userFiltersEndpoint(-1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[id]' => 'id must be greater than 0.',
            ],
        ], $content['metadata']);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function testGetFilters(): void
    {
        $category = $this->createAndGetFilterCategory(name: 'Test Category');
        $category2 = $this->createAndGetFilterCategory(name: 'Test Category');
        $filter1 = $this->createAndGetFilter(name: 'Filter 1', category: $category);
        $filter2 = $this->createAndGetFilter(name: 'Filter 2', category: $category);
        $filter3 = $this->createAndGetFilter(name: 'Filter 3', category: $category2);

        $this->setAndGetUserFilterInRepository(
            userId: new Id($this->tutor->getId()),
            filterId: new Id($filter1->getId()),
        );

        $this->setAndGetUserFilterInRepository(
            userId: new Id($this->tutor->getId()),
            filterId: new Id($filter2->getId()),
        );

        $this->setAndGetUserFilterInRepository(
            userId: new Id($this->tutor->getId()),
            filterId: new Id($filter3->getId()),
        );

        $token = $this->loginAndGetToken(email: self::EMAIL_MANAGER);
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::userFiltersEndpoint($this->tutor->getId()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertCount(3, $data);
        foreach ($data as $filterData) {
            $expected = match ($filterData['id']) {
                1 => [
                    'id' => $filter1->getId(),
                    'name' => 'Filter 1',
                    'category_id' => $category->getId(),
                ],
                2 => [
                    'id' => $filter2->getId(),
                    'name' => 'Filter 2',
                    'category_id' => $category->getId(),
                ],
                3 => [
                    'id' => $filter3->getId(),
                    'name' => 'Filter 3',
                    'category_id' => $category2->getId(),
                ],
                default => $this->fail('Unexpected filter result')
            };

            $this->assertEquals($expected, $filterData);
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Filter::class, FilterCategory::class,
        ]);
        $this->hardDeleteUsersByIds($this->userIds);
        parent::tearDown();
    }
}
