<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\User;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use App\Tests\Functional\HelperTrait\FilterHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\ManagerFilterFixtureTrait;
use App\Tests\Functional\V2\Fixtures\UserFilterFixtureTrait;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\UserFilter\UserFilterCriteria;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class PostUserFiltersFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use FilterHelperTrait;
    use UserFilterFixtureTrait;
    use ManagerFilterFixtureTrait;

    private const string EMAIL_MANAGER = '<EMAIL>';
    private const string EMAIL_USER = '<EMAIL>';
    private ?User $manager;
    private ?User $tutor;

    private array $userIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->manager = $this->createAndGetUser(roles: [User::ROLE_MANAGER], email: self::EMAIL_MANAGER);
        $this->tutor = $this->createAndGetUser(roles: [User::ROLE_TUTOR], email: self::EMAIL_USER);

        $this->userIds[] = $this->manager->getId();
        $this->userIds[] = $this->tutor->getId();
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint(1)
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken(email: self::EMAIL_USER);
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint(1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken(email: self::EMAIL_MANAGER);
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint(1),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '' => 'Body cannot be empty',
            ],
        ], $content['metadata']);

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint(1),
            body: json_encode([0, -1, 'aA']),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[0]' => 'This value should be greater than or equal to 1.',
                '[1]' => 'This value should be greater than or equal to 1.',
                '[2]' => 'This value should be of type integer.',
            ],
        ], $content['metadata']);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InfrastructureException
     */
    public function testPostUserFiltersAsManager(): void
    {
        /** @var UserFilterRepository $userFilterRepository */
        $userFilterRepository = $this->client->getContainer()
            ->get('App\V2\Domain\User\UserFilter\UserFilterRepository');

        $userFiltersResult = $userFilterRepository->findBy(
            UserFilterCriteria::createEmpty()->filterByUserId(new Id($this->tutor->getId()))
        );
        $this->assertCount(0, $userFiltersResult);

        $category = $this->createAndGetFilterCategory(name: 'Test Category');
        $filter1 = $this->createAndGetFilter(name: 'Filter 1', category: $category);
        $filter2 = $this->createAndGetFilter(name: 'Filter 2', category: $category);
        $filter3 = $this->createAndGetFilter(name: 'Filter 3', category: $category);

        $this->setAndGetManagerFilterInRepository(
            userId: new Id($this->manager->getId()),
            filterId: new Id($filter1->getId())
        );

        $token = $this->loginAndGetToken(email: self::EMAIL_MANAGER);

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint($this->tutor->getId()),
            body: json_encode([$filter1->getId(), $filter2->getId()]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'At least one of the filters is not managed by the user',
            $content['message'],
        );

        $this->setAndGetManagerFilterInRepository(
            userId: new Id($this->manager->getId()),
            filterId: new Id($filter2->getId())
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint($this->tutor->getId()),
            body: json_encode([$filter1->getId(), $filter2->getId()]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $userFiltersResult = $userFilterRepository->findBy(
            UserFilterCriteria::createEmpty()->filterByUserId(new Id($this->tutor->getId()))
        );
        $this->assertCount(2, $userFiltersResult);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InfrastructureException
     */
    public function testPostUserFiltersAsAdmin(): void
    {
        /** @var UserFilterRepository $userFilterRepository */
        $userFilterRepository = $this->client->getContainer()
            ->get('App\V2\Domain\User\UserFilter\UserFilterRepository');

        $userFiltersResult = $userFilterRepository->findBy(
            UserFilterCriteria::createEmpty()->filterByUserId(new Id($this->tutor->getId()))
        );
        $this->assertCount(0, $userFiltersResult);
        $adminFiltersResult = $userFilterRepository->findBy(
            UserFilterCriteria::createEmpty()->filterByUserId(new Id(self::DEFAULT_USER_ID))
        );
        $this->assertCount(0, $adminFiltersResult);

        $category = $this->createAndGetFilterCategory(name: 'Test Category');
        $filter1 = $this->createAndGetFilter(name: 'Filter 1', category: $category);
        $filter2 = $this->createAndGetFilter(name: 'Filter 2', category: $category);
        $filter3 = $this->createAndGetFilter(name: 'Filter 3', category: $category);

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint($this->tutor->getId()),
            body: json_encode([$filter1->getId(), $filter2->getId(), $filter3->getId()]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint($this->tutor->getId()),
            body: json_encode([$filter2->getId()]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'At least one of the filters is already assigned to a user',
            $content['message'],
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint($this->tutor->getId()),
            body: json_encode([999]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'At least one of the filters does not exist',
            $content['message'],
        );
    }

    public function testUserNotFound(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminUsersEndpoints::userFiltersEndpoint(9999),
            body: json_encode([1]),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'User not found',
            $content['message'],
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Filter::class, FilterCategory::class,
        ]);
        $this->hardDeleteUsersByIds($this->userIds);
        parent::tearDown();
    }
}
