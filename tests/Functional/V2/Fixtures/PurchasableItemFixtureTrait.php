<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;

trait PurchasableItemFixtureTrait
{
    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    private function setAndGetPurchasableItemInRepository(
        ?Uuid $id = null,
        ?string $name = null,
        ?string $description = null,
        ?int $priceAmount = null,
        ?string $priceCurrency = null,
        ?ResourceType $resourceType = null,
        ?Identifier $resourceId = null,
        ?\DateTimeImmutable $createdAt = null,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
        ?bool $isActive = null,
    ): PurchasableItem {
        $item = PurchasableItemMother::create(
            id: $id,
            name: $name,
            description: $description,
            price: MoneyMother::create(
                amount: $priceAmount ?? 1000,
                currency: new Currency(CurrencyCodeTransformer::fromString($priceCurrency ?? 'EUR'))
            ),
            resource: ResourceMother::create(
                type: $resourceType ?? ResourceType::Course,
                id: $resourceId ?? new Id(1)
            ),
            createdAt: $createdAt,
            updatedAt: $updatedAt,
            deletedAt: $deletedAt,
            isActive: $isActive,
        );

        $this->client->getContainer()->get('App\V2\Domain\Purchase\PurchasableItemRepository')
            ->put($item);

        return $item;
    }
}
