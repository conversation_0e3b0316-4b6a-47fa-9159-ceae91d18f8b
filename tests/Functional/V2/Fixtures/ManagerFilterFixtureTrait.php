<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;

trait ManagerFilterFixtureTrait
{
    private function getManagerFilterRepository(): object
    {
        return $this->client->getContainer()->get('App\V2\Domain\User\ManagerFilter\ManagerFilterRepository');
    }

    private function setAndGetManagerFilterInRepository(
        Id $userId,
        Id $filterId,
    ): ManagerFilter {
        $managerFilter = ManagerFilterMother::create(
            userId: $userId,
            filterId: $filterId
        );

        $this->client->getContainer()->get('App\V2\Domain\User\ManagerFilter\ManagerFilterRepository')
            ->insert($managerFilter);

        return $managerFilter;
    }
}
