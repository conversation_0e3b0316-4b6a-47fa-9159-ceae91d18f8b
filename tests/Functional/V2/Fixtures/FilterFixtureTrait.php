<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Filter\FilterMother;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Persistence\Filter\InMemoryFilterRepository;

trait FilterFixtureTrait
{
    private function getFilterRepository(): object
    {
        return $this->client->getContainer()
            ->get('App\V2\Domain\Filter\FilterRepository');
    }

    private function setAndGetFilterInRepository(
        ?Id $id = null,
        ?Id $filterCategoryId = null,
        ?string $name = null,
        ?string $code = null,
        ?int $sort = null,
        ?Id $parentId = null,
    ): Filter {
        $filter = FilterMother::create(
            id: $id,
            filterCategoryId: $filterCategoryId,
            name: $name,
            code: $code,
            sort: $sort,
            parentId: $parentId
        );

        /** @var InMemoryFilterRepository $repository */
        $repository = $this->getFilterRepository();
        $repository->add($filter);

        return $filter;
    }
}
