<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Locales;

use App\Entity\Setting;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Backend\BackendLocalesEndpoints;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;

class GetLocalesFunctionalTest extends FunctionalTestCase
{
    /**
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function testAdminLocales(): void
    {
        $newLAdminLocales = ['fr', 'jp'];
        $settingsRepository = $this->getRepository(Setting::class);
        $defaultAdminLocales = $settingsRepository->findOneBy(['code' => 'app.languages.admin'])->getValue();

        $settingsService = static::getContainer()->get('App\Service\SettingsService');
        $settingsService->setSetting('app.languages.admin', json_encode($newLAdminLocales));
        $settingsService->loadSettings();

        $userToken = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendLocalesEndpoints::adminLocalesEndpoint(),
            bearerToken: $userToken,
        );

        $responseData = $this->extractResponseData($response);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertNotEmpty($responseData);
        $this->assertIsArray($responseData);

        foreach ($responseData as $locale) {
            $this->assertIsString($locale);
            $this->assertContains($locale, $newLAdminLocales);
        }

        $settingsService->setSetting('app.languages.admin', $defaultAdminLocales);
    }

    public function testLocalesUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendLocalesEndpoints::adminLocalesEndpoint(),
            bearerToken: '',
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }
}
