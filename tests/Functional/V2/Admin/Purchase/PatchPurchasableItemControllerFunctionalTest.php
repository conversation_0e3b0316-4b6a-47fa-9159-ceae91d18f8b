<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Purchase;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\PurchasableItemEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\PurchasableItemFixtureTrait;
use App\Tests\Functional\V2\Fixtures\SubscriptionFixtureTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class PatchPurchasableItemControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use CourseHelperTrait;
    use SubscriptionFixtureTrait;
    use PurchasableItemFixtureTrait;

    private array $usersIds = [];

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->usersIds);
        $this->truncateEntities([
            'purchasable_item',
            'course',
        ]);
        parent::tearDown();
    }

    /**
     * @throws InvalidUuidException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testPatchPurchasableItemSuccessfully(): void
    {
        $course = $this->createAndGetCourse();
        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            resourceId: new Id($course->getId())
        );

        $token = $this->loginAndGetToken();

        $payload = [
            'price_amount' => 2500,
            'price_currency' => 'USD',
            'is_active' => true,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: PurchasableItemEndpoints::patchPurchasableItemEndpoint($purchasableItem->getId()->value()),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testPatchNonExistentPurchasableItem(): void
    {
        $token = $this->loginAndGetToken();

        $payload = [
            'price_amount' => 2500,
            'price_currency' => 'USD',
            'is_active' => true,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: PurchasableItemEndpoints::patchPurchasableItemEndpoint(UuidMother::create()->value()),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('not found', $responseData['message']);
    }

    /**
     * @throws InvalidUuidException
     */
    public function testUnauthorizedAccess(): void
    {
        $purchasableItemId = UuidMother::create()->value();

        $payload = [
            'price_amount' => 1000,
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: PurchasableItemEndpoints::patchPurchasableItemEndpoint($purchasableItemId),
            body: $payload
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws InvalidUuidException
     */
    public function testAccessDeniedForManagersOrCreators(): void
    {
        $user = $this->createAndGetUser(
            firstName: 'Regular',
            lastName: 'User',
            roles: [User::ROLE_USER, User::ROLE_MANAGER, User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $user->getId();

        $userToken = $this->loginAndGetTokenForUser($user);

        $payload = [
            'price_amount' => 5000,
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: PurchasableItemEndpoints::patchPurchasableItemEndpoint(UuidMother::create()->value()),
            body: $payload,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     */
    public function testPurchasableItemNotFound(): void
    {
        $token = $this->loginAndGetToken();

        $payload = [
            'price_amount' => 1000,
            'price_currency' => 'EUR',
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: PurchasableItemEndpoints::patchPurchasableItemEndpoint(UuidMother::create()->value()),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('not found', $responseData['message']);
    }
}
