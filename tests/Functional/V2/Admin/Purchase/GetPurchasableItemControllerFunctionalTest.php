<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Purchase;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\PurchasableItemEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\PurchasableItemFixtureTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetPurchasableItemControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use PurchasableItemFixtureTrait;

    private array $usersIds = [];

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->usersIds);
        $this->truncateEntities([
            'purchasable_item',
            'course',
        ]);
        parent::tearDown();
    }

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testGetPurchasableItemSuccessfully(): void
    {
        $userToken = $this->loginAndGetToken();

        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            name: 'Test Course',
            description: 'Test Course Description',
            priceAmount: 2500,
            priceCurrency: 'EUR',
            isActive: true
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $decodedResponse = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $decodedResponse);
        $this->assertIsArray($decodedResponse['data']);

        $data = $decodedResponse['data'];
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('name', $data);
        $this->assertArrayHasKey('description', $data);
        $this->assertArrayHasKey('price_amount', $data);
        $this->assertArrayHasKey('price_currency', $data);
        $this->assertArrayHasKey('resource_type', $data);
        $this->assertArrayHasKey('resource_id', $data);
        $this->assertArrayHasKey('is_active', $data);

        $this->assertEquals($purchasableItem->getId()->value(), $data['id']);
        $this->assertEquals($purchasableItem->getName(), $data['name']);
        $this->assertEquals($purchasableItem->getDescription(), $data['description']);
        $this->assertEquals($purchasableItem->getPrice()->value(), $data['price_amount']);
        $this->assertEquals($purchasableItem->getPrice()->currency()->code()->name, $data['price_currency']);
        $this->assertEquals(
            ResourceTypeTransformer::toString($purchasableItem->getResource()->getType()),
            $data['resource_type']
        );
        $this->assertEquals($purchasableItem->getResource()->getId()->value(), $data['resource_id']);
        $this->assertEquals($purchasableItem->getIsActive(), $data['is_active']);
    }

    /**
     * @throws InvalidUuidException
     */
    public function testGetNonExistentPurchasableItem(): void
    {
        $userToken = $this->loginAndGetToken();

        $nonExistentId = UuidMother::create()->value();

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemEndpoint($nonExistentId),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('not found', $responseData['message']);
    }

    #[DataProvider('invalidUuidProvider')]
    public function testGetPurchasableItemWithInvalidUuid(string $invalidUuid, int $expectedStatusCode): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemEndpoint($invalidUuid),
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        if (Response::HTTP_MOVED_PERMANENTLY !== $expectedStatusCode) {
            $responseData = json_decode($response->getContent(), true);
            $this->assertArrayHasKey('message', $responseData);
        }
    }

    public function testGetPurchasableItemWithoutAuthentication(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemEndpoint(UuidMother::create()->value())
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testGetInactivePurchasableItem(): void
    {
        $userToken = $this->loginAndGetToken();

        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            name: 'Inactive Course',
            description: 'This course is inactive',
            priceAmount: 1500,
            priceCurrency: 'USD',
            isActive: false
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $decodedResponse = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $decodedResponse);
        $data = $decodedResponse['data'];
        $this->assertFalse($data['is_active']);
        $this->assertEquals($purchasableItem->getId()->value(), $data['id']);
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testGetPurchasableItemWithDifferentCurrencies(): void
    {
        $userToken = $this->loginAndGetToken();

        // Test with USD currency
        $purchasableItemUSD = $this->setAndGetPurchasableItemInRepository(
            name: 'USD Course',
            description: 'Course priced in USD',
            priceAmount: 9999,
            priceCurrency: 'USD',
            isActive: true
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemEndpoint($purchasableItemUSD->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $decodedResponse = json_decode($response->getContent(), true);
        $data = $decodedResponse['data'];

        $this->assertEquals('USD', $data['price_currency']);
        $this->assertEquals(9999, $data['price_amount']);
    }

    /**
     * @throws OptimisticLockException
     * @throws InvalidCurrencyCodeException
     * @throws ORMException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    #[DataProvider('roleAccessProvider')]
    public function testGetPurchasableItemAccessByRole(array $userRoles, int $expectedStatusCode): void
    {
        // Create user with specific roles
        $user = $this->createAndGetUser(
            firstName: 'Test',
            lastName: 'User',
            roles: $userRoles,
            email: '<EMAIL>'
        );
        $this->usersIds[] = $user->getId();

        // Create a purchasable item for testing
        $purchasableItem = $this->setAndGetPurchasableItemInRepository(
            name: 'Role Test Course',
            description: 'Course for role testing',
            priceAmount: 1000,
            priceCurrency: 'EUR',
            isActive: true
        );

        // Get token for the user
        $userToken = $this->loginAndGetTokenForUser($user);

        // Make request
        $response = $this->makeRequest(
            method: 'GET',
            uri: PurchasableItemEndpoints::getPurchasableItemEndpoint($purchasableItem->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        if (Response::HTTP_OK === $expectedStatusCode) {
            $decodedResponse = json_decode($response->getContent(), true);
            $this->assertArrayHasKey('data', $decodedResponse);
            $this->assertEquals($purchasableItem->getId()->value(), $decodedResponse['data']['id']);
        } elseif (Response::HTTP_FORBIDDEN === $expectedStatusCode) {
            $responseData = json_decode($response->getContent(), true);
            $this->assertArrayHasKey('message', $responseData);
        }
    }

    public static function roleAccessProvider(): \Generator
    {
        yield 'ROLE_SUPER_ADMIN has access' => [
            ['ROLE_SUPER_ADMIN'],
            Response::HTTP_OK,
        ];

        yield 'ROLE_ADMIN has access' => [
            ['ROLE_ADMIN'],
            Response::HTTP_OK,
        ];

        yield 'ROLE_MANAGER is forbidden' => [
            ['ROLE_MANAGER'],
            Response::HTTP_FORBIDDEN,
        ];

        yield 'ROLE_CREATOR is forbidden' => [
            ['ROLE_CREATOR'],
            Response::HTTP_FORBIDDEN,
        ];

        yield 'ROLE_TUTOR is forbidden' => [
            ['ROLE_TUTOR'],
            Response::HTTP_FORBIDDEN,
        ];

        yield 'ROLE_USER is forbidden' => [
            ['ROLE_USER'],
            Response::HTTP_FORBIDDEN,
        ];

        yield 'Multiple roles with ROLE_ADMIN has access' => [
            ['ROLE_USER', 'ROLE_ADMIN', 'ROLE_MANAGER'],
            Response::HTTP_OK,
        ];

        yield 'Multiple roles without admin access is forbidden' => [
            ['ROLE_USER', 'ROLE_MANAGER', 'ROLE_CREATOR'],
            Response::HTTP_FORBIDDEN,
        ];
    }

    public static function invalidUuidProvider(): \Generator
    {
        yield 'empty string' => ['', Response::HTTP_MOVED_PERMANENTLY]; // Redirect to /purchasable-items
        yield 'invalid format' => ['invalid-uuid', Response::HTTP_BAD_REQUEST];
        yield 'too short' => ['123', Response::HTTP_BAD_REQUEST];
        yield 'too long' => ['12345678-1234-1234-1234-123456789012345', Response::HTTP_BAD_REQUEST];
        yield 'invalid characters' => ['xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx', Response::HTTP_BAD_REQUEST];
        yield 'missing hyphens' => ['12345678123412341234123456789012', Response::HTTP_BAD_REQUEST];
    }
}
