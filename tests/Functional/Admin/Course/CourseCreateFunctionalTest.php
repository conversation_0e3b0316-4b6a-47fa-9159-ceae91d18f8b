<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Survey;
use App\Entity\SurveyCourse;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class CourseCreateFunctionalTest extends FunctionalTestCase
{
    use CourseHelperTrait;

    private ?User $testUser = null;
    protected const string TEST_EMAIL = '<EMAIL>';

    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser = $this->createAndGetUser(
            firstName: 'test',
            lastName: 'test',
            roles: [User::ROLE_SUPER_ADMIN],
            email: self::TEST_EMAIL,
        );
    }

    #[DataProvider('createCourseCreatorProvider')]
    public function testIsCreatorCourseCreate(string $role, int $expectedStatusCode): void
    {
        $typeCourse = $this->getTypeCourse();
        $em = $this->getEntityManager();
        $em->refresh($this->testUser);
        $this->testUser->setRoles([$role]);
        $userToken = $this->loginAndGetToken(
            email: self::TEST_EMAIL,
        );
        $courseData = $this->getBasicCourseData();

        $request = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminCourseEndpoint(),
            queryParams: $courseData,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $request->getStatusCode());
    }

    public function testCreateCourseValidatorFail(): void
    {
        $userToken = $this->loginAndGetToken();
        $basicData = $this->getBasicCourseData();
        // Remove 'locale' field to simulate missing required field
        $basicDataWithoutLocale = array_splice($basicData, 1);
        $violation = [
            '[locale]' => 'This field is missing.',
        ];

        // Test with missing required fields
        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminCourseEndpoint(),
            queryParams: $basicDataWithoutLocale,
            bearerToken: $userToken,
        );
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $metadata = $content['metadata'];
        $this->assertArrayHasKey('violations', $metadata);
        $this->assertEquals($violation, $metadata['violations']);
    }

    /**
     * Test: Course is created with correct default values.
     *
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testCourseCreatedWithDefaultValues(): void
    {
        $courseData = $this->getBasicCourseData();
        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminCourseEndpoint(),
            queryParams: $courseData,
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);

        $courseId = $responseData['courseData']['id'];

        // Verify course was created with default values
        $em = $this->getEntityManager();
        $course = $em->find(Course::class, $courseId);

        $this->assertNotNull($course);
        $this->assertFalse($course->getIsNew(), 'Course should have isNew = false by default');

        // Verify initial season was created
        $seasons = $course->getSeasons();
        $this->assertCount(1, $seasons, 'Course should have one initial season');

        $initialSeason = $seasons->first();
        $this->assertEquals('Init', $initialSeason->getName());
        $this->assertEquals(1, $initialSeason->getSort());
    }

    /**
     * Test: Default survey is automatically assigned to course.
     *
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testDefaultSurveyIsAutomaticallyAssigned(): void
    {
        $courseData = $this->getBasicCourseData();
        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminCourseEndpoint(),
            queryParams: $courseData,
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);

        $courseId = $responseData['courseData']['id'];

        // Verify a survey was automatically assigned to course
        $em = $this->getEntityManager();
        $surveyCourse = $em->getRepository(SurveyCourse::class)->findOneBy(['course' => $courseId]);
        $defaultSurvey = $em->getRepository(Survey::class)->findOneBy(['isMain' => true]);

        $this->assertNotNull($surveyCourse, 'A survey should be automatically assigned to course');
        $this->assertNotNull($surveyCourse->getSurvey(), 'Survey should exist');
        $this->assertEquals($defaultSurvey->getId(), $surveyCourse->getSurvey()->getId());
    }

    public static function createCourseCreatorProvider(): \Generator
    {
        yield 'Super Admin Role Course Creation' => [
            'role' => User::ROLE_SUPER_ADMIN,
            'expectedStatusCode' => 201,
        ];
        yield 'Admin Role Course Creation' => [
            'role' => User::ROLE_ADMIN,
            'expectedStatusCode' => 201,
        ];
        yield 'Creator Role Course Creation' => [
            'role' => User::ROLE_CREATOR,
            'expectedStatusCode' => 201,
        ];
        yield 'Tutor Role Course Creation' => [
            'role' => User::ROLE_TUTOR,
            'expectedStatusCode' => 403,
        ];
        yield 'Manager Role Course Creation' => [
            'role' => User::ROLE_MANAGER,
            'expectedStatusCode' => 403,
        ];
        yield 'User Role Course Creation' => [
            'role' => User::ROLE_USER,
            'expectedStatusCode' => 302,
        ];
    }

    /**
     * Get basic course data for testing.
     */
    private function getBasicCourseData(): array
    {
        $courseCategory = $this->createAndGetCourseCategory();
        $typeCourse = $this->getTypeCourse();

        return [
            'locale' => 'ES',
            'code' => '123456',
            'name' => 'Curso de prueba',
            'duration' => 120,
            'typeCourse' => $typeCourse->getId(),
            'category' => $courseCategory->getId(),
        ];
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            UserLogin::class,
            Course::class,
            CourseCategory::class,
        ]);
        // Hard delete user created for the test
        if (!empty($this->testUser)) {
            $this->hardDeleteUsersByIds([
                $this->testUser->getId(),
            ]);
        }
        parent::tearDown();
    }
}
