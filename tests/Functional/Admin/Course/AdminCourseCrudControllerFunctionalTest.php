<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class AdminCourseCrudControllerFunctionalTest extends FunctionalTestCase
{
    private array $usersIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testGetCourseData()
    {
        $this->createAndGetCourse();

        $response = $this->getCoursePreDataResponse();
        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('id', $responseData);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertArrayHasKey('name', $responseData);
        $this->assertArrayHasKey('description', $responseData);
        $this->assertArrayHasKey('image', $responseData);
        $this->assertArrayHasKey('open', $responseData);
        $this->assertArrayHasKey('locale', $responseData);
        $this->assertArrayHasKey('points', $responseData);
        $this->assertArrayHasKey('category', $responseData);
        $this->assertArrayHasKey('generalInformation', $responseData);
        $this->assertArrayHasKey('active', $responseData);
        $this->assertArrayHasKey('level', $responseData);
        $this->assertArrayHasKey('documentation', $responseData);
        $this->assertArrayHasKey('tags', $responseData);
        $this->assertArrayHasKey('open_visible', $responseData);
        $this->assertArrayHasKey('courseSegments', $responseData);
        $this->assertArrayHasKey('isNew', $responseData);
        $this->assertArrayHasKey('typeCourse', $responseData);
        $this->assertArrayHasKey('typeDiploma', $responseData);
        $this->assertArrayHasKey('is_content_diploma', $responseData);
        $this->assertArrayHasKey('type_index_diploma', $responseData);
        $this->assertArrayHasKey('description_content_diploma', $responseData);
        $this->assertArrayHasKey('duration', $responseData);

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[DataProvider('getCourseDataProvider')]
    public function testGetCourseDataWithDuration(?int $duration, bool $showDuration)
    {
        $this->createAndGetCourse(
            duration: $duration,
            showDuration: $showDuration
        );

        $response = $this->getCoursePreDataResponse();
        $responseData = $this->extractResponseData($response);

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals($duration, $responseData['duration']);
        $this->assertEquals($showDuration, $responseData['diploma_config']['show_duration']);

        if ($duration) {
            $this->assertIsInt($responseData['duration']);
        }
    }

    public static function getCourseDataProvider(): \Generator
    {
        yield 'Course with 1 minute duration and true show duration' => [
            'duration' => 1,
            'showDuration' => true,
        ];
        yield 'Course with no duration and false show duration' => [
            'duration' => null,
            'showDuration' => false,
        ];
    }

    /**
     * @throws NotSupported
     */
    #[DataProvider('postCreateCourseDataProvider')]
    public function testCreateCourse(bool $hasDuration, $duration, int $expectedStatusCode)
    {
        $courseData = $this->getCourseData(hasDuration: $hasDuration, duration: $duration, create: true);
        $userToken = $this->loginAndGetToken();
        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminCourseEndpoint(),
            queryParams: $courseData,
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        if (201 === $expectedStatusCode) {
            $course = $this->getEntityManager()->getRepository(Course::class)->findOneBy(['name' => 'Test Course']);
            $this->assertEquals($duration, $course->getDuration());
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('postUpdateCourseDataProvider')]
    public function testUpdateCourse(bool $hasDuration, $duration, $diplomaShowDuration, int $expectedStatusCode)
    {
        $course = $this->createAndGetCourse(duration: 10, showDuration: !$diplomaShowDuration);
        $courseData = $this->getCourseData(hasDuration: $hasDuration, duration: $duration, create: false, diplomaShowDuration: $diplomaShowDuration);
        $userToken = $this->loginAndGetToken();
        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint($course->getId()),
            queryParams: $courseData,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        if (201 === $expectedStatusCode) {
            $course = $this->getEntityManager()->getRepository(Course::class)->find($course->getId());
            $this->assertEquals($duration, $course->getDuration());
            $this->assertEquals($diplomaShowDuration, $course->getDiplomaConfig()->showDuration());
        }
    }

    public static function postCreateCourseDataProvider(): \Generator
    {
        yield 'Course with 1 minute duration' => [
            'hasDuration' => true,
            'duration' => 1,
            'expectedStatusCode' => 201,
        ];
        yield 'Course with null duration' => [
            'hasDuration' => true,
            'duration' => null,
            'expectedStatusCode' => 201,
        ];
        yield 'Course with a string as duration' => [
            'hasDuration' => true,
            'duration' => 'A',
            'expectedStatusCode' => 400,
        ];
        yield 'Course with no duration field' => [
            'hasDuration' => false,
            'duration' => null,
            'expectedStatusCode' => 400,
        ];
    }

    public static function postUpdateCourseDataProvider(): \Generator
    {
        yield 'Course with 1 minute duration and true show duration ' => [
            'hasDuration' => true,
            'duration' => 1,
            'diplomaShowDuration' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Course with 1 minute duration and false show duration' => [
            'hasDuration' => true,
            'duration' => 1,
            'diplomaShowDuration' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Course with null duration and false show duration' => [
            'hasDuration' => true,
            'duration' => null,
            'diplomaShowDuration' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Course with a string as duration and show duration' => [
            'hasDuration' => true,
            'duration' => 'A',
            'diplomaShowDuration' => true,
            'expectedStatusCode' => 400,
        ];
        yield 'Course with no duration field' => [
            'hasDuration' => false,
            'duration' => null,
            'diplomaShowDuration' => false,
            'expectedStatusCode' => 400,
        ];
        yield 'Course with 1 minute duration and no show duration field' => [
            'hasDuration' => true,
            'duration' => 1,
            'diplomaShowDuration' => null,
            'expectedStatusCode' => 400,
        ];
        yield 'Course with 1 minute duration and no boolean show duration' => [
            'hasDuration' => true,
            'duration' => 1,
            'diplomaShowDuration' => 'A',
            'expectedStatusCode' => 400,
        ];
        yield 'Course with no duration and true show duration' => [
            'hasDuration' => true,
            'duration' => null,
            'diplomaShowDuration' => true,
            'expectedStatusCode' => 422,
        ];
    }

    private function getCoursePreDataResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminCourseEndpoints::adminGetCourseEndpoint(1),
            bearerToken: $userToken
        );
    }

    private function getCourseData(
        bool $hasDuration,
        $duration,
        bool $create,
        $diplomaShowDuration = null
    ): array {
        $data = $create ? [
            'name' => 'Test Course',
            'code' => 'TEST-COURSE',
            'typeCourse' => 1,
            'category' => 1,
            'locale' => 'en',
        ] : [
            'name' => 'Test Course',
            'code' => 'TEST-COURSE',
            'typeCourse' => 1,
            'category' => 1,
            'active' => 'true',
            'isNew' => 'true',
            'open' => 'true',
            'diploma_select' => 'DEFAULT',
            'description' => 'Test course description',
            'generalInformation' => 'Test course general information',
            'open_visible' => 'true',
            'open_new' => 'true',
            'diploma_index' => 'DEFAULT',
            'filters' => '[]',
            'locale' => 'en',
            'tags' => '[]',
            'managers' => '[]',
        ];

        if ($hasDuration) {
            $data['duration'] = json_encode($duration);
        }

        if (null !== $diplomaShowDuration) {
            $data['diploma_show_duration'] = json_encode($diplomaShowDuration);
        }

        return $data;
    }

    public function testCreateCourseNoUserRedirectToLogin(): void
    {
        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminCourseEndpoint(),
        );

        $this->assertEquals(Response::HTTP_FOUND, $response->getStatusCode());
    }

    public function testCreateCourseAccessDeniedWhenUserNotCreator()
    {
        $user = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER]
        );
        $this->usersIds = [$user->getId()];
        $this->assertEquals(false, $user->isCreator());
        $userToken = $this->loginAndGetToken(email: $user->getEmail());
        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminCourseEndpoint(),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([Course::class]);
        $this->hardDeleteUsersByIds($this->usersIds);
        parent::tearDown();
    }
}
