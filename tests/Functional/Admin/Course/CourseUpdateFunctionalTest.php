<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\HelperTrait\FilterHelperTrait;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Filter\FilterCollection;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\TransactionRequiredException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class CourseUpdateFunctionalTest extends FunctionalTestCase
{
    use CourseCreatorFixtureTrait;
    use FilterHelperTrait;

    private ?User $user = null;
    private ?User $otherUser = null;
    private ?Course $testCourse = null;

    private ?Filter $filter1 = null;
    private ?Filter $filter2 = null;
    private ?Filter $filter3 = null;
    private ?FilterCategory $filterCategory1 = null;
    private ?FilterCategory $filterCategory2 = null;
    private ?FilterCategory $filterCategory3 = null;

    private const int DURATION_COURSE = 60;

    private const int DEFAULT_POINTS_COURSE = 500;

    private const array COURSE_DATA_UPDATE_BY_CREATOR = [
        'name' => 'Updated Course Name',
        'description' => 'Updated Description',
        'locale' => 'en',
        'code' => 'UPDATED-CODE',
        'duration' => self::DURATION_COURSE,
        'category' => 1,
        'typeCourse' => 1,
        'general-information' => 'Updated General Information',
        'thumbnail' => 'online.jpg',
        'tags' => [],
    ];

    private const array COURSE_DATA_UPDATE_BY_MANAGER = [
        'active' => 'false',
        'open' => 'true',
        'open-visible' => 'true',
        'isNew' => 'true',
        'is-content-diploma' => 'true',
        'points' => self::DEFAULT_POINTS_COURSE,
        'diploma_show_duration' => 'false',
        'diploma-index' => 'MANUAL',
        'description-content-diploma' => 'Custom diploma description',
        'filters' => [],
        'managers' => [],
    ];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->createAndGetUser(
            firstName: 'test',
            lastName: 'test',
            roles: [User::ROLE_SUPER_ADMIN],
            email: '<EMAIL>',
        );

        $this->otherUser = $this->createAndGetUser(
            firstName: 'other',
            lastName: 'user',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>',
        );

        $this->testCourse = $this->createAndGetCourse(
            duration: self::DURATION_COURSE,
            createdBy: $this->user,
        );

        $this->filterCategory1 = $this->createAndGetFilterCategory(name: 'Filter Category 1');
        $this->filterCategory2 = $this->createAndGetFilterCategory(name: 'Filter Category 2');
        $this->filterCategory3 = $this->createAndGetFilterCategory(name: 'Filter Category 3');

        // Create filters
        $this->filter1 = $this->createAndGetFilter(name: 'Filter 1', category: $this->filterCategory1);
        $this->filter2 = $this->createAndGetFilter(name: 'Filter 2', category: $this->filterCategory2);
        $this->filter3 = $this->createAndGetFilter(name: 'Filter 3', category: $this->filterCategory3);
    }

    /**
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     * @throws ORMException
     */
    #[DataProvider('providerRolesBasedUpdatePermissions')]
    public function testRolesBasedUpdatePermissions(array $roles, int $expectedStatusCode): void
    {
        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);

        $this->user->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint($this->testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerRolesBasedUpdatePermissions(): \Generator
    {
        yield 'Super Admin can update course' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'expectedStatusCode' => 200,
        ];
        yield 'Admin can update course' => [
            'roles' => [User::ROLE_ADMIN],
            'expectedStatusCode' => 200,
        ];
        yield 'Manager can update course' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedStatusCode' => 200,
        ];
        yield 'Creator can update own course' => [
            'roles' => [User::ROLE_CREATOR],
            'expectedStatusCode' => 200,
        ];
        yield 'User cannot update course' => [
            'roles' => [User::ROLE_USER],
            'expectedStatusCode' => 302, // Redirected to login
        ];
        yield 'Tutor cannot update course' => [
            'roles' => [User::ROLE_TUTOR],
            'expectedStatusCode' => 403,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     * @throws ORMException
     */
    #[DataProvider('providerValidationError')]
    public function testValidationErrors(array $queryParams, int $expectedStatusCode): void
    {
        $this->user->setRoles([User::ROLE_CREATOR]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint($this->testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerValidationError(): \Generator
    {
        yield 'Missing name field' => [
            'queryParams' => [
                'description' => 'Updated Description',
                'locale' => 'en',
                'code' => 'UPDATED-CODE',
                'duration' => '60',
                'category' => 1,
                'typeCourse' => 1,
            ],
            'expectedStatusCode' => 400,
        ];
        yield 'Missing code field' => [
            'queryParams' => [
                'name' => 'Updated Course Name',
                'description' => 'Updated Description',
                'locale' => 'en',
                'duration' => '60',
                'category' => 1,
                'typeCourse' => 1,
            ],
            'expectedStatusCode' => 400,
        ];
        yield 'Missing locale field' => [
            'queryParams' => [
                'name' => 'Updated Course Name',
                'description' => 'Updated Description',
                'code' => 'UPDATED-CODE',
                'duration' => '60',
                'category' => 1,
                'typeCourse' => 1,
            ],
            'expectedStatusCode' => 400,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     * @throws NotSupported
     */
    public function testAdminCanUpdateAllFields(): void
    {
        $this->user->setRoles([User::ROLE_ADMIN]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);

        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint($this->testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        // Refresh course from DB
        $course = $this->getEntityManager()->getRepository(Course::class)->find($this->testCourse->getId());

        $this->assertEquals('Updated Course Name', $course->getName());
        $this->assertEquals('Updated Description', $course->getDescription());
        $this->assertEquals('en', $course->getLocale());
        $this->assertEquals('UPDATED-CODE', $course->getCode());
        $this->assertEquals(self::DURATION_COURSE, $course->getDuration());
        $this->assertEquals('Updated General Information', $course->getGeneralInformation());
        $this->assertEquals('online.png', $course->getImage());
        $this->assertFalse($course->getActive());
        $this->assertTrue($course->getOpen());
        $this->assertTrue($course->getOpenVisible());
        $this->assertTrue($course->getIsNew());
        $this->assertTrue($course->isIsContentDiploma());
        $this->assertEquals(self::DEFAULT_POINTS_COURSE, $course->getPoints());
        $this->assertCount(0, $course->getFilters());
        $this->assertCount(0, $course->getManagers());
        $this->assertFalse($course->getDiplomaConfig()->showDuration());
        $this->assertEquals('MANUAL', $course->getTypeIndexDiploma());
        $this->assertEquals('Custom diploma description', $course->getDescriptionContentDiploma());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     */
    #[DataProvider('providerUserCreatorOnlyUpdateBasicFields')]
    public function testUserCreatorOnlyUpdateBasicFields(
        string $courseOwner,
        bool $shareCourseWithUser,
        int $expectedStatusCode
    ): void {
        // Create course with the specified owner
        $owner = 'user' === $courseOwner ? $this->user : $this->otherUser;
        $course = $this->createAndGetCourse(createdBy: $owner);

        if ($shareCourseWithUser && 'otherUser' === $courseOwner) {
            $this->setAndGetCourseCreatorInRepository(
                userId: new Id($this->user->getId()),
                courseId: new Id($course->getId()),
            );
        }

        $this->user->setRoles([User::ROLE_CREATOR]);
        $this->getEntityManager()->flush();

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);

        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint($course->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        if (200 === $expectedStatusCode) {
            $this->assertEquals($expectedStatusCode, $response->getStatusCode());

            // Refresh course from DB
            $course = $this->getEntityManager()->getRepository(Course::class)->find($course->getId());
            $this->assertEquals('Updated Course Name', $course->getName());
            $this->assertEquals('Updated Description', $course->getDescription());
            $this->assertEquals('UPDATED-CODE', $course->getCode());
            $this->assertEquals(self::DURATION_COURSE, $course->getDuration());
            $this->assertEquals('Updated General Information', $course->getGeneralInformation());
            $this->assertEquals('en', $course->getLocale());
            $this->assertEquals('online.png', $course->getImage());
        } else {
            $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        }
    }

    public static function providerUserCreatorOnlyUpdateBasicFields(): \Generator
    {
        yield 'User creator can update basic fields on own course' => [
            'courseOwner' => 'user',
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'User creator can update basic fields on other user course (shared)' => [
            'courseOwner' => 'otherUser',
            'shareCourseWithUser' => true,
            'expectedStatusCode' => 200,
        ];

        yield 'User creator cannot update basic fields on other user course (not shared)' => [
            'courseOwner' => 'otherUser',
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 403,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     */
    #[DataProvider('providerManagerCanOnlyUpdatePermittedFields')]
    public function testManagerCanOnlyUpdatePermittedFields(
        bool $managerHasSharedCourses,
        bool $managerHasSharedThisCourse,
        int $expectedStatusCode
    ): void {
        $course = $this->createAndGetCourse(createdBy: $this->otherUser);

        if ($managerHasSharedCourses) {
            $otherCourse = $this->createAndGetCourse(createdBy: $this->otherUser);
            $otherCourse->addManager($this->user);
            $this->getEntityManager()->flush();
            $this->getEntityManager()->refresh($otherCourse);
        }

        if ($managerHasSharedThisCourse) {
            $course->addManager($this->user);
            $this->getEntityManager()->flush();
            $this->getEntityManager()->refresh($course);
        }

        $this->user->setRoles([User::ROLE_MANAGER]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);

        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint($course->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        if (200 === $expectedStatusCode) {
            // Refresh course from DB
            $courseUpdated = $this->getEntityManager()->getRepository(Course::class)->find($course->getId());

            // Basic fields should NOT be updated by manager (should keep original values)
            $this->assertEquals($course->getName(), $courseUpdated->getName());
            $this->assertEquals($course->getCode(), $courseUpdated->getCode());
            $this->assertEquals($course->getDuration(), $courseUpdated->getDuration());
            $this->assertEquals($course->getDescription(), $courseUpdated->getDescription());
            $this->assertEquals($course->getGeneralInformation(), $courseUpdated->getGeneralInformation());
            $this->assertEquals($course->getLocale(), $courseUpdated->getLocale());
            $this->assertEquals($course->getImage(), $courseUpdated->getImage());
            $this->assertEquals($course->getCategory()->getId(), $courseUpdated->getCategory()->getId());
            $this->assertEquals($course->getTypeCourse()->getId(), $courseUpdated->getTypeCourse()->getId());

            // Management fields SHOULD be updated by manager
            $this->assertFalse($courseUpdated->getActive());
            $this->assertTrue($courseUpdated->getOpen());
            $this->assertTrue($courseUpdated->getOpenVisible());
            $this->assertTrue($courseUpdated->getIsNew());
            $this->assertTrue($courseUpdated->isIsContentDiploma());
            $this->assertEquals(self::DEFAULT_POINTS_COURSE, $courseUpdated->getPoints());
            $this->assertCount(0, $courseUpdated->getFilters());
            if ($managerHasSharedThisCourse) {
                $this->assertCount(1, $courseUpdated->getManagers());
            } else {
                $this->assertCount(0, $courseUpdated->getManagers());
            }
            $this->assertFalse($courseUpdated->getDiplomaConfig()->showDuration());
            $this->assertEquals('MANUAL', $courseUpdated->getTypeIndexDiploma());
            $this->assertEquals('Custom diploma description', $courseUpdated->getDescriptionContentDiploma());
        } else {
            $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        }
    }

    public static function providerManagerCanOnlyUpdatePermittedFields(): \Generator
    {
        yield 'Manager without shared courses' => [
            'managerHasSharedCourses' => false,
            'managerHasSharedThisCourse' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Manager with course shared with him' => [
            'managerHasSharedCourses' => false,
            'managerHasSharedThisCourse' => true,
            'expectedStatusCode' => 200,
        ];

        yield 'Manager with shared courses but not this one' => [
            'managerHasSharedCourses' => true,
            'managerHasSharedThisCourse' => false,
            'expectedStatusCode' => 403,
        ];
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    public function testManagerCanNotUpdateFiltersIfOneOrMoreAreNotAllow(): void
    {
        $this->user->setRoles([User::ROLE_MANAGER]);
        $this->user->addManagerFilter($this->filter1);
        $this->user->addManagerFilter($this->filter2);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);
        $queryParams['filters'] = [$this->filter3->getId(), $this->filter2->getId()];

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminUpdateCourseEndpoint($this->testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(403, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(
            'You can not modify the filters.',
            $content['message']
        );

        // Refresh course from DB
        $course = $this->getEntityManager()->getRepository(Course::class)->find($this->testCourse->getId());
        $this->assertCount(0, $course->getFilters());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     * @throws CollectionException
     */
    public function testManagerCannotRemoveCourseFilterWithoutPermission(): void
    {
        $testCourse = $this->createAndGetCourse(createdBy: $this->user, courseFilters: new FilterCollection(
            [$this->filter3]
        ));

        $this->user->setRoles([User::ROLE_MANAGER]);
        $this->user->addManagerFilter($this->filter1);
        $this->user->addManagerFilter($this->filter2);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);
        // A filter is removed when it is not sent in the request.
        $queryParams['filters'] = [$this->filter2->getId()];

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminUpdateCourseEndpoint($testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(403, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(
            'You can not modify the filters.',
            $content['message']
        );
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    public function testManagerCanUpdateAllowFilters(): void
    {
        $this->user->setRoles([User::ROLE_MANAGER]);
        $this->user->addManagerFilter($this->filter1);
        $this->user->addManagerFilter($this->filter2);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);
        $queryParams['filters'] = [$this->filter2->getId(), $this->filter1->getId()];

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminUpdateCourseEndpoint($this->testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        // Refresh course from DB
        $course = $this->getEntityManager()->getRepository(Course::class)->find($this->testCourse->getId());
        $this->assertCount(2, $course->getFilters());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    public function testAdminUpdatingFilters(): void
    {
        $this->user->setRoles([User::ROLE_ADMIN]);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);
        $queryParams['filters'] = [$this->filter3->getId(), $this->filter2->getId(), $this->filter1->getId()];

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminUpdateCourseEndpoint($this->testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        // Refresh course from DB
        $course = $this->getEntityManager()->getRepository(Course::class)->find($this->testCourse->getId());
        $this->assertCount(3, $course->getFilters());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    public function testAdminAndManagerUpdatingFilters(): void
    {
        $this->user->setRoles([User::ROLE_ADMIN, User::ROLE_MANAGER]);
        $this->user->addManagerFilter($this->filter1);
        $this->user->addManagerFilter($this->filter3);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);
        $queryParams['filters'] = [$this->filter3->getId(), $this->filter2->getId(), $this->filter1->getId()];

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminUpdateCourseEndpoint($this->testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        // Refresh course from DB
        $course = $this->getEntityManager()->getRepository(Course::class)->find($this->testCourse->getId());
        $this->assertCount(3, $course->getFilters());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    public function testAddFiltersAsManagerMaintainingOldFilters(): void
    {
        $testCourse = $this->createAndGetCourse(createdBy: $this->otherUser, courseFilters: new FilterCollection(
            [$this->filter3]
        ));
        // Sending as Manager and testing all filters
        $this->user->setRoles([User::ROLE_MANAGER]);
        $this->user->addManagerFilter($this->filter1);
        $this->user->addManagerFilter($this->filter2);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);
        $queryParams['filters'] = [$this->filter3->getId(), $this->filter2->getId()];

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminUpdateCourseEndpoint($testCourse->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        // Refresh course from DB
        $course = $this->getEntityManager()->getRepository(Course::class)->find($testCourse->getId());
        $this->assertCount(2, $course->getFilters());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     * @throws NotSupported
     */
    #[DataProvider('providerCourseIncludeTrainingTimeInDiploma')]
    public function testCourseIncludeTrainingTimeInDiploma(
        ?int $duration,
        bool $diplomaShowDuration,
        int $expectedStatusCode
    ): void {
        $course = $this->createAndGetCourse(duration: 10);
        $this->user->setRoles([User::ROLE_ADMIN]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);
        $queryParams['duration'] = null === $duration ? 'null' : $duration;
        $queryParams['diploma_show_duration'] = $diplomaShowDuration ? 'true' : 'false';

        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint($course->getId()),
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        if (200 === $expectedStatusCode) {
            // Refresh course from DB
            $courseUpdated = $this->getEntityManager()->getRepository(Course::class)->find($course->getId());
            $this->assertEquals($duration, $courseUpdated->getDuration());
            $this->assertEquals($diplomaShowDuration, $courseUpdated->getDiplomaConfig()->showDuration());
        }
    }

    public static function providerCourseIncludeTrainingTimeInDiploma(): \Generator
    {
        yield 'Course with duration and show duration' => [
            'duration' => 1,
            'diplomaShowDuration' => true,
            'expectedStatusCode' => 200,
        ];

        yield 'Course with duration and do not show duration' => [
            'duration' => 10,
            'diplomaShowDuration' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Course with no duration and show duration' => [
            'duration' => null,
            'diplomaShowDuration' => true,
            'expectedStatusCode' => 422,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     * @throws ORMException
     */
    public function testUpdateNonExistentCourse(): void
    {
        $queryParams = array_merge(self::COURSE_DATA_UPDATE_BY_CREATOR, self::COURSE_DATA_UPDATE_BY_MANAGER);

        $this->user->setRoles([User::ROLE_ADMIN]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->user);

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeAdminApiRequest(
            'POST',
            AdminCourseEndpoints::adminUpdateCourseEndpoint(99999), // Non-existent course ID
            queryParams: $queryParams,
            bearerToken: $userToken
        );

        $this->assertEquals(500, $response->getStatusCode());
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    public function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            'course_filter',
            'manager_filter',
            'course_creator',
            'course_manager',
            Filter::class,
            FilterCategory::class,
        ]);

        $userIds = array_filter([
            $this->user?->getId(),
            $this->otherUser?->getId(),
        ]);

        if (!empty($userIds)) {
            $this->hardDeleteUsersByIds($userIds);
        }

        parent::tearDown();
    }
}
