<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Export;
use App\Entity\Task;
use App\Entity\User;
use App\Tests\Mother\Entity\ExportMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait ExportHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetExport(
        ?string $filename = null,
        ?array $meta = null,
        ?\DateTimeInterface $finishedAt = null,
        ?string $type = null,
        ?User $createdBy = null,
        ?User $updatedBy = null,
        ?User $deletedBy = null,
        ?Task $task = null,
        ?string $taskName = null,
        ?\DateTimeInterface $availableUntil = null,
    ): Export {
        $em = $this->getEntityManager();

        $export = ExportMother::create(
            createdBy: $createdBy,
            updatedBy: $updatedBy,
            deletedBy: $deletedBy,
            task: $task,
            type: $type,
            meta: $meta,
            filename: $filename,
            finishedAt: $finishedAt,
            availableUntil: $availableUntil,
        );

        $em->persist($export);
        $em->flush();

        return $export;
    }
}
