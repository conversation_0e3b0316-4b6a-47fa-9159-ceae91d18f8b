<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Frontend;

class FrontendCourseEndpoints
{
    public static function courseEndpoint(int $courseId, ?bool $new = null, ?int $announcementId = null): string
    {
        $queryParams = [];
        $url = '/courses/' . $courseId;

        if (null !== $new) {
            $queryParams['new'] = $new ? 'true' : 'false';
        }

        if (null !== $announcementId) {
            $queryParams['idAnnouncement'] = $announcementId;
        }

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    public static function userCourseChapterUpdateEndpoint(int $userCourseChapterId): string
    {
        return \sprintf('/api/chapter/%d/update', $userCourseChapterId);
    }

    public static function courseOpinionEndpoint(int $courseId, ?int $announcementId = null, ?int $page = null, ?int $pageSize = null): string
    {
        $url = "/course/{$courseId}/opinions";

        $queryParams = [];

        if (null !== $announcementId) {
            $queryParams['announcementId'] = $announcementId;
        }

        if (null !== $page) {
            $queryParams['page'] = $page;
        }

        if (null !== $pageSize) {
            $queryParams['pageSize'] = $pageSize;
        }

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }
}
