<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class PurchasableItemEndpoints
{
    public static function getPurchasableItemsEndpoint(
        ?int $page = null,
        ?int $pageSize = null,
        ?string $sortBy = null,
        ?string $sortDir = null,
        ?string $search = null,
        ?string $type = null,
        ?int $priceMin = null,
        ?int $priceMax = null,
        ?bool $isActive = null
    ): string {
        $queryParams = [];

        if (null !== $page) {
            $queryParams['page'] = $page;
        }

        if (null !== $pageSize) {
            $queryParams['page_size'] = $pageSize;
        }

        if (null !== $sortBy) {
            $queryParams['sort_by'] = $sortBy;
        }

        if (null !== $sortDir) {
            $queryParams['sort_dir'] = $sortDir;
        }

        if (null !== $search) {
            $queryParams['search'] = $search;
        }

        if (null !== $type) {
            $queryParams['type'] = $type;
        }

        if (null !== $priceMin) {
            $queryParams['price_min'] = $priceMin;
        }

        if (null !== $priceMax) {
            $queryParams['price_max'] = $priceMax;
        }

        if (null !== $isActive) {
            $queryParams['is_active'] = $isActive ? 'true' : 'false';
        }

        $url = '/api/v2/admin/purchasable-items';

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    public static function getPurchasableItemEndpoint(string $purchasableItemId): string
    {
        return '/api/v2/admin/purchasable-items/' . $purchasableItemId;
    }

    public static function putPurchasableItemEndpoint(): string
    {
        return '/api/v2/admin/purchasable-items';
    }

    public static function patchPurchasableItemEndpoint(string $purchasableItemId): string
    {
        return '/api/v2/admin/purchasable-items/' . $purchasableItemId;
    }

    public static function deletePurchasableItemEndpoint(string $purchasableItemId): string
    {
        return '/api/v2/admin/purchasable-items/' . $purchasableItemId;
    }
}
