<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

trait PurchasableItemHelperTrait
{
    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws InvalidCurrencyCodeException
     */
    protected function createAndGetPurchasableItem(
        ?string $name = null,
        ?string $description = null,
        ?int $priceAmount = null,
        ?Currency $priceCurrency = null,
        ?ResourceType $resourceType = null,
        ?Identifier $resourceId = null,
        ?bool $isActive = true,
    ): PurchasableItem {
        $item = PurchasableItemMother::create(
            name: $name,
            description: $description,
            price: MoneyMother::create(
                amount: $priceAmount ?? 1000,
                currency: $priceCurrency ?? Currency::EUR()
            ),
            resource: ResourceMother::create(
                type: $resourceType ?? ResourceType::Course,
                id: $resourceId ?? new Id(1)
            ),
            isActive: $isActive,
        );

        $this->client->getContainer()->get('App\V2\Domain\Purchase\PurchasableItemRepository')
            ->put($item);

        return $item;
    }
}
