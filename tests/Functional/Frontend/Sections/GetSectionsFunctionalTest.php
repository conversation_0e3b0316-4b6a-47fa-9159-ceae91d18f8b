<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\Sections;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\Setting;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Modules\CourseSection\Enum\SectionSubtypeEnum;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendSectionEndpoints;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Filter\FilterCollection;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetSectionsFunctionalTest extends FunctionalTestCase
{
    public const string APP_OPEN_COURSE_SETTING = 'app.openCourse';
    public const string APP_USER_USE_FILTERS_SETTING = 'app.user.useFilters';

    private ?User $user = null;
    private string $userToken;

    /**
     * @throws ORMException
     * @throws NotSupported
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->userToken = $this->loginAndGetToken();

        $openCourseSetting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => self::APP_OPEN_COURSE_SETTING]);

        $this->originalsSettings[self::APP_OPEN_COURSE_SETTING] = $openCourseSetting?->getValue();

        $userUseFiltersSetting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => self::APP_USER_USE_FILTERS_SETTING]);

        $this->originalsSettings[self::APP_USER_USE_FILTERS_SETTING] = $userUseFiltersSetting?->getValue();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function createSampleData(): void
    {
        $this->createAndGetCourse(
            name: 'Course 1',
            code: 'course-1',
            open: true,
            openVisible: true,
        );

        $this->createAndGetCourseSection();
    }

    /**
     * @throws Exception
     */
    public function testEmptySectionWhenNoData(): void
    {
        $this->truncateEntities([CourseSection::class]);

        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendSectionEndpoints::sectionsEndpoint(),
            [],
            [],
            [],
            $this->userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('sections', $responseData);
        $this->assertEmpty($responseData['sections']);
        $this->assertArrayHasKey('training', $responseData);
        $this->assertEmpty($responseData['training']);
    }

    private function getSectionsResponse(): array
    {
        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendSectionEndpoints::sectionsEndpoint(),
            [],
            [],
            [],
            $this->userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        return $this->extractResponseData($response);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testContainCorrectStructure(): void
    {
        $this->createSampleData();
        // This is done because otherwise, the current user's language remains cached in another language. This might need to be reviewed.
        $this->setLocaleForUser(self::DEFAULT_USER_LOCALE);

        $responseData = $this->getSectionsResponse();

        $this->assertArrayHasKey('sections', $responseData);
        $this->assertArrayHasKey('training', $responseData);
        $this->assertIsArray($responseData['sections']);
        $this->assertIsArray($responseData['training']);

        $baseSection = $responseData['sections'][0];
        $this->assertArrayHasKey('name', $baseSection);
        $this->assertEquals('Mi Formación', $baseSection['name']);
        $this->assertArrayHasKey('slug', $baseSection);
        $this->assertEquals('mi-formacion', $baseSection['slug']);
        $this->assertArrayHasKey('sort', $baseSection);
        $this->assertEquals(1, $baseSection['sort']);
        $this->assertArrayHasKey('hideCategoryName', $baseSection);
        $this->assertArrayHasKey('isMain', $baseSection);
        $this->assertArrayHasKey('openCampus', $baseSection);
        $this->assertArrayHasKey('isItineraries', $baseSection);
        $this->assertArrayHasKey('isAnnouncements', $baseSection);
        $this->assertArrayHasKey('categories', $baseSection);
        $this->assertIsArray($baseSection['categories']);

        $this->assertIsBool($baseSection['isMain']);
        $this->assertIsBool($baseSection['openCampus']);
        $this->assertIsBool($baseSection['isItineraries']);
        $this->assertIsBool($baseSection['isAnnouncements']);
        $this->assertIsBool($baseSection['hideCategoryName']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testCheckStructureOfSectionCategories(): void
    {
        $this->createSampleData();

        $responseData = $this->getSectionsResponse();

        $this->assertArrayHasKey('sections', $responseData);
        $this->assertNotEmpty($responseData['sections'], 'No sections found in response.');

        foreach ($responseData['sections'] as $section) {
            $categories = $section['categories'];

            $this->assertIsArray($categories);

            if (empty($categories)) {
                continue;
            }

            foreach ($categories as $category) {
                $this->validateCategoryStructure($category);
            }
        }
    }

    private function validateCategoryStructure(array $category): void
    {
        $this->assertArrayHasKey('id', $category);
        $this->assertArrayHasKey('name', $category);
        $this->assertArrayHasKey('slug', $category);
        $this->assertArrayHasKey('sort', $category);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testHasAtMostOneMainSection(): void
    {
        $this->createSampleData();

        $responseData = $this->getSectionsResponse();
        $sections = $responseData['sections'] ?? [];

        $mainSectionCount = 0;

        foreach ($sections as $section) {
            if (isset($section['isMain']) && true === $section['isMain']) {
                ++$mainSectionCount;
            }
        }

        $this->assertLessThanOrEqual(
            1,
            $mainSectionCount,
            \sprintf('Expected at most one main section, but found %d.', $mainSectionCount)
        );
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    #[DataProvider('providerSectionCategoryVisibility')]
    public function testSectionCategoryVisibility(
        bool $isOpenCampus,
        bool $isFilters,
        array $sectionMeta,
        int $expectedCount,
        int $expectedCountSection,
        array $expectedCategories
    ) {
        $this->updateSettingValue(value: (string) $isOpenCampus, code: self::APP_OPEN_COURSE_SETTING);
        $this->updateSettingValue(value: (string) $isFilters, code: self::APP_USER_USE_FILTERS_SETTING);

        $courseCategory1 = $this->createAndGetCourseCategory(name: 'Course Category 1', sort: 1);
        $courseCategory2 = $this->createAndGetCourseCategory(name: 'Course Category 2', sort: 2);
        $courseCategory3 = $this->createAndGetCourseCategory(name: 'Course Category 3', sort: 3);
        $courseCategory4 = $this->createAndGetCourseCategory(name: 'Course Category 4', sort: 4);
        $courseCategory5 = $this->createAndGetCourseCategory(name: 'Course Category 5', sort: 5);

        $filterCategory1 = $this->createAndGetFilterCategory(name: 'Filter Category 1');
        $filterCategory2 = $this->createAndGetFilterCategory(name: 'Filter Category 2');
        $filterCategory3 = $this->createAndGetFilterCategory(name: 'Filter Category 3');

        // Create filters
        $filter1 = $this->createAndGetFilter(name: 'Filter 1', category: $filterCategory1);
        $filter2 = $this->createAndGetFilter(name: 'Filter 2', category: $filterCategory2);
        $filter3 = $this->createAndGetFilter(name: 'Filter 3', category: $filterCategory3);

        $course1 = $this->createAndGetCourse(
            name: 'Course 1',
            code: 'course-1',
            open: true,
            openVisible: false,
            courseCategory: $courseCategory1,
            courseFilters: new FilterCollection(
                [$filter3]
            )
        );

        $course2 = $this->createAndGetCourse(
            name: 'Course 2',
            code: 'course-2',
            open: true,
            openVisible: true,
            courseCategory: $courseCategory2,
            courseFilters: new FilterCollection(
                [$filter1]
            )
        );

        $course3 = $this->createAndGetCourse(
            name: 'Course 3',
            code: 'course-3',
            open: true,
            openVisible: true,
            courseCategory: $courseCategory3,
        );

        $course4 = $this->createAndGetCourse(
            name: 'Course 4',
            code: 'course-4',
            open: true,
            openVisible: false,
            courseCategory: $courseCategory4,
            courseFilters: new FilterCollection(
                [$filter2]
            )
        );

        $course5 = $this->createAndGetCourse(
            name: 'Course 5',
            code: 'course-5',
            open: false,
            openVisible: true,
            courseCategory: $courseCategory5,
        );

        $courses = [$course1, $course2, $course3, $course4, $course5];

        foreach ($courses as $course) {
            $season = $this->createAndGetSeason(course: $course);
            $courseChapter = $this->createAndGetChapter(course: $course, season: $season, chapterType: $this->createAndGetChapterTypeRevised());
            $courseChapter->addContent($this->createAndGetContent(chapter: $courseChapter));
        }

        $this->createAndGetCourseSection(
            meta: $sectionMeta,
        );

        $this->user = $this->createAndGetUser(
            firstName: 'Test',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>',
            open: $isOpenCampus,
            userFilters: $isFilters ? new FilterCollection([$filter1, $filter2, $filter3]) : null,
        );

        $userToken = $this->loginAndGetTokenForUser($this->user);

        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendSectionEndpoints::sectionsEndpoint(),
            [],
            [],
            [],
            $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);

        $this->assertCount($expectedCountSection, $responseData['sections']);

        if ($expectedCountSection > 0) {
            $courseSection = $responseData['sections'][0];
            $this->assertCount($expectedCount, $courseSection['categories']);
            $this->assertEquals($expectedCategories, $courseSection['categories']);
        } else {
            $this->assertEmpty($responseData['sections']);
        }
    }

    public static function providerSectionCategoryVisibility(): \Generator
    {
        yield 'Open campus and filters both active' => [
            'isOpenCampus' => true,
            'isFilters' => true,
            'sectionMeta' => [
                ['subtype' => SectionSubtypeEnum::COURSES_BY_OPEN_CAMPUS, 'active' => true],
                ['subtype' => SectionSubtypeEnum::COURSES_BY_FILTERS, 'active' => true],
            ],
            'expectedCount' => 5,
            'expectedCountSection' => 1,
            'expectedCategories' => [
                ['id' => 1, 'name' => 'Course Category 1', 'slug' => 'course-category-1', 'slugMenu' => 'course-category-1', 'sort' => 1],
                ['id' => 2, 'name' => 'Course Category 2', 'slug' => 'course-category-2', 'slugMenu' => 'course-category-2', 'sort' => 2],
                ['id' => 3, 'name' => 'Course Category 3', 'slug' => 'course-category-3', 'slugMenu' => 'course-category-3', 'sort' => 3],
                ['id' => 4, 'name' => 'Course Category 4', 'slug' => 'course-category-4', 'slugMenu' => 'course-category-4', 'sort' => 4],
                ['id' => 5, 'name' => 'Course Category 5', 'slug' => 'course-category-5', 'slugMenu' => 'course-category-5', 'sort' => 5],
            ],
        ];

        yield 'Only filters active (open campus disabled)' => [
            'isOpenCampus' => false,
            'isFilters' => true,
            'sectionMeta' => [
                ['subtype' => SectionSubtypeEnum::COURSES_BY_OPEN_CAMPUS, 'active' => false],
                ['subtype' => SectionSubtypeEnum::COURSES_BY_FILTERS, 'active' => true],
            ],
            'expectedCount' => 3,
            'expectedCountSection' => 1,
            'expectedCategories' => [
                ['id' => 1, 'name' => 'Course Category 1', 'slug' => 'course-category-1', 'slugMenu' => 'course-category-1', 'sort' => 1],
                ['id' => 2, 'name' => 'Course Category 2', 'slug' => 'course-category-2', 'slugMenu' => 'course-category-2', 'sort' => 2],
                ['id' => 4, 'name' => 'Course Category 4', 'slug' => 'course-category-4', 'slugMenu' => 'course-category-4', 'sort' => 4],
            ],
        ];

        yield 'Only open campus active (filters disabled)' => [
            'isOpenCampus' => true,
            'isFilters' => false,
            'sectionMeta' => [
                ['subtype' => SectionSubtypeEnum::COURSES_BY_OPEN_CAMPUS, 'active' => true],
                ['subtype' => SectionSubtypeEnum::COURSES_BY_FILTERS, 'active' => false],
            ],
            'expectedCount' => 3,
            'expectedCountSection' => 1,
            'expectedCategories' => [
                ['id' => 2, 'name' => 'Course Category 2', 'slug' => 'course-category-2', 'slugMenu' => 'course-category-2', 'sort' => 2],
                ['id' => 3, 'name' => 'Course Category 3', 'slug' => 'course-category-3', 'slugMenu' => 'course-category-3', 'sort' => 3],
                ['id' => 5, 'name' => 'Course Category 5', 'slug' => 'course-category-5', 'slugMenu' => 'course-category-5', 'sort' => 5],
            ],
        ];

        yield 'Neither open campus nor filters active' => [
            'isOpenCampus' => false,
            'isFilters' => false,
            'sectionMeta' => [
                ['subtype' => SectionSubtypeEnum::COURSES_BY_OPEN_CAMPUS, 'active' => false],
                ['subtype' => SectionSubtypeEnum::COURSES_BY_FILTERS, 'active' => false],
            ],
            'expectedCount' => 0,
            'expectedCountSection' => 0,
            'expectedCategories' => [],
        ];
    }

    /**
     * @throws ORMException
     * @throws MappingException
     * @throws OptimisticLockException
     * @throws NotSupported
     * @throws Exception
     */
    public function tearDown(): void
    {
        $this->truncateEntities([
            UserLogin::class,
            'course_filter',
            'user_filter',
            Course::class,
            'course_section_course_category',
            CourseCategory::class,
            CourseSection::class,
            Filter::class,
            FilterCategory::class,
        ]);

        foreach ($this->originalsSettings as $settingCode => $originalValue) {
            $this->updateSettingValue(value: $originalValue, code: $settingCode);
        }

        if (null !== $this->user) {
            $this->hardDeleteUsersByIds((array) $this->user->getId());
        }

        parent::tearDown();
    }
}
