<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\EmailNotification;
use App\Entity\UserTime;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\AnnouncementHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\EmailNotificationHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use Symfony\Component\HttpFoundation\Response;

/**
 * Demonstrates how to:
 *   - Create in the DB a TypeCourse, a Course, and an Announcement associated with the default user.
 *   - Always send "new" => true (forcing the backend so that name becomes "GLOBAL").
 *   - Use the actual ID of that Course and Announcement in the payload.
 *   - Verify the response and that a UserTime with name="GLOBAL" and time has been saved in the DB.
 */
class UserTimeTest extends FunctionalTestCase
{
    use EmailNotificationHelperTrait;
    use CourseHelperTrait;
    use AnnouncementHelperTrait;

    /** @var int|null We will store the ID of the Course created in setUp() */
    private ?int $testCourseId = null;

    /** @var int|null We will store the ID of the Announcement created in setUp() */
    private ?int $testAnnouncementId = null;

    protected function setUp(): void
    {
        parent::setUp();

        $this->createAndGetEmailNotification(
            $this->getDefaultUser(), // user
            false,                   // sent
            'announcement',          // type
            'Demo Title',            // title
            'Demo Message',          // message
            ['extra_info' => 'data'] // extra
        );

        $this->createAndGetTranslatedEmailNotification(
            $this->getDefaultUser(), // user
            false,                   // sent
            'announcement',          // type
            'notification.announcement.notification_user_title', // translationTitle
            'notification.announcement.notification_user_message', // translationText
            [[
                '%course%' => 'curso-demo',
                '%startAt%' => (new \DateTime())->format('Y-m-d'),
                '%endAt%' => (new \DateTime())->modify('+5 days')->format('Y-m-d'),
            ]] // attributes
        );

        $typeCourse = $this->getTypeCourse();

        $course = $this->createAndGetCourse(
            'Test Course', // name
            'courseCode-1', // code
            $typeCourse, // typeCourse
            'Test course description', // description
            'es', // locale
            true, // active
            true, // open
            true, // isNew
            true, // openVisible
            null // CourseCategory
        );

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: new \DateTimeImmutable('-2 days'),
            finishAt: new \DateTimeImmutable('+2 days'),
            code: 'TEST-ANN-1' // code
        );

        $this->createAndGetAnnouncementUser(
            $announcement, // announcement
            $this->getDefaultUser() // user
        );

        $this->testCourseId = $course->getId();
        $this->testAnnouncementId = $announcement->getId();
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            AnnouncementUser::class,
            Announcement::class,
            Course::class,
            EmailNotification::class,
            UserTime::class,
        ]);

        parent::tearDown();
    }

    /**
     * Verifies the time update without notifications by sending "new" => true,
     * and using the generated Course and Announcement.
     */
    public function testUserTimeUpdateWithoutNotification(): void
    {
        $locale = self::DEFAULT_USER_LOCALE;
        $this->setLocaleForUser($locale);

        $token = $this->loginAndGetToken();
        $randomTime = random_int(10, 99);

        $payload = [
            'notification' => 0,
            'time' => $randomTime,
            'name' => 'course', // Will be forced to 'GLOBAL' by 'new' => true
            'new' => true,
            'params' => [
                'courseId' => $this->testCourseId,
                'announcementId' => $this->testAnnouncementId,
            ],
        ];

        $response = $this->makeFrontendApiRequest(
            'POST',
            FrontendUserEndpoints::userTimeEndpoint(),
            $payload,
            [],
            [],
            $token
        );

        $this->assertEquals(
            Response::HTTP_OK,
            $response->getStatusCode(),
            "[Locale: $locale] Expected HTTP 200 status code."
        );

        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse(
            $responseData['error'],
            "[Locale: $locale] Expected 'error' = false in the response."
        );
        $this->assertIsArray(
            $responseData['data'],
            "[Locale: $locale] Expected 'data' to be an array."
        );
        $this->assertEmpty(
            $responseData['data'],
            "[Locale: $locale] Expected 'data' to be an empty array (no notifications)."
        );

        // Verify that UserTime was saved
        $em = $this->getEntityManager();
        $userTimeRepo = $em->getRepository(UserTime::class);

        $userTime = $userTimeRepo->findOneBy([
            'user' => $this->getDefaultUser(),
            'name' => 'GLOBAL',
            'announcement' => $this->testAnnouncementId,
        ]);

        $this->assertNotNull(
            $userTime,
            "[Locale: $locale] Expected a UserTime when forcing new=true."
        );
        $this->assertSame(
            self::DEFAULT_TEST_IP,
            $userTime->getIp(),
            'The IP does not match the test one.'
        );
        $this->assertEquals(
            $randomTime,
            $userTime->getTime(),
            "[Locale: $locale] The UserTime does not have the expected time ($randomTime)."
        );
        $this->assertNotNull(
            $userTime->getAnnouncement(),
            "[Locale: $locale] Expected the GLOBAL UserTime to have an assigned Announcement."
        );
        $this->assertEquals(
            $this->testAnnouncementId,
            $userTime->getAnnouncement()->getId(),
            "[Locale: $locale] The Announcement of the UserTime does not match the expected one."
        );
    }

    /**
     * Verifies the time update with notifications by sending "new" => true,
     * and using the generated Course and Announcement.
     *
     * @dataProvider localeProvider
     */
    public function testUserTimeUpdateWithNotification(string $locale): void
    {
        $this->setLocaleForUser($locale);

        $token = $this->loginAndGetToken();
        $randomTime = random_int(10, 99);

        $payload = [
            'notification' => 1,
            'time' => $randomTime,
            'name' => 'course',
            'new' => true, // Forced => "GLOBAL"
            'params' => [
                'courseId' => $this->testCourseId,
                'announcementId' => $this->testAnnouncementId,
            ],
        ];

        $response = $this->makeFrontendApiRequest(
            'POST',
            FrontendUserEndpoints::userTimeEndpoint(),
            $payload,
            [],
            [],
            $token
        );

        $this->assertEquals(
            Response::HTTP_OK,
            $response->getStatusCode(),
            "[Locale: $locale] Expected HTTP 200 status code when performing update-time with notification=1."
        );

        $responseData = json_decode($response->getContent(), true);

        $this->assertFalse(
            $responseData['error'],
            "[Locale: $locale] Expected 'error' = false in the response."
        );
        $this->assertIsArray(
            $responseData['data'],
            "[Locale: $locale] Expected 'data' to be an array of notifications."
        );
        $this->assertNotEmpty(
            $responseData['data'],
            "[Locale: $locale] Expected 'data' to contain at least one notification."
        );

        $em = $this->getEntityManager();
        $userTimeRepo = $em->getRepository(UserTime::class);

        $userTime = $userTimeRepo->findOneBy([
            'user' => $this->getDefaultUser(),
            'name' => 'GLOBAL',
        ]);
        $this->assertNotNull(
            $userTime,
            "[Locale: $locale] No UserTime with name='GLOBAL' was found after new=true."
        );
        $this->assertEquals(
            $randomTime,
            $userTime->getTime(),
            "[Locale: $locale] The UserTime (GLOBAL) does not have the expected time ($randomTime)."
        );

        foreach ($responseData['data'] as $notification) {
            $this->assertArrayHasKey('id', $notification, 'Missing key "id".');
            $this->assertArrayHasKey('type', $notification, 'Missing key "type".');
            $this->assertArrayHasKey('sent', $notification, 'Missing key "sent".');
            $this->assertArrayHasKey('createdAt', $notification, 'Missing key "createdAt".');
            $this->assertArrayHasKey('title', $notification, 'Missing key "title".');
            $this->assertArrayHasKey('message', $notification, 'Missing key "message".');
            $this->assertArrayHasKey('date', $notification, 'Missing key "date".');
            $this->assertArrayHasKey('createdAtNotFormatted', $notification, 'Missing key "createdAtNotFormatted".');
            $this->assertArrayHasKey('dateNotFormatted', $notification, 'Missing key "dateNotFormatted".');

            $notificationEntity = $em
                ->getRepository(EmailNotification::class)
                ->find($notification['id']);

            $this->assertNotNull($notificationEntity, \sprintf(
                'No EmailNotification with ID %d was found in the database.',
                $notification['id']
            ));

            $expectedTitle = $notificationEntity->getTitle();
            $expectedText = $notificationEntity->getMessage();

            if (!$expectedTitle) {
                $dbTranslationTitle = $notificationEntity->getTranslationTitle();
                $expectedTitle = $this->getTranslatedMessage(
                    $dbTranslationTitle,
                    [],
                    'emailNotification',
                    $locale
                );
            }

            if (!$expectedText) {
                $dbTranslationText = $notificationEntity->getTranslationText();
                $dbTranslationTextAttributes = $notificationEntity->getAttributes();
                $expectedText = $this->getTranslatedMessage(
                    $dbTranslationText,
                    $dbTranslationTextAttributes,
                    'emailNotification',
                    $locale
                );
            }

            $this->assertSame(
                $expectedTitle,
                $notification['title'],
                "[Locale: $locale] The translated title does not match."
            );
            $this->assertSame(
                $expectedText,
                $notification['message'],
                "[Locale: $locale] The translated message does not match."
            );
        }
    }
}
