<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Filter;
use App\Entity\FilterCategory;

class FilterMother
{
    public static function create(
        ?int $id = null,
        ?string $name = null,
        ?FilterCategory $category = null,
    ): Filter {
        $filter = new Filter();
        $filter->setId($id)
            ->setName($name ?? 'Filter')
            ->setFilterCategory($category);

        return $filter;
    }
}
