<?php

declare(strict_types=1);

namespace App\Tests\Service\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\ConfigurationClientAnnouncement;
use App\Repository\AnnouncementConfigurationRepository;
use App\Repository\AnnouncementConfigurationTypeRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;

class AnnouncementConfigurationServiceTest extends TestCase
{
    public function testIsConfigurationEnabledWithNoConfigurationType()
    {
        // Mock Announcement
        $announcement = $this->createMock(Announcement::class);

        /** Mock AnnouncementConfigurationTypeRepository
         * Expect to use only once with the method find and the parameter 1
         * Return null to test the case when the configuration is not found.
         * */
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository->expects($this->once())
            ->method('find')
            ->with(1)
            ->willReturn(null);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnMap([
            [AnnouncementConfigurationType::class, $announcementConfigurationTypeRepository]
        ]);

        /*$em->method('getRepository')->willReturnCallback(
            function ($entity) use ($announcementConfigurationTypeRepository) {
                if ($entity === AnnouncementConfigurationType::class) {
                    return $announcementConfigurationTypeRepository;
                }
                return null;
            });*/

        $service = new AnnouncementConfigurationsService($em);
        $result = $service->isConfigurationEnabled($announcement, 1);

        $this->assertFalse($result);
    }

    public function testIsConfigurationEnabledWithConfigurationTypeIsActiveFalse()
    {
        $announcement = $this->createMock(Announcement::class);

        // Mocking entity AnnouncementConfigurationType
        $configurationType = $this->createMock(AnnouncementConfigurationType::class);
        $configurationType->method('isActive')->willReturn(false);

        /** Mock AnnouncementConfigurationTypeRepository
         * Expect to use only once with the method find and the parameter 1
         * Return the entity previously created to test the case when the configuration->isActive() returns false.
         * */
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository->expects($this->once())
            ->method('find')
            ->with(1)
            ->willReturn($configurationType);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnMap([
            [AnnouncementConfigurationType::class, $announcementConfigurationTypeRepository]
        ]);

        $service = new AnnouncementConfigurationsService($em);
        $result = $service->isConfigurationEnabled($announcement, 1);

        $this->assertFalse($result);
    }

    public function testIsConfigurationEnabledWithConfigurationClientAnnouncementIsActiveFalse()
    {
        $announcement = $this->createMock(Announcement::class);

        // Mocking entity AnnouncementConfigurationType and set configurationClientAnnouncement to false
        $configurationClientAnnouncement = $this->createMock(ConfigurationClientAnnouncement::class);
        $configurationClientAnnouncement->method('isActive')->willReturn(false);
        $configurationType = new AnnouncementConfigurationType();
        $configurationType->setActive(true);
        $configurationType->setConfigurationClientAnnouncement($configurationClientAnnouncement);
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository->expects($this->once())
            ->method('find')
            ->with(1)
            ->willReturn($configurationType);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnMap([
            [AnnouncementConfigurationType::class, $announcementConfigurationTypeRepository]
        ]);

        $service = new AnnouncementConfigurationsService($em);
        $result = $service->isConfigurationEnabled($announcement, 1);

        $this->assertFalse($result);
    }

    public function testIsConfigurationEnabledWithNoConfiguration()
    {
        $announcement = $this->createMock(Announcement::class);

        // Mocking entity AnnouncementConfigurationType and set configurationClientAnnouncement to false
        $configurationClientAnnouncement = $this->createMock(ConfigurationClientAnnouncement::class);
        $configurationClientAnnouncement->method('isActive')->willReturn(true);
        $configurationType = new AnnouncementConfigurationType();
        $configurationType->setActive(true);
        $configurationType->setConfigurationClientAnnouncement($configurationClientAnnouncement);
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository->expects($this->once())
            ->method('find')
            ->with(1)
            ->willReturn($configurationType);

        $announcementConfigurationRepository = $this->createMock(AnnouncementConfigurationRepository::class);
        $announcementConfigurationRepository->expects($this->once())
            ->method('findOneBy')
            ->with(['announcement' => $announcement, 'configuration' => $configurationType])
            ->willReturn(null);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnMap([
            [AnnouncementConfigurationType::class, $announcementConfigurationTypeRepository],
            [AnnouncementConfiguration::class, $announcementConfigurationRepository]
        ]);

        $service = new AnnouncementConfigurationsService($em);
        $result = $service->isConfigurationEnabled($announcement, 1);

        $this->assertFalse($result);
    }

    public function testIsConfigurationEnabled()
    {
        $announcement = $this->createMock(Announcement::class);

        $configurationClientAnnouncement = $this->createMock(ConfigurationClientAnnouncement::class);
        $configurationClientAnnouncement->method('isActive')->willReturn(true);
        $configurationType = new AnnouncementConfigurationType();
        $configurationType->setActive(true);
        $configurationType->setConfigurationClientAnnouncement($configurationClientAnnouncement);
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository->expects($this->once())
            ->method('find')
            ->with(1)
            ->willReturn($configurationType);

        $announcementConfigurationRepository = $this->createMock(AnnouncementConfigurationRepository::class);
        $announcementConfigurationRepository->expects($this->once())
            ->method('findOneBy')
            ->with(['announcement' => $announcement, 'configuration' => $configurationType])
            ->willReturn(new AnnouncementConfiguration());

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnMap([
            [AnnouncementConfigurationType::class, $announcementConfigurationTypeRepository],
            [AnnouncementConfiguration::class, $announcementConfigurationRepository]
        ]);

        $service = new AnnouncementConfigurationsService($em);
        $result = $service->isConfigurationEnabled($announcement, 1);

        $this->assertTrue($result);
    }
}
