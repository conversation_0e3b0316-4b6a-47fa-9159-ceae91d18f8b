<?php

namespace App\Tests\Service\Annoucement\Admin;

use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Repository\AnnouncementConfigurationRepository;
use App\Repository\AnnouncementConfigurationTypeRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class AnnouncementConfigurationsServiceTest extends TestCase
{
    private const string ANNOUNCEMENT_ID = '1';
    private const string CONFIGURATION_ID = '1';

    /**
     * @throws Exception
     */
    #[DataProvider('isConfigurationEnabledProvider')]
    public function testIsConfigurationEnabled(
        ?AnnouncementConfigurationType $announcementConfigurationType,
        ?AnnouncementConfiguration $announcementConfiguration,
        bool $expected,
    ): void {
        $configurationId = self::CONFIGURATION_ID;
        $announcement = AnnouncementMother::create(
            id: self::ANNOUNCEMENT_ID,
        );

        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository
            ->expects($this->once())
            ->method('find')
            ->with($configurationId)
            ->willReturn($announcementConfigurationType);

        $announcementConfigurationRepository = $this->createMock(AnnouncementConfigurationRepository::class);

        if (null !== $announcementConfiguration) {
            $announcementConfigurationRepository
                ->expects($this->once())
                ->method('findOneBy')
                ->with(['announcement' => $announcement, 'configuration' => $announcementConfigurationType])
                ->willReturn($announcementConfiguration);
        }

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnMap([
            [AnnouncementConfigurationType::class, $announcementConfigurationTypeRepository],
            [AnnouncementConfiguration::class, $announcementConfigurationRepository]
        ]);

        $service = new AnnouncementConfigurationsService($em);

        $this->assertEquals(
            $expected,
            $service->isConfigurationEnabled($announcement, $configurationId)
        );
    }

    public static function isConfigurationEnabledProvider(): \Generator
    {
        yield 'AnnouncementConfigurationType is not found' => [
            null,
            null,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(false);
        yield 'AnnouncementConfigurationType is found but not active' => [
            $announcementConfigurationType,
            null,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(true);
        yield 'AnnouncementConfigurationType is found and active but AnnouncementConfiguration is null' => [
            $announcementConfigurationType,
            null,
            false
        ];

        $announcementConfiguration = new AnnouncementConfiguration();
        $announcementConfiguration->setAnnouncement(AnnouncementMother::create(id: self::ANNOUNCEMENT_ID));
        $announcementConfiguration->setConfiguration($announcementConfigurationType);
        yield 'AnnouncementConfigurationType is found and active and AnnouncementConfiguration is not null' => [
            $announcementConfigurationType,
            $announcementConfiguration,
            true
        ];
    }

    /**
     * @throws Exception
     */
    #[DataProvider('hasEmailNotificationOnAnnouncementProvider')]
    public function testHasEmailNotificationOnAnnouncement(
        ?AnnouncementConfigurationType $announcementConfigurationType,
        bool $expected,
    ): void {
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository
            ->expects($this->once())
            ->method('find')
            ->with(AnnouncementConfigurationType::ID_ENABLE_EMAIL_NOTIFICATION_ON_ANNOUNCEMENT)
            ->willReturn($announcementConfigurationType);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')
            ->with(AnnouncementConfigurationType::class)
            ->willReturn($announcementConfigurationTypeRepository);

        $service = new AnnouncementConfigurationsService($em);

        $this->assertEquals(
            $expected,
            $service->hasEmailNotificationOnAnnouncement()
        );
    }

    public static function hasEmailNotificationOnAnnouncementProvider(): \Generator
    {
        yield 'AnnouncementConfigurationType is not found' => [
            null,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(false);
        yield 'AnnouncementConfigurationType is found but not active' => [
            $announcementConfigurationType,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(true);
        yield 'AnnouncementConfigurationType is found and active' => [
            $announcementConfigurationType,
            true
        ];
    }

    /**
     * @throws Exception
     */
    #[DataProvider('hasNotificationOnAnnouncementProvider')]
    public function testHasNotificationOnAnnouncement(
        ?AnnouncementConfigurationType $announcementConfigurationType,
        bool $expected,
    ): void {
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository
            ->expects($this->once())
            ->method('find')
            ->with(AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION_ON_ANNOUNCEMENT)
            ->willReturn($announcementConfigurationType);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')
            ->with(AnnouncementConfigurationType::class)
            ->willReturn($announcementConfigurationTypeRepository);

        $service = new AnnouncementConfigurationsService($em);

        $this->assertEquals(
            $expected,
            $service->hasNotificationOnAnnouncement()
        );
    }

    public static function hasNotificationOnAnnouncementProvider(): \Generator
    {
        yield 'AnnouncementConfigurationType is not found' => [
            null,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(false);
        yield 'AnnouncementConfigurationType is found but not active' => [
            $announcementConfigurationType,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(true);
        yield 'AnnouncementConfigurationType is found and active' => [
            $announcementConfigurationType,
            true
        ];
    }

    /**
     * @throws Exception
     */
    #[DataProvider('hasEnrollmentTemplateProvider')]
    public function testHasEnrollmentTemplate(
        ?AnnouncementConfigurationType $announcementConfigurationType,
        bool $expected,
    ): void {
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository
            ->expects($this->once())
            ->method('find')
            ->with(AnnouncementConfigurationType::ID_TEMPLATE_XLSX_IBEROSTAR)
            ->willReturn($announcementConfigurationType);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')
            ->with(AnnouncementConfigurationType::class)
            ->willReturn($announcementConfigurationTypeRepository);

        $service = new AnnouncementConfigurationsService($em);

        $this->assertEquals(
            $expected,
            $service->hasEnrollmentTemplate()
        );
    }

    public static function hasEnrollmentTemplateProvider(): \Generator
    {
        yield 'AnnouncementConfigurationType is not found' => [
            null,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(false);
        yield 'AnnouncementConfigurationType is found but not active' => [
            $announcementConfigurationType,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(true);
        yield 'AnnouncementConfigurationType is found and active' => [
            $announcementConfigurationType,
            true
        ];
    }

    /**
     * @throws Exception
     */
    #[DataProvider('hasExportReportZipProvider')]
    public function testHasExportReportZip(
        ?AnnouncementConfigurationType $announcementConfigurationType,
        bool $expected,
    ): void {
        $announcementConfigurationTypeRepository = $this->createMock(AnnouncementConfigurationTypeRepository::class);
        $announcementConfigurationTypeRepository
            ->expects($this->once())
            ->method('find')
            ->with(AnnouncementConfigurationType::ID_REPORT_ZIP)
            ->willReturn($announcementConfigurationType);

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')
            ->with(AnnouncementConfigurationType::class)
            ->willReturn($announcementConfigurationTypeRepository);

        $service = new AnnouncementConfigurationsService($em);

        $this->assertEquals(
            $expected,
            $service->hasExportReportZip()
        );
    }

    public static function hasExportReportZipProvider(): \Generator
    {
        yield 'AnnouncementConfigurationType is not found' => [
            null,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(false);
        yield 'AnnouncementConfigurationType is found but not active' => [
            $announcementConfigurationType,
            false
        ];

        $announcementConfigurationType = new AnnouncementConfigurationType();
        $announcementConfigurationType->setActive(true);
        yield 'AnnouncementConfigurationType is found and active' => [
            $announcementConfigurationType,
            true
        ];
    }
}
