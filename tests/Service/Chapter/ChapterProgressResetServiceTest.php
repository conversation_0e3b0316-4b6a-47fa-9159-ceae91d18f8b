<?php

declare(strict_types=1);

namespace App\Tests\Service\Chapter;

use App\Entity\Chapter;
use App\Entity\UserCourseChapter;
use App\Exception\ChapterResetServiceException;
use App\Service\Chapter\ChapterProgressResetService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;

/**
 * Test class for ChapterProgressResetService
 * Tests the functionality of resetting SCORM chapter progress.
 */
class ChapterProgressResetServiceTest extends FunctionalTestCase
{
    use ChapterTypeHelperTrait;
    use SeasonHelperTrait;
    use ChapterHelperTrait;

    private $entityManager;
    private $userCourseChapterRepository;
    private $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->entityManager = $this->getEntityManager();
        $this->userCourseChapterRepository = $this->getRepository(UserCourseChapter::class);

        $this->service = new ChapterProgressResetService(
            $this->entityManager,
            $this->userCourseChapterRepository
        );
    }

    public function testResetCurrentProgressWithNonScormChapter(): void
    {
        // Create a non-SCORM chapter
        $chapterType = $this->createAndGetChapterType('video');
        $course = $this->createAndGetCourse();
        $season = $this->createAndGetSeason(course: $course);
        $chapter = $this->createAndGetChapter(course: $course, chapterType: $chapterType, season: $season);

        // Verify that the expected exception is thrown
        $this->expectException(ChapterResetServiceException::class);
        $this->expectExceptionMessage('Only SCORM type chapters can be reset');

        $this->service->resetCurrentProgress($chapter);
    }

    public function testResetCurrentProgressWithScormChapter(): void
    {
        // Create a SCORM chapter
        $chapterType = $this->createAndGetChapterType('scorm');
        $course = $this->createAndGetCourse();
        $season = $this->createAndGetSeason(course: $course);
        $chapter = $this->createAndGetChapter(course: $course, chapterType: $chapterType, season: $season);

        // Create UserCourse
        $userCourse = new \App\Entity\UserCourse();
        $userCourse->setUser($this->getDefaultUser());
        $userCourse->setCourse($course);
        $this->entityManager->persist($userCourse);
        $this->entityManager->flush();

        // Create UserCourseChapter with progress data
        $userCourseChapter = new UserCourseChapter();
        $userCourseChapter->setUserCourse($userCourse);
        $userCourseChapter->setChapter($chapter);
        $userCourseChapter->setData([
            'scorm' => [
                'cmi.core.student_id' => 1,
                'cmi.core.student_name' => 'Support',
                'cmi.core.exit' => 'suspend',
                'cmi.core.lesson_status' => 'incomplete',
                'cmi.suspend_data' => 'progress data',
            ],
        ]);
        $userCourseChapter->setFinishedAt(new \DateTime());
        $this->entityManager->persist($userCourseChapter);
        $this->entityManager->flush();

        // Execute the reset
        $this->service->resetCurrentProgress($chapter);

        // Verify the changes
        $this->entityManager->refresh($userCourseChapter);
        $data = $userCourseChapter->getData();

        $this->assertNotEmpty($userCourseChapter->getFinishedAt());
        $this->assertArrayHasKey('scorm', $data);
        $this->assertCount(2, $data['scorm']); // Should only have student_id and student_name
        $this->assertArrayHasKey('cmi.core.student_id', $data['scorm']);
        $this->assertArrayHasKey('cmi.core.student_name', $data['scorm']);
    }
}
