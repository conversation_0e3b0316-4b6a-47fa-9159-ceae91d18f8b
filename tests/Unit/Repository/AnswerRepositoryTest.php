<?php

declare(strict_types=1);

namespace App\Tests\Unit\Repository;

use App\Entity\Answer;
use App\Repository\AnswerRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\FilterCollection;
use PHPUnit\Framework\TestCase;

class AnswerRepositoryTest extends TestCase
{
    private AnswerRepository $repository;
    private EntityManagerInterface $entityManager;
    private FilterCollection $filters;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->filters = $this->createMock(FilterCollection::class);
        $this->repository = $this->createPartialMock(AnswerRepository::class, ['findOneBy']);
    }

    public function testFindWithDeletedDisablesAndEnablesFilter(): void
    {
        $answerId = 123;
        $answer = new Answer();

        $this->entityManager
            ->expects($this->once())
            ->method('getFilters')
            ->willReturn($this->filters);

        $this->filters
            ->expects($this->once())
            ->method('disable')
            ->with('softdeleteable');

        $this->filters
            ->expects($this->once())
            ->method('enable')
            ->with('softdeleteable');

        $this->repository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => $answerId])
            ->willReturn($answer);

        // Use reflection to set the private entityManager property
        $reflection = new \ReflectionClass($this->repository);
        $property = $reflection->getProperty('_em');
        $property->setAccessible(true);
        $property->setValue($this->repository, $this->entityManager);

        $result = $this->repository->findWithDeleted($answerId);

        $this->assertSame($answer, $result);
    }

    public function testFindWithDeletedReturnsNullWhenNotFound(): void
    {
        $answerId = 999;

        $this->entityManager
            ->expects($this->once())
            ->method('getFilters')
            ->willReturn($this->filters);

        $this->filters
            ->expects($this->once())
            ->method('disable')
            ->with('softdeleteable');

        $this->filters
            ->expects($this->once())
            ->method('enable')
            ->with('softdeleteable');

        $this->repository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => $answerId])
            ->willReturn(null);

        // Use reflection to set the private entityManager property
        $reflection = new \ReflectionClass($this->repository);
        $property = $reflection->getProperty('_em');
        $property->setAccessible(true);
        $property->setValue($this->repository, $this->entityManager);

        $result = $this->repository->findWithDeleted($answerId);

        $this->assertNull($result);
    }

    public function testFindWithDeletedAlwaysEnablesFilterEvenOnException(): void
    {
        $answerId = 123;

        $this->entityManager
            ->expects($this->once())
            ->method('getFilters')
            ->willReturn($this->filters);

        $this->filters
            ->expects($this->once())
            ->method('disable')
            ->with('softdeleteable');

        $this->filters
            ->expects($this->once())
            ->method('enable')
            ->with('softdeleteable');

        $this->repository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => $answerId])
            ->willThrowException(new \Exception('Database error'));

        // Use reflection to set the private entityManager property
        $reflection = new \ReflectionClass($this->repository);
        $property = $reflection->getProperty('_em');
        $property->setAccessible(true);
        $property->setValue($this->repository, $this->entityManager);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->repository->findWithDeleted($answerId);
    }
}
