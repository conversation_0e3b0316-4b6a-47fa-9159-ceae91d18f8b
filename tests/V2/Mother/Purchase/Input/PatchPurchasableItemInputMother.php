<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Purchase\Input;

use App\V2\Application\DTO\Purchase\PatchPurchasableItemInputDTO;
use App\V2\Domain\Shared\Financial\Money;

class PatchPurchasableItemInputMother
{
    public static function create(
        ?Money $price = null,
        ?bool $isActive = null,
    ): PatchPurchasableItemInputDTO {
        return new PatchPurchasableItemInputDTO(
            price: $price,
            isActive: $isActive,
        );
    }
}
