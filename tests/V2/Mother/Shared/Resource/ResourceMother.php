<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Shared\Resource;

use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

class ResourceMother
{
    private const ResourceType DEFAULT_TYPE = ResourceType::Course;

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function create(
        ?ResourceType $type = null,
        ?Identifier $id = null,
    ): Resource {
        $resourceType = $type ?? self::DEFAULT_TYPE;
        $identifier = $id ?? (ResourceType::Course === $resourceType ? IdMother::create() : UuidMother::create());

        return new Resource($resourceType, $identifier);
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function createCourse(?Identifier $id = null): Resource
    {
        return new Resource(
            ResourceType::Course,
            $id ?? IdMother::create()
        );
    }

    /**
     * @throws InvalidUuidException
     */
    public static function createSubscription(?Identifier $id = null): Resource
    {
        return new Resource(
            ResourceType::Subscription,
            $id ?? UuidMother::create()
        );
    }
}
