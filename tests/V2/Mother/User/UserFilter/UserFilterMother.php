<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\User\UserFilter;

use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\UserFilter\UserFilter;

class UserFilterMother
{
    public static function create(
        ?Id $userId = null,
        ?Id $filterId = null,
    ): UserFilter {
        return new UserFilter(
            userId: $userId ?? new Id(random_int(1, 50)),
            filterId: $filterId ?? new Id(random_int(1, 50)),
        );
    }
}
