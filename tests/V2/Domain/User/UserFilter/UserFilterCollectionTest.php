<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\User\UserFilter;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;

class UserFilterCollectionTest extends CollectionTestCase
{
    protected function createCollection(array $items): Collection
    {
        return new UserFilterCollection($items);
    }

    protected function getExpectedType(): string
    {
        return UserFilter::class;
    }

    protected function getItem(): object
    {
        return UserFilterMother::create();
    }
}
