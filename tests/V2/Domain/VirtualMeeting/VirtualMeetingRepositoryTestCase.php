<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\VirtualMeeting;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingNotFoundException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class VirtualMeetingRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): VirtualMeetingRepository;

    /**
     * @throws VirtualMeetingNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     */
    public function testPut(): void
    {
        $meeting1 = VirtualMeetingMother::create();
        $meeting2 = VirtualMeetingMother::create();
        $meeting3 = VirtualMeetingMother::create();

        $repository = $this->getRepository();

        $repository->put($meeting1);
        $repository->put($meeting2);
        $repository->put($meeting3);

        $this->assertEquals(
            $meeting2,
            $repository->findOneBy(
                VirtualMeetingCriteria::createById($meeting2->getId())
            )
        );

        // Test update
        $updatedMeeting = VirtualMeetingMother::create(
            id: $meeting1->getId(),
            type: VirtualMeetingType::Fixed,
            url: 'https://updated-url.com'
        );

        $repository->put($updatedMeeting);

        $foundMeeting = $repository->findOneBy(
            VirtualMeetingCriteria::createById($meeting1->getId())
        );

        $this->assertEquals($updatedMeeting, $foundMeeting);
        $this->assertEquals('https://updated-url.com', $foundMeeting->getUrl());
    }

    /**
     * @throws VirtualMeetingNotFoundException
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws InvalidVirtualMeetingException
     */
    public function testFindOneBy(): void
    {
        $meeting1 = VirtualMeetingMother::create();
        $meeting2 = VirtualMeetingMother::create();
        $meeting3 = VirtualMeetingMother::create();

        $repository = $this->getRepository();

        $repository->put($meeting1);
        $repository->put($meeting2);
        $repository->put($meeting3);

        $this->assertEquals(
            $meeting2,
            $repository->findOneBy(
                VirtualMeetingCriteria::createById($meeting2->getId())
            )
        );

        $this->expectException(VirtualMeetingNotFoundException::class);
        $repository->findOneBy(
            VirtualMeetingCriteria::createById(UuidMother::create())
        );
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     */
    #[DataProvider('findByDataProvider')]
    public function testFindBy(
        VirtualMeetingCollection $input,
        VirtualMeetingCriteria $criteria,
        int $expectedCount,
        array $expectedResults
    ) {
        $repository = $this->getRepository();
        foreach ($input->all() as $meeting) {
            $repository->put($meeting);
        }

        $result = $repository->findBy(
            VirtualMeetingCriteria::createEmpty()
        );
        $this->assertCount(
            count($input->all()),
            $result->all()
        );

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);

        // Check that all expected results are in the result
        foreach ($expectedResults as $expected) {
            $found = false;
            foreach ($result->all() as $actual) {
                if ($expected->getId()->equals($actual->getId())) {
                    $found = true;
                    break;
                }
            }
            $this->assertTrue($found, "Expected meeting with ID {$expected->getId()->value()} not found in results");
        }
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     */
    public static function findByDataProvider(): \Generator
    {
        $meeting1 = VirtualMeetingMother::create(
            type: VirtualMeetingType::Fixed,
            startAt: new \DateTimeImmutable('2025-01-01 10:00:00'),
            finishAt: new \DateTimeImmutable('2025-01-01 11:00:00')
        );

        $meeting2 = VirtualMeetingMother::create(
            type: VirtualMeetingType::Fixed,
            startAt: new \DateTimeImmutable('2025-02-01 10:00:00'),
            finishAt: new \DateTimeImmutable('2025-02-01 11:00:00')
        );

        $meeting3 = VirtualMeetingMother::create(
            type: VirtualMeetingType::Fixed,
            startAt: new \DateTimeImmutable('2025-03-01 10:00:00'),
            finishAt: new \DateTimeImmutable('2025-03-01 11:00:00')
        );

        yield '3 meetings get all' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$meeting1, $meeting2, $meeting3],
        ];

        yield '3 meetings meeting by id' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createById($meeting2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$meeting2],
        ];

        yield '3 meetings meeting by id 1 and id 3' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createByIds(
                new UuidCollection([$meeting1->getId(), $meeting3->getId()])
            ),
            'expectedCount' => 2,
            'expectedResults' => [$meeting1, $meeting3],
        ];

        // Test filtering by type
        yield '3 meetings filter by type' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty()->filterByType(VirtualMeetingType::Fixed),
            'expectedCount' => 3,
            'expectedResults' => [$meeting1, $meeting2, $meeting3],
        ];

        // Test filtering by startAt range
        yield '3 meetings filter by startAt from' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty()
                ->filterByStartAtFrom(new \DateTimeImmutable('2025-02-01 00:00:00')),
            'expectedCount' => 2,
            'expectedResults' => [$meeting2, $meeting3],
        ];

        yield '3 meetings filter by startAt to' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty()
                ->filterByStartAtTo(new \DateTimeImmutable('2025-02-01 23:59:59')),
            'expectedCount' => 2,
            'expectedResults' => [$meeting1, $meeting2],
        ];

        yield '3 meetings filter by startAt range' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty()
                ->filterByStartAtFrom(new \DateTimeImmutable('2025-01-15 00:00:00'))
                ->filterByStartAtTo(new \DateTimeImmutable('2025-02-15 23:59:59')),
            'expectedCount' => 1,
            'expectedResults' => [$meeting2],
        ];

        // Test filtering by finishAt range
        yield '3 meetings filter by finishAt from' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty()
                ->filterByFinishAtFrom(new \DateTimeImmutable('2025-02-01 00:00:00')),
            'expectedCount' => 2,
            'expectedResults' => [$meeting2, $meeting3],
        ];

        yield '3 meetings filter by finishAt to' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty()
                ->filterByFinishAtTo(new \DateTimeImmutable('2025-02-01 23:59:59')),
            'expectedCount' => 2,
            'expectedResults' => [$meeting1, $meeting2],
        ];

        // Test combined filtering
        yield '3 meetings filter by startAt and finishAt' => [
            'input' => new VirtualMeetingCollection([$meeting1, $meeting2, $meeting3]),
            'criteria' => VirtualMeetingCriteria::createEmpty()
                ->filterByStartAtFrom(new \DateTimeImmutable('2025-01-15 00:00:00'))
                ->filterByFinishAtTo(new \DateTimeImmutable('2025-02-15 23:59:59')),
            'expectedCount' => 1,
            'expectedResults' => [$meeting2],
        ];
    }

    /**
     * @throws VirtualMeetingNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     */
    public function testDelete(): void
    {
        $meeting1 = VirtualMeetingMother::create();
        $meeting2 = VirtualMeetingMother::create();
        $meeting3 = VirtualMeetingMother::create();

        $repository = $this->getRepository();

        $repository->put($meeting1);
        $repository->put($meeting2);
        $repository->put($meeting3);

        $this->assertEquals(
            $meeting2,
            $repository->findOneBy(
                VirtualMeetingCriteria::createById($meeting2->getId())
            )
        );

        $repository->delete($meeting2);

        $this->expectException(VirtualMeetingNotFoundException::class);
        $repository->findOneBy(
            VirtualMeetingCriteria::createById($meeting2->getId())
        );
    }
}
