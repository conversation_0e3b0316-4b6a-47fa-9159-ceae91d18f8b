<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\VirtualMeeting;

use App\Tests\V2\Domain\Shared\Entity\EntityWithIdCollectionTestCase;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;

class VirtualMeetingCollectionTest extends EntityWithIdCollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): VirtualMeetingCollection
    {
        return new VirtualMeetingCollection($items);
    }

    protected function getExpectedType(): string
    {
        return VirtualMeeting::class;
    }

    /**
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InvalidVirtualMeetingException
     */
    protected function getItem(): VirtualMeeting
    {
        return VirtualMeetingMother::create();
    }
}
