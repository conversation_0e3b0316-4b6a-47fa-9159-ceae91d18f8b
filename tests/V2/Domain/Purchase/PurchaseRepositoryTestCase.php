<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\Tests\V2\Mother\Purchase\PurchaseItemMother;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\PurchaseItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseItemCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class PurchaseRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): PurchaseRepository;

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     */
    public function testPut(): void
    {
        $repository = $this->getRepository();

        $purchase1 = PurchaseMother::create(
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1000)
        );

        $purchase2 = PurchaseMother::create(
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 2000)
        );

        $repository->put($purchase1);
        $repository->put($purchase2);

        $found = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterById($purchase1->getId())
        );

        $this->assertEquals($purchase1, $found);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testFindOneBy(): void
    {
        $userId1 = IdMother::create(1);
        $userId2 = IdMother::create(2);

        $purchase1 = PurchaseMother::create(
            userId: $userId1,
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1000)
        );
        $purchase2 = PurchaseMother::create(
            userId: $userId2,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 2000)
        );
        $purchase3 = PurchaseMother::create(
            userId: $userId1,
            status: PurchaseStatus::Cancelled,
            amount: MoneyMother::create(amount: 1500)
        );

        $repository = $this->getRepository();
        $repository->put($purchase1);
        $repository->put($purchase2);
        $repository->put($purchase3);

        // Test find by ID
        $found = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterById($purchase2->getId())
        );
        $this->assertEquals($purchase2, $found);

        // Test find by user ID
        $found = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterByUserId($userId2)
        );
        $this->assertEquals($purchase2, $found);

        // Test find by status
        $found = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterByStatus(PurchaseStatus::Completed)
        );
        $this->assertEquals($purchase2, $found);

        // Test find by min amount
        $found = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterByMinAmount(MoneyMother::create(amount: 1800))
        );
        $this->assertEquals($purchase2, $found);

        // Test find by max amount
        $found = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterByMaxAmount(MoneyMother::create(amount: 1200))
        );
        $this->assertEquals($purchase1, $found);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testFindOneByThrowsExceptionWhenNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(PurchaseNotFoundException::class);
        $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterById(UuidMother::create())
        );
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        PurchaseCollection $input,
        PurchaseCriteria $criteria,
        int $expectedCount,
        array $expectedResults,
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $item) {
            $repository->put($item);
        }

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);

        $expectedResultsIds = array_map(
            fn ($item) => $item->getId(),
            $expectedResults
        );

        $foundIds = array_map(
            fn ($item) => $item->getId(),
            $result->all()
        );

        $this->assertCount(
            0,
            array_diff($expectedResultsIds, $foundIds),
            'Expected results not found in results'
        );
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws \DateMalformedStringException
     * @throws \Exception
     */
    public static function provideFindBy(): \Generator
    {
        $userId1 = IdMother::create(1);
        $userId2 = IdMother::create(2);

        $purchase1 = PurchaseMother::create(
            userId: $userId1,
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1000),
        );
        $purchase2 = PurchaseMother::create(
            userId: $userId2,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 2000),
            createdAt: $purchase1->getCreatedAt()->modify('-10 day')
        );
        $purchase3 = PurchaseMother::create(
            userId: $userId1,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 1500),
            createdAt: $purchase1->getCreatedAt()->modify('-10 day')
        );

        yield '3 purchases get all' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$purchase1, $purchase2, $purchase3],
        ];

        yield '3 purchases get purchase 2 by id' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterById($purchase2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$purchase2],
        ];

        yield '3 purchases get purchase 1 and 3 by user id' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterByUserId($userId1),
            'expectedCount' => 2,
            'expectedResults' => [$purchase1, $purchase3],
        ];

        yield '3 purchases get purchase 2 by status' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterByStatus(PurchaseStatus::Completed),
            'expectedCount' => 2,
            'expectedResults' => [$purchase2, $purchase3],
        ];

        yield '3 purchases get purchase 2 by min amount' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 1800)),
            'expectedCount' => 1,
            'expectedResults' => [$purchase2],
        ];

        yield '3 purchases get purchase 1 and 2 by max amount' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterByMaxAmount(MoneyMother::create(amount: 1800)),
            'expectedCount' => 2,
            'expectedResults' => [$purchase1, $purchase3],
        ];

        yield '3 purchases get purchase 2 by amount range' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 1800))
                ->filterByMaxAmount(MoneyMother::create(amount: 2200)),
            'expectedCount' => 1,
            'expectedResults' => [$purchase2],
        ];

        yield '3 purchases get purchase 1 and 3 by user id and status' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterByUserId($userId1)
                ->filterByStatus(PurchaseStatus::Completed),
            'expectedCount' => 1,
            'expectedResults' => [$purchase3],
        ];

        $startDate = $purchase1->getCreatedAt()->modify('-1 day');
        $endDate = $purchase1->getCreatedAt()->modify('+1 day');

        yield '3 purchases get purchase 1 and 3 by date range' => [
            'input' => new PurchaseCollection([$purchase1, $purchase2, $purchase3]),
            'criteria' => PurchaseCriteria::createEmpty()
                ->filterByStartDate($startDate)
                ->filterByEndDate($endDate),
            'expectedCount' => 1,
            'expectedResults' => [$purchase1],
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws \Exception
     */
    public function testCountBy(): void
    {
        $userId1 = IdMother::create(1);
        $userId2 = IdMother::create(2);

        $purchase1 = PurchaseMother::create(
            userId: $userId1,
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1000)
        );
        $purchase2 = PurchaseMother::create(
            userId: $userId2,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 2000)
        );
        $purchase3 = PurchaseMother::create(
            userId: $userId1,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 1500)
        );

        $repository = $this->getRepository();
        $repository->put($purchase1);
        $repository->put($purchase2);
        $repository->put($purchase3);

        // Test count all
        $count = $repository->countBy(PurchaseCriteria::createEmpty());
        $this->assertEquals(3, $count);

        // Test count by user ID
        $count = $repository->countBy(
            PurchaseCriteria::createEmpty()->filterByUserId($userId1)
        );
        $this->assertEquals(2, $count);

        // Test count by status
        $count = $repository->countBy(
            PurchaseCriteria::createEmpty()->filterByStatus(PurchaseStatus::Completed)
        );
        $this->assertEquals(2, $count);

        // Test count by amount range
        $count = $repository->countBy(
            PurchaseCriteria::createEmpty()->filterByMinAmount(MoneyMother::create(amount: 1200))
        );
        $this->assertEquals(2, $count);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     */
    public function testDelete(): void
    {
        $purchase1 = PurchaseMother::create(
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1000)
        );
        $purchase2 = PurchaseMother::create(
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 2000)
        );

        $repository = $this->getRepository();
        $repository->put($purchase1);
        $repository->put($purchase2);

        // Verify both purchases exist
        $count = $repository->countBy(PurchaseCriteria::createEmpty());
        $this->assertEquals(2, $count);

        // Delete one purchase
        $repository->delete($purchase1);

        // Verify only one purchase remains
        $count = $repository->countBy(PurchaseCriteria::createEmpty());
        $this->assertEquals(1, $count);

        // Verify the remaining purchase is the correct one
        $found = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterById($purchase2->getId())
        );
        $this->assertEquals($purchase2, $found);

        // Verify the deleted purchase is not found
        $this->expectException(PurchaseNotFoundException::class);
        $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterById($purchase1->getId())
        );
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws \Exception
     */
    public function testCountByConsistencyWithFindBy(): void
    {
        $userId = IdMother::create();

        $purchase1 = PurchaseMother::create(
            userId: $userId,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 1000)
        );
        $purchase2 = PurchaseMother::create(
            userId: $userId,
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1500)
        );

        $repository = $this->getRepository();
        $repository->put($purchase1);
        $repository->put($purchase2);

        $criteria = PurchaseCriteria::createEmpty()
            ->filterByUserId($userId)
            ->filterByMinAmount(MoneyMother::create(amount: 1200));

        $foundCollection = $repository->findBy($criteria);
        $count = $repository->countBy($criteria);

        $this->assertEquals($foundCollection->count(), $count);
        $this->assertEquals(1, $count);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws PurchaseItemNotFoundException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testPutPurchaseItem(): void
    {
        $purchaseItem = PurchaseItemMother::create();

        $repository = $this->getRepository();
        $repository->putPurchaseItem($purchaseItem);

        $found = $repository->findOnePurchaseItemBy(
            PurchaseItemCriteria::createById($purchaseItem->getId())
        );

        $this->assertEquals($purchaseItem, $found);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws PurchaseItemNotFoundException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testFindOnePurchaseItemBy(): void
    {
        $purchaseItem1 = PurchaseItemMother::create();
        $purchaseItem2 = PurchaseItemMother::create();
        $purchaseItem3 = PurchaseItemMother::create();

        $repository = $this->getRepository();
        $repository->putPurchaseItem($purchaseItem1);
        $repository->putPurchaseItem($purchaseItem2);
        $repository->putPurchaseItem($purchaseItem3);

        $found = $repository->findOnePurchaseItemBy(
            PurchaseItemCriteria::createEmpty()
                ->filterById($purchaseItem2->getId())
        );
        $this->assertEquals($purchaseItem2, $found);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     */
    public function testFindPurchaseItemsBy(): void
    {
        $purchaseId1 = UuidMother::create();
        $purchaseId2 = UuidMother::create();

        $purchaseItem1 = PurchaseItemMother::create(
            purchaseId: $purchaseId1,
            price: MoneyMother::create(amount: 1000)
        );
        $purchaseItem2 = PurchaseItemMother::create(
            purchaseId: $purchaseId2,
            price: MoneyMother::create(amount: 2000)
        );
        $purchaseItem3 = PurchaseItemMother::create(
            purchaseId: $purchaseId1,
            price: MoneyMother::create(amount: 3000)
        );

        $repository = $this->getRepository();
        $repository->putPurchaseItem($purchaseItem1);
        $repository->putPurchaseItem($purchaseItem2);
        $repository->putPurchaseItem($purchaseItem3);

        $found = $repository->findPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()
                ->filterByPurchaseId($purchaseItem2->getPurchaseId())
        );
        $this->assertEquals(1, $found->count());
        $this->assertEquals($purchaseItem2, $found->first());

        $found = $repository->findPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()
                ->filterByPurchasableItemId($purchaseItem3->getPurchasableItemId())
        );
        $this->assertEquals(1, $found->count());
        $this->assertEquals($purchaseItem3, $found->first());

        $found = $repository->findPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()
                ->filterByMaxPrice(MoneyMother::create(amount: 1500))
        );
        $this->assertEquals(1, $found->count());
        $this->assertEquals($purchaseItem1, $found->first());

        $found = $repository->findPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 2500))
        );
        $this->assertEquals(1, $found->count());
        $this->assertEquals($purchaseItem3, $found->first());

        $found = $repository->findPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()
                ->filterByPurchaseIds(new UuidCollection([$purchaseId1]))
        );
        $this->assertEquals(2, $found->count());
        $this->assertEquals($purchaseItem1, $found->first());
        $this->assertEquals($purchaseItem3, $found->last());
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testCountPurchaseItemsBy(): void
    {
        $purchaseItem1 = PurchaseItemMother::create();
        $purchaseItem2 = PurchaseItemMother::create();
        $purchaseItem3 = PurchaseItemMother::create();

        $repository = $this->getRepository();
        $repository->putPurchaseItem($purchaseItem1);
        $repository->putPurchaseItem($purchaseItem2);
        $repository->putPurchaseItem($purchaseItem3);

        $count = $repository->countPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()
                ->filterByPurchaseId($purchaseItem2->getPurchaseId())
        );
        $this->assertEquals(1, $count);
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testPutPurchaseWithPurchaseItems(): void
    {
        $repository = $this->getRepository();

        // Create a purchase
        $purchase = PurchaseMother::create(
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 3000)
        );

        // Create multiple purchase items for this purchase
        $purchaseItem1 = PurchaseItemMother::create(
            purchaseId: $purchase->getId(),
            price: MoneyMother::create(amount: 1000)
        );
        $purchaseItem2 = PurchaseItemMother::create(
            purchaseId: $purchase->getId(),
            price: MoneyMother::create(amount: 2000)
        );

        // Create a collection and set it on the purchase
        $purchaseItemCollection = new PurchaseItemCollection([$purchaseItem1, $purchaseItem2]);
        $purchase->setPurchaseItems($purchaseItemCollection);

        // Save the purchase (this should save both purchase and purchase items)
        $repository->put($purchase);

        // Retrieve the purchase and verify it was saved correctly
        $foundPurchase = $repository->findOneBy(
            PurchaseCriteria::createEmpty()->filterById($purchase->getId())
        );

        $this->assertEquals($purchase->getId(), $foundPurchase->getId());
        $this->assertEquals($purchase->getUserId(), $foundPurchase->getUserId());
        $this->assertEquals($purchase->getStatus(), $foundPurchase->getStatus());
        $this->assertEquals($purchase->getAmount(), $foundPurchase->getAmount());

        // Verify that the purchase items were saved correctly
        $foundPurchaseItems = $repository->findPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()->filterByPurchaseId($purchase->getId())
        );

        $this->assertEquals(2, $foundPurchaseItems->count());

        // Verify individual purchase items
        $foundItem1 = $repository->findOnePurchaseItemBy(
            PurchaseItemCriteria::createById($purchaseItem1->getId())
        );
        $foundItem2 = $repository->findOnePurchaseItemBy(
            PurchaseItemCriteria::createById($purchaseItem2->getId())
        );

        $this->assertEquals($purchaseItem1->getId(), $foundItem1->getId());
        $this->assertEquals($purchaseItem1->getPurchaseId(), $foundItem1->getPurchaseId());
        $this->assertEquals($purchaseItem1->getPurchasableItemId(), $foundItem1->getPurchasableItemId());
        $this->assertEquals($purchaseItem1->getPrice(), $foundItem1->getPrice());

        $this->assertEquals($purchaseItem2->getId(), $foundItem2->getId());
        $this->assertEquals($purchaseItem2->getPurchaseId(), $foundItem2->getPurchaseId());
        $this->assertEquals($purchaseItem2->getPurchasableItemId(), $foundItem2->getPurchasableItemId());
        $this->assertEquals($purchaseItem2->getPrice(), $foundItem2->getPrice());

        // Verify that both purchase items belong to the same purchase
        $this->assertEquals($purchase->getId(), $foundItem1->getPurchaseId());
        $this->assertEquals($purchase->getId(), $foundItem2->getPurchaseId());
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws PurchaseItemNotFoundException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testDeletePurchaseItem(): void
    {
        $purchaseItem1 = PurchaseItemMother::create();
        $purchaseItem2 = PurchaseItemMother::create();
        $purchaseItem3 = PurchaseItemMother::create();

        $repository = $this->getRepository();
        $repository->putPurchaseItem($purchaseItem1);
        $repository->putPurchaseItem($purchaseItem2);
        $repository->putPurchaseItem($purchaseItem3);

        $repository->deletePurchaseItem($purchaseItem2);

        $this->expectException(PurchaseItemNotFoundException::class);
        $repository->findOnePurchaseItemBy(
            PurchaseItemCriteria::createEmpty()
                ->filterById($purchaseItem2->getId())
        );
    }
}
