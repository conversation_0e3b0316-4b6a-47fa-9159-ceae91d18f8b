<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class PurchasableItemRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): PurchasableItemRepository;

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     */
    public function testPut(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            resource: ResourceMother::create(id: new Id(3))
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createById($item1->getId())
        );
        $this->assertEquals($item1, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createById($item2->getId())
        );
        $this->assertEquals($item2, $found);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testFindOneBy(): void
    {
        $resource1 = ResourceMother::create(id: new Id(1));
        $resource2 = ResourceMother::create(id: new Id(2));
        $resource3 = ResourceMother::create(type: ResourceType::Subscription, id: UuidMother::create());

        $price1 = MoneyMother::create(amount: 1000);
        $price2 = MoneyMother::create(amount: 2000);

        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: $price1,
            resource: $resource1
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: $price2,
            resource: $resource2
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            price: $price1,
            resource: $resource3
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $this->assertEquals(
            $item2,
            $repository->findOneBy(
                PurchasableItemCriteria::createEmpty()
                    ->filterById($item2->getId())
            )
        );

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByResource($resource2)
        );
        $this->assertEquals($item2, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByResourceType($item3->getResource()->getType())
        );
        $this->assertEquals($item3, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice($price2)
        );
        $this->assertEquals($item2, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByMaxPrice($price1)
        );
        $this->assertTrue(
            $found->getId()->equals($item1->getId())
            || $found->getId()->equals($item3->getId()),
            'Found item should be either item1 or item3'
        );
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws PurchasableItemRepositoryException
     */
    public function testFindOneByThrowsExceptionWhenNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(PurchasableItemNotFoundException::class);
        $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById(UuidMother::create())
        );
    }

    /**
     * @throws PurchasableItemRepositoryException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        PurchasableItemCollection $input,
        PurchasableItemCriteria $criteria,
        int $expectedCount,
        array $expectedResults,
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $item) {
            $repository->put($item);
        }

        // First verify that all items were stored correctly
        $allItems = $repository->findBy(PurchasableItemCriteria::createEmpty());
        $this->assertCount($input->count(), $allItems);

        // Then test the specific criteria
        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);

        $expectedResultsIds = array_map(
            fn ($item) => $item->getId(),
            $expectedResults
        );

        $foundIds = array_map(
            fn ($item) => $item->getId(),
            $result->all()
        );

        $this->assertCount(
            0,
            array_diff($expectedResultsIds, $foundIds),
            'Expected results not found in results'
        );
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public static function provideFindBy(): \Generator
    {
        $resource1 = ResourceMother::create(id: new Id(1));
        $resource2 = ResourceMother::create(id: new Id(2));
        $resource3 = ResourceMother::create(id: new Id(3));
        $price1 = MoneyMother::create(amount: 1000);
        $price2 = MoneyMother::create(amount: 2000);
        $price3 = MoneyMother::create(amount: 3000);

        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: $price1,
            resource: $resource1
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: $price2,
            resource: $resource2
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            price: $price3,
            resource: $resource3
        );

        yield '3 items get all' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$item1, $item2, $item3],
        ];

        yield '3 items get item 2 by id' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterById($item2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$item2],
        ];

        yield '3 items get by resource 1' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByResource($resource1),
            'expectedCount' => 1,
            'expectedResults' => [$item1],
        ];

        yield '3 items get by min price 2000' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice($price2),
            'expectedCount' => 2,
            'expectedResults' => [$item2, $item3],
        ];

        yield '3 items get by max price 2000' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMaxPrice($price2),
            'expectedCount' => 2,
            'expectedResults' => [$item1, $item2],
        ];

        yield '3 items get by price range 1500-2500' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 1500))
                ->filterByMaxPrice(MoneyMother::create(amount: 2500)),
            'expectedCount' => 1,
            'expectedResults' => [$item2],
        ];

        $activeItem = PurchasableItemMother::create(
            name: 'Active Item',
            resource: ResourceMother::create(id: new Id(10)),
            isActive: true
        );
        $inactiveItem = PurchasableItemMother::create(
            name: 'Inactive Item',
            resource: ResourceMother::create(id: new Id(11)),
            isActive: false
        );

        yield '2 items get active only' => [
            'input' => new PurchasableItemCollection([$activeItem, $inactiveItem]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByIsActive(true),
            'expectedCount' => 1,
            'expectedResults' => [$activeItem],
        ];

        yield '2 items get inactive only' => [
            'input' => new PurchasableItemCollection([$activeItem, $inactiveItem]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByIsActive(false),
            'expectedCount' => 1,
            'expectedResults' => [$inactiveItem],
        ];
    }

    /**
     * @throws CriteriaException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testDelete(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            resource: ResourceMother::create(id: new Id(3))
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item2->getId())
        );
        $this->assertEquals($item2, $found);

        $repository->delete($item2);

        $this->expectException(PurchasableItemNotFoundException::class);
        $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item2->getId())
        );
    }

    /**
     * @throws CriteriaException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testDeleteDoesNotAffectOtherItems(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            resource: ResourceMother::create(id: new Id(3))
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $repository->delete($item2);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item1->getId())
        );
        $this->assertEquals($item1, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item3->getId())
        );
        $this->assertEquals($item3, $found);
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws PurchasableItemRepositoryException
     */
    public function testEnsureUniqueResource(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(
                type: ResourceType::Course,
                id: new Id(1)
            ),
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(
                type: ResourceType::Course,
                id: new Id(1)
            )
        );

        $this->expectException(PurchasableItemRepositoryException::class);
        $repository = $this->getRepository();
        $repository->put($item1);
        $repository->put($item2);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     */
    public function testIsActiveFieldPersistence(): void
    {
        $activeItem = PurchasableItemMother::create(
            name: 'Active Item',
            resource: ResourceMother::create(id: new Id(1000)),
            isActive: true
        );
        $inactiveItem = PurchasableItemMother::create(
            name: 'Inactive Item',
            resource: ResourceMother::create(id: new Id(3)),
            isActive: false
        );

        $repository = $this->getRepository();
        $repository->put($activeItem);
        $repository->put($inactiveItem);

        $foundActive = $repository->findOneBy(
            PurchasableItemCriteria::createById($activeItem->getId())
        );
        $this->assertTrue($foundActive->getIsActive());

        $foundInactive = $repository->findOneBy(
            PurchasableItemCriteria::createById($inactiveItem->getId())
        );
        $this->assertFalse($foundInactive->getIsActive());

        $foundActive->setIsActive(false);
        $repository->put($foundActive);

        $updatedItem = $repository->findOneBy(
            PurchasableItemCriteria::createById($activeItem->getId())
        );
        $this->assertFalse($updatedItem->getIsActive());
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws CriteriaException
     */
    public function testCountByWithEmptyRepository(): void
    {
        $repository = $this->getRepository();

        $count = $repository->countBy(PurchasableItemCriteria::createEmpty());

        $this->assertSame(0, $count, 'Empty repository should return count of 0');
        $this->assertIsInt($count, 'countBy should return an integer');
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testCountByBasicFunctionality(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            resource: ResourceMother::create(id: new Id(3))
        );

        $repository = $this->getRepository();
        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $totalCount = $repository->countBy(PurchasableItemCriteria::createEmpty());
        $this->assertSame(3, $totalCount, 'Should count all items');

        $countById = $repository->countBy(
            PurchasableItemCriteria::createById($item2->getId())
        );
        $this->assertSame(1, $countById, 'Should count single item by ID');
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testCountByConsistencyWithFindBy(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: MoneyMother::create(amount: 1000),
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: MoneyMother::create(amount: 2000),
            resource: ResourceMother::create(id: new Id(2))
        );

        $repository = $this->getRepository();
        $repository->put($item1);
        $repository->put($item2);

        $criteria = PurchasableItemCriteria::createEmpty()
            ->filterByMinPrice(MoneyMother::create(amount: 1500));

        $foundItems = $repository->findBy($criteria);
        $countResult = $repository->countBy($criteria);

        $this->assertSame(
            $foundItems->count(),
            $countResult,
            'countBy result should match findBy collection count'
        );
    }

    /**
     * @throws PurchasableItemRepositoryException
     */
    #[DataProvider('provideCountByCriteria')]
    public function testCountByWithVariousCriteria(
        PurchasableItemCollection $input,
        PurchasableItemCriteria $criteria,
        int $expectedCount,
        string $testDescription
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $item) {
            $repository->put($item);
        }

        $actualCount = $repository->countBy($criteria);

        $this->assertSame(
            $expectedCount,
            $actualCount,
            $testDescription
        );
        $this->assertIsInt($actualCount, 'countBy should always return an integer');
        $this->assertGreaterThanOrEqual(0, $actualCount, 'Count should never be negative');
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     */
    public static function provideCountByCriteria(): \Generator
    {
        $resource1 = ResourceMother::create(id: new Id(1));
        $resource2 = ResourceMother::create(id: new Id(2));
        $resource3 = ResourceMother::create(id: new Id(3));
        $price1 = MoneyMother::create(amount: 1000);
        $price2 = MoneyMother::create(amount: 2000);
        $price3 = MoneyMother::create(amount: 3000);

        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: $price1,
            resource: $resource1,
            isActive: true
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: $price2,
            resource: $resource2,
            isActive: false
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            price: $price3,
            resource: $resource3,
            isActive: true
        );

        yield 'count all items' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty(),
            'expectedCount' => 3,
            'testDescription' => 'Should count all items with empty criteria',
        ];

        yield 'count by specific ID' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createById($item2->getId()),
            'expectedCount' => 1,
            'testDescription' => 'Should count single item by ID',
        ];

        yield 'count by resource' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByResource($resource1),
            'expectedCount' => 1,
            'testDescription' => 'Should count items by resource',
        ];

        yield 'count by min price' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice($price2),
            'expectedCount' => 2,
            'testDescription' => 'Should count items with price >= 2000',
        ];

        yield 'count by max price' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMaxPrice($price2),
            'expectedCount' => 2,
            'testDescription' => 'Should count items with price <= 2000',
        ];

        yield 'count by price range' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 1500))
                ->filterByMaxPrice(MoneyMother::create(amount: 2500)),
            'expectedCount' => 1,
            'testDescription' => 'Should count items in price range 1500-2500',
        ];

        yield 'count active items only' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByIsActive(true),
            'expectedCount' => 2,
            'testDescription' => 'Should count only active items',
        ];

        yield 'count inactive items only' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByIsActive(false),
            'expectedCount' => 1,
            'testDescription' => 'Should count only inactive items',
        ];

        yield 'count with search filter' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('Item 2'),
            'expectedCount' => 1,
            'testDescription' => 'Should count items matching search term',
        ];

        yield 'count with no matching criteria' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 5000)),
            'expectedCount' => 0,
            'testDescription' => 'Should return 0 when no items match criteria',
        ];
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testCountByAfterItemDeletion(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );

        $repository = $this->getRepository();
        $repository->put($item1);
        $repository->put($item2);

        $countBefore = $repository->countBy(PurchasableItemCriteria::createEmpty());
        $this->assertSame(2, $countBefore, 'Should count 2 items before deletion');

        $repository->delete($item1);

        $countAfter = $repository->countBy(PurchasableItemCriteria::createEmpty());
        $this->assertSame(1, $countAfter, 'Should count 1 item after deletion');

        $countDeleted = $repository->countBy(
            PurchasableItemCriteria::createById($item1->getId())
        );
        $this->assertSame(0, $countDeleted, 'Deleted item should not be counted');
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testCountByAfterItemUpdate(): void
    {
        $item = PurchasableItemMother::create(
            name: 'Original Item',
            resource: ResourceMother::create(id: new Id(1)),
            isActive: false
        );

        $repository = $this->getRepository();
        $repository->put($item);

        $inactiveCount = $repository->countBy(
            PurchasableItemCriteria::createEmpty()->filterByIsActive(false)
        );
        $this->assertSame(1, $inactiveCount, 'Should count 1 inactive item');

        $activeCount = $repository->countBy(
            PurchasableItemCriteria::createEmpty()->filterByIsActive(true)
        );
        $this->assertSame(0, $activeCount, 'Should count 0 active items');

        // Update item to be active
        $item->setIsActive(true);
        $repository->put($item);

        $inactiveCountAfter = $repository->countBy(
            PurchasableItemCriteria::createEmpty()->filterByIsActive(false)
        );
        $this->assertSame(0, $inactiveCountAfter, 'Should count 0 inactive items after update');

        $activeCountAfter = $repository->countBy(
            PurchasableItemCriteria::createEmpty()->filterByIsActive(true)
        );
        $this->assertSame(1, $activeCountAfter, 'Should count 1 active item after update');
    }
}
