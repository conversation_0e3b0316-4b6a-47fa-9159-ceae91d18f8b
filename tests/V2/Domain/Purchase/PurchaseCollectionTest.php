<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

class PurchaseCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new PurchaseCollection($items);
    }

    protected function getExpectedType(): string
    {
        return Purchase::class;
    }

    /**
     * @throws InvalidUuidException
     */
    protected function getItem(): object
    {
        return PurchaseMother::create();
    }
}
