<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\V2\Domain\Purchase\PaginatedPurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\TestCase;

class PaginatedPurchaseCollectionTest extends TestCase
{
    /**
     * @throws InvalidUuidException
     * @throws CollectionException
     */
    public function testPaginatedCollection(): void
    {
        $collection = new PurchaseCollection([PurchaseMother::create()]);
        $paginatedCollection = new PaginatedPurchaseCollection($collection, 1);

        $this->assertSame($collection, $paginatedCollection->getCollection());
        $this->assertEquals(1, $paginatedCollection->getTotalItems());
    }

    public function testExceptionWithInvalidCollection(): void
    {
        $this->expectException(\TypeError::class);
        new PaginatedPurchaseCollection(new \stdClass(), 1);
    }
}
