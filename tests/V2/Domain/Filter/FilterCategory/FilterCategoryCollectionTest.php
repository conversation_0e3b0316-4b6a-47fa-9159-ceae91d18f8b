<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Filter\FilterCategory;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Filter\FilterCategoryMother;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class FilterCategoryCollectionTest extends CollectionTestCase
{
    protected function getExpectedType(): string
    {
        return FilterCategory::class;
    }

    protected function getItem(): object
    {
        return FilterCategoryMother::create();
    }

    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new FilterCategoryCollection($items);
    }
}
