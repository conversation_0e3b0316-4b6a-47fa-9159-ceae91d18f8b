<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Filter\FilterCategory;

use App\Tests\V2\Mother\Filter\FilterCategoryMother;
use App\V2\Domain\Filter\Exception\FilterCategoryNotFoundException;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use PHPUnit\Framework\TestCase;

abstract class FilterCategoryRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): FilterCategoryRepository;

    /**
     * Repositories are read-only, so a method to add categories is required.
     */
    abstract protected function addFilterCategory(FilterCategory $category): void;

    /**
     * @throws InfrastructureException
     * @throws FilterCategoryNotFoundException
     * @throws CriteriaException
     */
    public function testFindOneBy(): void
    {
        $repository = $this->getRepository();

        $category1 = FilterCategoryMother::create(
            id: new Id(1),
            name: 'Category 1',
            sort: 2,
        );

        $category2 = FilterCategoryMother::create(
            id: new Id(2),
            name: 'Category 2',
            sort: 0,
        );

        $category3 = FilterCategoryMother::create(
            id: new Id(3),
            parentId: new Id(2),
            name: 'Category 3',
            sort: 1,
        );

        $this->addFilterCategory($category1);
        $this->addFilterCategory($category2);
        $this->addFilterCategory($category3);

        $this->assertEquals(
            $category1,
            $repository->findOneBy(
                FilterCategoryCriteria::createById(new Id(1))
            )
        );

        $this->assertEquals(
            $category2,
            $repository->findOneBy(
                FilterCategoryCriteria::createEmpty()
            )
        );

        $this->assertEquals(
            $category1,
            $repository->findOneBy(
                FilterCategoryCriteria::createEmpty()
                    ->filterByName('1')
            )
        );

        $this->assertEquals(
            $category3,
            $repository->findOneBy(
                FilterCategoryCriteria::createEmpty()
                    ->filterByParentId(new Id(2))
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws FilterCategoryNotFoundException
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testFindBy(): void
    {
        $repository = $this->getRepository();

        $category1 = FilterCategoryMother::create(
            id: new Id(1),
            name: 'Category 1',
            sort: 2,
        );

        $category2 = FilterCategoryMother::create(
            id: new Id(2),
            name: 'Category 2',
            sort: 0,
        );

        $category3 = FilterCategoryMother::create(
            id: new Id(3),
            name: 'Category 3',
            sort: 1,
        );

        $this->addFilterCategory($category1);
        $this->addFilterCategory($category2);
        $this->addFilterCategory($category3);

        $result = $repository->findBy(
            FilterCategoryCriteria::createEmpty()
        );
        $this->assertCount(3, $result);
        $this->assertEquals(2, $result->first()->getId()->value());
        $this->assertEquals(1, $result->last()->getId()->value());

        $result = $repository->findBy(
            FilterCategoryCriteria::createEmpty()->filterByName('Category')
        );
        $this->assertCount(3, $result);
        $this->assertEquals(2, $result->first()->getId()->value());
        $this->assertEquals(1, $result->last()->getId()->value());

        $result = $repository->findBy(
            FilterCategoryCriteria::createByIds(
                new IdCollection([new Id(1), new Id(3)])
            )
        );
        $this->assertCount(2, $result);
        $this->assertEquals(3, $result->first()->getId()->value());
        $this->assertEquals(1, $result->last()->getId()->value());

        $result = $repository->findBy(
            FilterCategoryCriteria::createByIds(
                new IdCollection([new Id(1), new Id(3)])
            )->filterByName('Category 3')
        );
        $this->assertCount(1, $result);
        $this->assertEquals($category3, $result->first());
    }
}
