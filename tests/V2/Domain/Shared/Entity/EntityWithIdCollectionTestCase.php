<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Entity;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Entity\EntityWithIdCollection;
use App\V2\Domain\Shared\Entity\EntityWithIdCollectionException;

abstract class EntityWithIdCollectionTestCase extends CollectionTestCase
{
    public function testAllIndexedById(): void
    {
        $item1 = $this->getItem();
        $item2 = $this->getItem();
        $item3 = $this->getItem();

        /** @var EntityWithIdCollection<EntityWithId> $collection */
        $collection = $this->createCollection([
            $item1,
            $item2,
            $item3,
        ]);

        $collectionIndexedById = $collection->allIndexedById();

        $this->assertCount(3, $collectionIndexedById);
        $this->assertSame($item1, $collectionIndexedById[$item1->getId()->value()]);
        $this->assertSame($item2, $collectionIndexedById[$item2->getId()->value()]);
        $this->assertSame($item3, $collectionIndexedById[$item3->getId()->value()]);
    }

    /**
     * @throws CollectionException
     * @throws EntityWithIdCollectionException
     */
    public function testAllIds(): void
    {
        $item1 = $this->getItem();
        $item2 = $this->getItem();
        $item3 = $this->getItem();

        /** @var EntityWithIdCollection<EntityWithId> $collection */
        $collection = $this->createCollection([
            $item1,
            $item2,
            $item3,
        ]);

        $ids = $collection->allIds();

        $this->assertCount(3, $ids);
        $this->assertContains($item1->getId(), $ids);
        $this->assertContains($item2->getId(), $ids);
        $this->assertContains($item3->getId(), $ids);
    }
}
