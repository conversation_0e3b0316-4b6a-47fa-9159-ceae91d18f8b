<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Purchase;

use App\Tests\V2\Mother\Purchase\PurchasableItemCollectionMother;
use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\CurrencyMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Financial\TaxRateMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Purchase\PurchaseFactory;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PurchaseFactoryTest extends TestCase
{
    private UuidGenerator $uuidGenerator;
    private PurchaseFactory $purchaseFactory;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->uuidGenerator = $this->createMock(UuidGenerator::class);
        $this->purchaseFactory = new PurchaseFactory($this->uuidGenerator);
    }

    /**
     * @throws Exception
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public function testCreateFromPurchasableItemsWithValidInputs(): void
    {
        // Arrange
        $userId = IdMother::create();
        $currency = CurrencyMother::eur();
        $taxRate = TaxRateMother::create(0.21);

        $item1Price = MoneyMother::create(amount: 1000, currency: $currency);
        $item2Price = MoneyMother::create(amount: 2000, currency: $currency);

        $purchasableItem1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: $item1Price
        );

        $purchasableItem2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: $item2Price
        );

        $purchasableItems = new PurchasableItemCollection([
            $purchasableItem1,
            $purchasableItem2,
        ]);

        $expectedTotalAmount = $item1Price->value() + $item2Price->value();
        $expectedTaxAmount = (int) round($expectedTotalAmount * $taxRate->value());

        $purchaseId = UuidMother::create();
        $purchaseItemId1 = UuidMother::create();
        $purchaseItemId2 = UuidMother::create();

        // Configure mock
        $this->uuidGenerator->expects($this->exactly(3))
            ->method('generate')
            ->willReturnOnConsecutiveCalls(
                $purchaseId,
                $purchaseItemId1,
                $purchaseItemId2
            );

        // Act
        $purchase = $this->purchaseFactory->createFromPurchasableItems(
            userId: $userId,
            purchasableItems: $purchasableItems,
            currency: $currency,
            taxRate: $taxRate
        );

        // Assert
        $this->assertInstanceOf(Purchase::class, $purchase);
        $this->assertEquals($purchaseId, $purchase->getId());
        $this->assertEquals($userId, $purchase->getUserId());
        $this->assertEquals(PurchaseStatus::Pending, $purchase->getStatus());
        $this->assertEquals($expectedTotalAmount, $purchase->getAmount()->value());
        $this->assertEquals($currency, $purchase->getAmount()->currency());
        $this->assertEquals($taxRate, $purchase->getTaxRate());
        $this->assertEquals($expectedTaxAmount, $purchase->getTaxAmount()->value());
        $this->assertEquals($currency, $purchase->getTaxAmount()->currency());

        $purchaseItems = $purchase->getPurchaseItems();
        $this->assertCount(2, $purchaseItems->all());

        $purchaseItemsArray = $purchaseItems->all();
        $this->assertEquals($purchaseItemId1, $purchaseItemsArray[0]->getId());
        $this->assertEquals($purchaseId, $purchaseItemsArray[0]->getPurchaseId());
        $this->assertEquals($purchasableItem1->getId(), $purchaseItemsArray[0]->getPurchasableItemId());
        $this->assertEquals($item1Price, $purchaseItemsArray[0]->getPrice());

        $this->assertEquals($purchaseItemId2, $purchaseItemsArray[1]->getId());
        $this->assertEquals($purchaseId, $purchaseItemsArray[1]->getPurchaseId());
        $this->assertEquals($purchasableItem2->getId(), $purchaseItemsArray[1]->getPurchasableItemId());
        $this->assertEquals($item2Price, $purchaseItemsArray[1]->getPrice());
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws \Exception
     */
    public function testCreateFromPurchasableItemsWithCurrencyMismatch(): void
    {
        // Arrange
        $userId = IdMother::create();
        $eurCurrency = CurrencyMother::eur();
        $usdCurrency = CurrencyMother::usd();
        $taxRate = TaxRateMother::create();

        $item1 = PurchasableItemMother::create(
            price: MoneyMother::create(currency: $eurCurrency)
        );

        $item2 = PurchasableItemMother::create(
            price: MoneyMother::create(currency: $usdCurrency)
        );

        $purchasableItems = new PurchasableItemCollection([$item1, $item2]);

        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Currency mismatch');

        // Act
        $this->purchaseFactory->createFromPurchasableItems(
            userId: $userId,
            purchasableItems: $purchasableItems,
            currency: $eurCurrency,
            taxRate: $taxRate
        );
    }

    /**
     * @throws Exception
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public function testCreateFromPurchasableItemsWithEmptyCollection(): void
    {
        // Arrange
        $userId = IdMother::create();
        $currency = CurrencyMother::eur();
        $taxRate = TaxRateMother::create();
        $purchasableItems = PurchasableItemCollectionMother::empty();

        $purchaseId = UuidMother::create();

        // Configure mock
        $this->uuidGenerator->expects($this->once())
            ->method('generate')
            ->willReturn($purchaseId);

        // Act
        $purchase = $this->purchaseFactory->createFromPurchasableItems(
            userId: $userId,
            purchasableItems: $purchasableItems,
            currency: $currency,
            taxRate: $taxRate
        );

        // Assert
        $this->assertInstanceOf(Purchase::class, $purchase);
        $this->assertEquals($purchaseId, $purchase->getId());
        $this->assertEquals($userId, $purchase->getUserId());
        $this->assertEquals(PurchaseStatus::Pending, $purchase->getStatus());
        $this->assertEquals(0, $purchase->getAmount()->value());
        $this->assertEquals($currency, $purchase->getAmount()->currency());
        $this->assertEquals($taxRate, $purchase->getTaxRate());
        $this->assertEquals(0, $purchase->getTaxAmount()->value());
        $this->assertEquals($currency, $purchase->getTaxAmount()->currency());

        $purchaseItems = $purchase->getPurchaseItems();
        $this->assertCount(0, $purchaseItems->all());
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     */
    public function testCreatePurchaseItemFromPurchasableItem(): void
    {
        // Arrange
        $purchaseId = UuidMother::create();
        $currency = CurrencyMother::eur();
        $price = MoneyMother::create(amount: 1500, currency: $currency);
        $purchasableItem = PurchasableItemMother::create(price: $price);

        $purchaseItemId = UuidMother::create();

        // Configure mock
        $this->uuidGenerator->expects($this->once())
            ->method('generate')
            ->willReturn($purchaseItemId);

        // Act
        $purchaseItem = $this->purchaseFactory->createPurchaseItemFromPurchasableItem(
            purchaseId: $purchaseId,
            purchasableItem: $purchasableItem
        );

        // Assert
        $this->assertInstanceOf(PurchaseItem::class, $purchaseItem);
        $this->assertEquals($purchaseItemId, $purchaseItem->getId());
        $this->assertEquals($purchaseId, $purchaseItem->getPurchaseId());
        $this->assertEquals($purchasableItem->getId(), $purchaseItem->getPurchasableItemId());
        $this->assertEquals($price, $purchaseItem->getPrice());
    }
}
