<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Resource;

use App\Entity\Course;
use App\Repository\CourseRepository;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceProvider;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Application\Resource\CourseResourceProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class CourseResourceProviderTest extends TestCase
{
    private CourseRepository $courseRepository;
    private CourseResourceProvider $courseResourceProvider;

    protected function setUp(): void
    {
        parent::setUp();

        $this->courseRepository = $this->createMock(CourseRepository::class);
        $this->courseResourceProvider = new CourseResourceProvider($this->courseRepository);
    }

    public function testCanBeInstantiated(): void
    {
        $this->assertInstanceOf(CourseResourceProvider::class, $this->courseResourceProvider);
        $this->assertInstanceOf(ResourceProvider::class, $this->courseResourceProvider);
    }

    public function testRequiredMethodsExist(): void
    {
        $this->assertTrue(
            method_exists(CourseResourceProvider::class, 'supports'),
            'supports method should exist'
        );

        $this->assertTrue(
            method_exists(CourseResourceProvider::class, 'getEntity'),
            'getEntity method should exist'
        );

        $this->assertTrue(
            \is_callable([$this->courseResourceProvider, 'supports']),
            'supports method should be callable'
        );

        $this->assertTrue(
            \is_callable([$this->courseResourceProvider, 'getEntity']),
            'getEntity method should be callable'
        );
    }

    public function testImplementsResourceProviderInterface(): void
    {
        $this->assertInstanceOf(ResourceProvider::class, $this->courseResourceProvider);
    }

    public function testSupportsReturnsTrueForCourseResourceType(): void
    {
        $result = $this->courseResourceProvider->supports(ResourceType::Course);

        $this->assertTrue($result);
    }

    #[DataProvider('nonCourseResourceTypeProvider')]
    public function testSupportsReturnsFalseForNonCourseResourceTypes(ResourceType $resourceType): void
    {
        $result = $this->courseResourceProvider->supports($resourceType);

        $this->assertFalse($result);
    }

    /**
     * @throws Exception
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testGetEntityReturnsCourseWhenFound(): void
    {
        $courseId = IdMother::create(123);
        $resource = new Resource(ResourceType::Course, $courseId);
        $expectedCourse = $this->createMock(Course::class);

        $this->courseRepository->expects($this->once())
            ->method('find')
            ->with($courseId->value())
            ->willReturn($expectedCourse);

        $result = $this->courseResourceProvider->getEntity($resource);

        $this->assertSame($expectedCourse, $result);
    }

    /**
     * @throws \Exception
     */
    public function testGetEntityThrowsExceptionWhenCourseNotFound(): void
    {
        $courseId = IdMother::create(456);
        $resource = new Resource(ResourceType::Course, $courseId);

        $this->courseRepository->expects($this->once())
            ->method('find')
            ->with($courseId->value())
            ->willReturn(null);

        $this->expectException(ResourceNotFoundException::class);
        $this->expectExceptionMessage('Resource of type Course with ID 456 not found');

        $this->courseResourceProvider->getEntity($resource);
    }

    /**
     * @throws Exception
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    #[DataProvider('courseIdProvider')]
    public function testGetEntityWithDifferentCourseIds(int $idValue): void
    {
        $courseId = IdMother::create($idValue);
        $resource = new Resource(ResourceType::Course, $courseId);
        $expectedCourse = $this->createMock(Course::class);

        $this->courseRepository->expects($this->once())
            ->method('find')
            ->with($courseId->value())
            ->willReturn($expectedCourse);

        $result = $this->courseResourceProvider->getEntity($resource);

        $this->assertSame($expectedCourse, $result);
        $this->assertInstanceOf(Course::class, $result);
    }

    public function testSupportsConsistency(): void
    {
        $result1 = $this->courseResourceProvider->supports(ResourceType::Course);
        $result2 = $this->courseResourceProvider->supports(ResourceType::Course);

        $this->assertSame($result1, $result2);
        $this->assertTrue($result1);
    }

    /**
     * @throws Exception
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testGetEntityConsistency(): void
    {
        $courseId = IdMother::create(789);
        $resource = new Resource(ResourceType::Course, $courseId);
        $expectedCourse = $this->createMock(Course::class);

        $this->courseRepository->expects($this->exactly(2))
            ->method('find')
            ->with($courseId->value())
            ->willReturn($expectedCourse);

        $result1 = $this->courseResourceProvider->getEntity($resource);
        $result2 = $this->courseResourceProvider->getEntity($resource);

        $this->assertSame($result1, $result2);
        $this->assertSame($expectedCourse, $result1);
    }

    /**
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testGetEntityWithRepositoryException(): void
    {
        $courseId = IdMother::create(999);
        $resource = new Resource(ResourceType::Course, $courseId);
        $expectedException = new \RuntimeException('Database error');

        $this->courseRepository->expects($this->once())
            ->method('find')
            ->with($courseId->value())
            ->willThrowException($expectedException);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Database error');

        $this->courseResourceProvider->getEntity($resource);
    }

    /**
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testGetEntityWithZeroId(): void
    {
        $courseId = IdMother::create(0);
        $resource = new Resource(ResourceType::Course, $courseId);

        $this->courseRepository->expects($this->once())
            ->method('find')
            ->with($courseId->value())
            ->willReturn(null);

        $this->expectException(ResourceNotFoundException::class);
        $this->expectExceptionMessage('Resource of type Course with ID 0 not found');

        $this->courseResourceProvider->getEntity($resource);
    }

    /**
     * @throws Exception
     * @throws \Exception
     * @throws ResourceNotFoundException
     */
    public function testIntegrationContext(): void
    {
        $courseId = IdMother::create(100);
        $resource = new Resource(ResourceType::Course, $courseId);
        $course = $this->createMock(Course::class);

        $this->courseRepository->method('find')
            ->with($courseId->value())
            ->willReturn($course);

        $this->assertTrue($this->courseResourceProvider->supports(ResourceType::Course));
        $this->assertFalse($this->courseResourceProvider->supports(ResourceType::Subscription));

        $retrievedCourse = $this->courseResourceProvider->getEntity($resource);
        $this->assertSame($course, $retrievedCourse);

        $this->assertSame(ResourceType::Course, $resource->getType());
        $this->assertSame($courseId, $resource->getId());
    }

    public static function nonCourseResourceTypeProvider(): array
    {
        return [
            'subscription resource type' => [ResourceType::Subscription],
        ];
    }

    public static function courseIdProvider(): array
    {
        return [
            'small ID' => [1],
            'medium ID' => [123],
            'large ID' => [999999],
            'maximum integer' => [PHP_INT_MAX],
        ];
    }
}
