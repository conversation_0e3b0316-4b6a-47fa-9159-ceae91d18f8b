<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Resource;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\Tests\V2\Mother\Subscription\SubscriptionMother;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceProvider;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Subscription\Exception\SubscriptionNotFoundException;
use App\V2\Domain\Subscription\Exception\SubscriptionRepositoryException;
use App\V2\Domain\Subscription\Subscription;
use App\V2\Domain\Subscription\SubscriptionCriteria;
use App\V2\Domain\Subscription\SubscriptionRepository;
use App\V2\Application\Resource\SubscriptionResourceProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class SubscriptionResourceProviderTest extends TestCase
{
    private SubscriptionRepository $subscriptionRepository;
    private SubscriptionResourceProvider $subscriptionResourceProvider;

    protected function setUp(): void
    {
        parent::setUp();

        $this->subscriptionRepository = $this->createMock(SubscriptionRepository::class);
        $this->subscriptionResourceProvider = new SubscriptionResourceProvider($this->subscriptionRepository);
    }

    public function testCanBeInstantiated(): void
    {
        $this->assertInstanceOf(SubscriptionResourceProvider::class, $this->subscriptionResourceProvider);
        $this->assertInstanceOf(ResourceProvider::class, $this->subscriptionResourceProvider);
    }

    public function testRequiredMethodsExist(): void
    {
        $this->assertTrue(
            method_exists(SubscriptionResourceProvider::class, 'supports'),
            'supports method should exist'
        );

        $this->assertTrue(
            method_exists(SubscriptionResourceProvider::class, 'getEntity'),
            'getEntity method should exist'
        );

        $this->assertTrue(
            \is_callable([$this->subscriptionResourceProvider, 'supports']),
            'supports method should be callable'
        );

        $this->assertTrue(
            \is_callable([$this->subscriptionResourceProvider, 'getEntity']),
            'getEntity method should be callable'
        );
    }

    public function testImplementsResourceProviderInterface(): void
    {
        $this->assertInstanceOf(ResourceProvider::class, $this->subscriptionResourceProvider);
    }

    public function testSupportsReturnsTrueForSubscriptionResourceType(): void
    {
        $result = $this->subscriptionResourceProvider->supports(ResourceType::Subscription);

        $this->assertTrue($result);
    }

    #[DataProvider('nonSubscriptionResourceTypeProvider')]
    public function testSupportsReturnsFalseForNonSubscriptionResourceTypes(ResourceType $resourceType): void
    {
        $result = $this->subscriptionResourceProvider->supports($resourceType);

        $this->assertFalse($result);
    }

    public function testGetEntityReturnsSubscriptionWhenFound(): void
    {
        $subscriptionId = UuidMother::create();
        $resource = new Resource(ResourceType::Subscription, $subscriptionId);
        $expectedSubscription = SubscriptionMother::create(id: $subscriptionId);

        $this->subscriptionRepository->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (SubscriptionCriteria $criteria) use ($subscriptionId) {
                return null !== $criteria->getId() && $criteria->getId()->equals($subscriptionId);
            }))
            ->willReturn($expectedSubscription);

        $result = $this->subscriptionResourceProvider->getEntity($resource);

        $this->assertSame($expectedSubscription, $result);
        $this->assertInstanceOf(Subscription::class, $result);
    }

    public function testGetEntityThrowsExceptionWhenSubscriptionNotFound(): void
    {
        $subscriptionId = UuidMother::create();
        $resource = new Resource(ResourceType::Subscription, $subscriptionId);

        $this->subscriptionRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new SubscriptionNotFoundException());

        $this->expectException(ResourceNotFoundException::class);
        $this->expectExceptionMessage(\sprintf(
            'Resource of type Subscription with ID %s not found',
            $subscriptionId->value()
        ));

        $this->subscriptionResourceProvider->getEntity($resource);
    }

    #[DataProvider('subscriptionUuidProvider')]
    public function testGetEntityWithDifferentSubscriptionUuids(string $uuidValue): void
    {
        $subscriptionId = new \App\V2\Domain\Shared\Uuid\Uuid($uuidValue);
        $resource = new Resource(ResourceType::Subscription, $subscriptionId);
        $expectedSubscription = SubscriptionMother::create(id: $subscriptionId);

        $this->subscriptionRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($expectedSubscription);

        $result = $this->subscriptionResourceProvider->getEntity($resource);

        $this->assertSame($expectedSubscription, $result);
        $this->assertInstanceOf(Subscription::class, $result);
    }

    public function testSupportsConsistency(): void
    {
        $result1 = $this->subscriptionResourceProvider->supports(ResourceType::Subscription);
        $result2 = $this->subscriptionResourceProvider->supports(ResourceType::Subscription);

        $this->assertSame($result1, $result2);
        $this->assertTrue($result1);
    }

    public function testGetEntityConsistency(): void
    {
        $subscriptionId = UuidMother::create();
        $resource = new Resource(ResourceType::Subscription, $subscriptionId);
        $expectedSubscription = SubscriptionMother::create(id: $subscriptionId);

        $this->subscriptionRepository->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn($expectedSubscription);

        $result1 = $this->subscriptionResourceProvider->getEntity($resource);
        $result2 = $this->subscriptionResourceProvider->getEntity($resource);

        $this->assertSame($result1, $result2);
        $this->assertSame($expectedSubscription, $result1);
    }

    // ========================================
    public function testGetEntityWithRepositoryException(): void
    {
        $subscriptionId = UuidMother::create();
        $resource = new Resource(ResourceType::Subscription, $subscriptionId);
        $expectedException = new SubscriptionRepositoryException('Database error');

        $this->subscriptionRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException($expectedException);

        $this->expectException(SubscriptionRepositoryException::class);
        $this->expectExceptionMessage('Database error');

        $this->subscriptionResourceProvider->getEntity($resource);
    }

    public function testGetEntityWithCriteriaException(): void
    {
        $subscriptionId = UuidMother::create();
        $resource = new Resource(ResourceType::Subscription, $subscriptionId);
        $expectedException = new CriteriaException('Criteria error');

        $this->subscriptionRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException($expectedException);

        $this->expectException(CriteriaException::class);
        $this->expectExceptionMessage('Criteria error');

        $this->subscriptionResourceProvider->getEntity($resource);
    }

    public function testIntegrationContext(): void
    {
        $subscriptionId = UuidMother::create();
        $resource = new Resource(ResourceType::Subscription, $subscriptionId);
        $subscription = SubscriptionMother::create(id: $subscriptionId);

        $this->subscriptionRepository->method('findOneBy')
            ->willReturn($subscription);

        // Test the provider supports Subscription type
        $this->assertTrue($this->subscriptionResourceProvider->supports(ResourceType::Subscription));
        $this->assertFalse($this->subscriptionResourceProvider->supports(ResourceType::Course));

        // Test entity retrieval
        $retrievedSubscription = $this->subscriptionResourceProvider->getEntity($resource);
        $this->assertSame($subscription, $retrievedSubscription);

        // Test resource type handling
        $this->assertSame(ResourceType::Subscription, $resource->getType());
        $this->assertSame($subscriptionId, $resource->getId());
    }

    public static function nonSubscriptionResourceTypeProvider(): array
    {
        return [
            'course resource type' => [ResourceType::Course],
        ];
    }

    public static function subscriptionUuidProvider(): array
    {
        return [
            'valid UUID 1' => ['550e8400-e29b-41d4-a716-************'],
            'valid UUID 2' => ['6ba7b810-9dad-11d1-80b4-00c04fd430c8'],
            'valid UUID 3' => ['6ba7b811-9dad-11d1-80b4-00c04fd430c8'],
        ];
    }
}
