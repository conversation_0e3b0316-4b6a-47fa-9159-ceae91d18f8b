<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Resource;

use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Resource\CompositeResourceProvider;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceProvider;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class CompositeResourceProviderTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCanBeInstantiatedWithProviders(): void
    {
        $provider1 = $this->createMock(ResourceProvider::class);
        $provider2 = $this->createMock(ResourceProvider::class);
        $providers = [$provider1, $provider2];

        $compositeProvider = new CompositeResourceProvider($providers);

        $this->assertInstanceOf(CompositeResourceProvider::class, $compositeProvider);
        $this->assertInstanceOf(ResourceProvider::class, $compositeProvider);
    }

    public function testCanBeInstantiatedWithEmptyProviders(): void
    {
        $compositeProvider = new CompositeResourceProvider([]);

        $this->assertInstanceOf(CompositeResourceProvider::class, $compositeProvider);
    }

    public function testRequiredMethodsExist(): void
    {
        $compositeProvider = new CompositeResourceProvider([]);

        $this->assertTrue(
            method_exists(CompositeResourceProvider::class, 'supports'),
            'supports method should exist'
        );

        $this->assertTrue(
            method_exists(CompositeResourceProvider::class, 'getEntity'),
            'getEntity method should exist'
        );

        $this->assertTrue(
            \is_callable([$compositeProvider, 'supports']),
            'supports method should be callable'
        );

        $this->assertTrue(
            \is_callable([$compositeProvider, 'getEntity']),
            'getEntity method should be callable'
        );
    }

    public function testImplementsResourceProviderInterface(): void
    {
        $compositeProvider = new CompositeResourceProvider([]);

        $this->assertInstanceOf(ResourceProvider::class, $compositeProvider);
    }

    /**
     * @throws Exception
     */
    #[DataProvider('supportsResourceTypeProvider')]
    public function testSupportsReturnsTrueWhenProviderSupportsResourceType(
        ResourceType $resourceType,
        bool $provider1Supports,
        bool $provider2Supports,
        bool $expectedResult,
    ): void {
        $provider1 = $this->createMock(ResourceProvider::class);
        $provider1->expects($this->once())
            ->method('supports')
            ->with($resourceType)
            ->willReturn($provider1Supports);

        $provider2 = $this->createMock(ResourceProvider::class);
        $provider2->expects($provider1Supports ? $this->never() : $this->once())
            ->method('supports')
            ->with($resourceType)
            ->willReturn($provider2Supports);

        $compositeProvider = new CompositeResourceProvider([$provider1, $provider2]);

        $result = $compositeProvider->supports($resourceType);

        $this->assertSame($expectedResult, $result);
    }

    /**
     * @throws Exception
     */
    public function testSupportsReturnsFalseWhenNoProvidersSupport(): void
    {
        $provider1 = $this->createMock(ResourceProvider::class);
        $provider1->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(false);

        $provider2 = $this->createMock(ResourceProvider::class);
        $provider2->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(false);

        $compositeProvider = new CompositeResourceProvider([$provider1, $provider2]);

        $result = $compositeProvider->supports(ResourceType::Course);

        $this->assertFalse($result);
    }

    public function testSupportsReturnsFalseWhenNoProviders(): void
    {
        $compositeProvider = new CompositeResourceProvider([]);

        $result = $compositeProvider->supports(ResourceType::Course);

        $this->assertFalse($result);
    }

    /**
     * @throws Exception
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testGetEntityDelegatesToFirstSupportingProvider(): void
    {
        $resource = new Resource(ResourceType::Course, IdMother::create(123));
        $expectedEntity = new \stdClass();

        $provider1 = $this->createMock(ResourceProvider::class);
        $provider1->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(true);
        $provider1->expects($this->once())
            ->method('getEntity')
            ->with($resource)
            ->willReturn($expectedEntity);

        $provider2 = $this->createMock(ResourceProvider::class);
        $provider2->expects($this->never())
            ->method('supports');
        $provider2->expects($this->never())
            ->method('getEntity');

        $compositeProvider = new CompositeResourceProvider([$provider1, $provider2]);

        $result = $compositeProvider->getEntity($resource);

        $this->assertSame($expectedEntity, $result);
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     * @throws ResourceNotFoundException
     */
    public function testGetEntitySkipsNonSupportingProviders(): void
    {
        $resource = new Resource(ResourceType::Subscription, UuidMother::create());
        $expectedEntity = new \stdClass();

        $provider1 = $this->createMock(ResourceProvider::class);
        $provider1->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Subscription)
            ->willReturn(false);
        $provider1->expects($this->never())
            ->method('getEntity');

        $provider2 = $this->createMock(ResourceProvider::class);
        $provider2->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Subscription)
            ->willReturn(true);
        $provider2->expects($this->once())
            ->method('getEntity')
            ->with($resource)
            ->willReturn($expectedEntity);

        $compositeProvider = new CompositeResourceProvider([$provider1, $provider2]);

        $result = $compositeProvider->getEntity($resource);

        $this->assertSame($expectedEntity, $result);
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    public function testGetEntityThrowsExceptionWhenNoProviderSupports(): void
    {
        $resource = new Resource(ResourceType::Course, IdMother::create(123));

        $provider1 = $this->createMock(ResourceProvider::class);
        $provider1->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(false);

        $provider2 = $this->createMock(ResourceProvider::class);
        $provider2->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(false);

        $compositeProvider = new CompositeResourceProvider([$provider1, $provider2]);

        $this->expectException(ResourceNotFoundException::class);
        $this->expectExceptionMessage('Resource of type Course with ID 123 not found');

        $compositeProvider->getEntity($resource);
    }

    /**
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testGetEntityThrowsExceptionWhenNoProviders(): void
    {
        $resource = new Resource(ResourceType::Course, IdMother::create(456));
        $compositeProvider = new CompositeResourceProvider([]);

        $this->expectException(ResourceNotFoundException::class);
        $this->expectExceptionMessage('Resource of type Course with ID 456 not found');

        $compositeProvider->getEntity($resource);
    }

    /**
     * @throws Exception
     */
    public function testSupportsConsistency(): void
    {
        $provider = $this->createMock(ResourceProvider::class);
        $provider->expects($this->exactly(2))
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(true);

        $compositeProvider = new CompositeResourceProvider([$provider]);

        $result1 = $compositeProvider->supports(ResourceType::Course);
        $result2 = $compositeProvider->supports(ResourceType::Course);

        $this->assertSame($result1, $result2);
        $this->assertTrue($result1);
    }

    /**
     * @throws Exception
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testGetEntityConsistency(): void
    {
        $resource = new Resource(ResourceType::Course, IdMother::create(789));
        $expectedEntity = new \stdClass();

        $provider = $this->createMock(ResourceProvider::class);
        $provider->expects($this->exactly(2))
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(true);
        $provider->expects($this->exactly(2))
            ->method('getEntity')
            ->with($resource)
            ->willReturn($expectedEntity);

        $compositeProvider = new CompositeResourceProvider([$provider]);

        $result1 = $compositeProvider->getEntity($resource);
        $result2 = $compositeProvider->getEntity($resource);

        $this->assertSame($result1, $result2);
        $this->assertSame($expectedEntity, $result1);
    }

    /**
     * @throws Exception
     */
    public function testWorksWithIterableProviders(): void
    {
        $provider = $this->createMock(ResourceProvider::class);
        $provider->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(true);

        $providers = new \ArrayIterator([$provider]);
        $compositeProvider = new CompositeResourceProvider($providers);

        $result = $compositeProvider->supports(ResourceType::Course);

        $this->assertTrue($result);
    }

    /**
     * @throws Exception
     * @throws ResourceNotFoundException
     * @throws \Exception
     */
    public function testProviderExceptionsArePropagated(): void
    {
        $resource = new Resource(ResourceType::Course, IdMother::create(999));
        $expectedException = new \RuntimeException('Provider error');

        $provider = $this->createMock(ResourceProvider::class);
        $provider->expects($this->once())
            ->method('supports')
            ->with(ResourceType::Course)
            ->willReturn(true);
        $provider->expects($this->once())
            ->method('getEntity')
            ->with($resource)
            ->willThrowException($expectedException);

        $compositeProvider = new CompositeResourceProvider([$provider]);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Provider error');

        $compositeProvider->getEntity($resource);
    }

    /**
     * @throws Exception
     * @throws ResourceNotFoundException
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public function testIntegrationContext(): void
    {
        $courseResource = new Resource(ResourceType::Course, IdMother::create(100));
        $subscriptionResource = new Resource(ResourceType::Subscription, UuidMother::create());

        $courseEntity = new \stdClass();
        $subscriptionEntity = new \stdClass();

        $courseProvider = $this->createMock(ResourceProvider::class);
        $courseProvider->method('supports')
            ->willReturnCallback(fn (ResourceType $type) => ResourceType::Course === $type);
        $courseProvider->method('getEntity')
            ->with($courseResource)
            ->willReturn($courseEntity);

        $subscriptionProvider = $this->createMock(ResourceProvider::class);
        $subscriptionProvider->method('supports')
            ->willReturnCallback(fn (ResourceType $type) => ResourceType::Subscription === $type);
        $subscriptionProvider->method('getEntity')
            ->with($subscriptionResource)
            ->willReturn($subscriptionEntity);

        $compositeProvider = new CompositeResourceProvider([$courseProvider, $subscriptionProvider]);

        // Test both resource types are supported
        $this->assertTrue($compositeProvider->supports(ResourceType::Course));
        $this->assertTrue($compositeProvider->supports(ResourceType::Subscription));

        // Test entities are retrieved correctly
        $this->assertSame($courseEntity, $compositeProvider->getEntity($courseResource));
        $this->assertSame($subscriptionEntity, $compositeProvider->getEntity($subscriptionResource));
    }

    public static function supportsResourceTypeProvider(): array
    {
        return [
            'first provider supports' => [
                'resourceType' => ResourceType::Course,
                'provider1Supports' => true,
                'provider2Supports' => false,
                'expectedResult' => true,
            ],
            'second provider supports' => [
                'resourceType' => ResourceType::Subscription,
                'provider1Supports' => false,
                'provider2Supports' => true,
                'expectedResult' => true,
            ],
            'both providers support' => [
                'resourceType' => ResourceType::Course,
                'provider1Supports' => true,
                'provider2Supports' => true,
                'expectedResult' => true,
            ],
            'no providers support' => [
                'resourceType' => ResourceType::Subscription,
                'provider1Supports' => false,
                'provider2Supports' => false,
                'expectedResult' => false,
            ],
        ];
    }
}
