<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Service\SettingsService;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Filter\FilterMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Application\Command\PostManagerFiltersCommand;
use App\V2\Application\CommandHandler\PostManagerFiltersCommandHandler;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\PostManagerFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PostManagerFiltersCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?UserRepository $userRepository = null,
        ?FilterRepository $filterRepository = null,
        ?ManagerFilterRepository $managerFilterRepository = null,
        ?SettingsService $settingsService = null,
    ): PostManagerFiltersCommandHandler {
        return new PostManagerFiltersCommandHandler(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            filterRepository: $filterRepository ?? $this->createMock(FilterRepository::class),
            managerFilterRepository: $managerFilterRepository ?? $this->createMock(ManagerFilterRepository::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class),
        );
    }

    /**
     * @throws UserForbiddenAction
     * @throws PostManagerFiltersCommandHandlerException
     * @throws InfrastructureException
     * @throws Exception
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testHandle(): void
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(UserMother::create(id: 2));

        $filterRepository = $this->createMock(FilterRepository::class);
        $filterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new FilterCollection([
                FilterMother::create(id: new Id(2), name: 'Filter 2'),
                FilterMother::create(id: new Id(3), name: 'Filter 3'),
            ]));
        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        $managerFilterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new ManagerFilterCollection([]));

        $managerFilterRepository->expects($this->exactly(2))
            ->method('insert')
            ->willReturnCallback(function (ManagerFilter $managerFilter) {
                $this->assertEquals(2, $managerFilter->getUserId()->value());
                $this->assertTrue(\in_array($managerFilter->getFilterId()->value(), [2, 3]));
            });

        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturn(true);

        $handler = $this->getHandler(
            userRepository: $userRepository,
            filterRepository: $filterRepository,
            managerFilterRepository: $managerFilterRepository,
            settingsService: $settingsService,
        );

        $handler->handle(new PostManagerFiltersCommand(
            userId: new Id(2),
            filterIds: new IdCollection([new Id(2), new Id(3)])
        ));
    }

    /**
     * @throws UserForbiddenAction
     * @throws PostManagerFiltersCommandHandlerException
     * @throws InfrastructureException
     * @throws Exception
     * @throws CollectionException
     * @throws CriteriaException
     */
    #[DataProvider('provideExceptionCases')]
    public function testExceptions(
        callable $settingsServiceCallback,
        ?callable $userFindOneBy,
        ?callable $filterRepositoryFindBy,
        ?callable $managerFilterRepositoryFindBy,
        $expectedException,
    ): void {
        $userRepository = $this->createMock(UserRepository::class);
        $filterRepository = $this->createMock(FilterRepository::class);
        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturnCallback($settingsServiceCallback);

        if (null !== $userFindOneBy) {
            $userRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($userFindOneBy);
        }

        if (null !== $filterRepositoryFindBy) {
            $filterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($filterRepositoryFindBy);
        }

        if (null !== $managerFilterRepositoryFindBy) {
            $managerFilterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($managerFilterRepositoryFindBy);
        }

        $handler = $this->getHandler(
            userRepository: $userRepository,
            filterRepository: $filterRepository,
            managerFilterRepository: $managerFilterRepository,
            settingsService: $settingsService,
        );

        $this->expectExceptionObject($expectedException);
        $handler->handle(new PostManagerFiltersCommand(
            userId: new Id(2),
            filterIds: new IdCollection([new Id(2), new Id(3)])
        ));
    }

    public static function provideExceptionCases(): \Generator
    {
        yield 'filters disabled' => [
            'settingsServiceCallback' => fn () => false,
            'userFindOneBy' => null,
            'filterRepositoryFindBy' => null,
            'managerFilterRepositoryFindBy' => null,
            'expectedException' => UserForbiddenAction::filtersNotEnabled(),
        ];

        yield 'user not found' => [
            'settingsServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => throw new UserNotFoundException(),
            'filterRepositoryFindBy' => null,
            'managerFilterRepositoryFindBy' => null,
            'expectedException' => new UserNotFoundException(),
        ];

        yield 'filter does not exists' => [
            'settingsServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 2),
            'filterRepositoryFindBy' => fn () => new FilterCollection([]),
            'managerFilterRepositoryFindBy' => null,
            'expectedException' => PostManagerFiltersCommandHandlerException::filterDoesNotExist(),
        ];

        yield 'one of the filters is assigned' => [
            'settingsServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 2),
            'filterRepositoryFindBy' => fn () => new FilterCollection([
                FilterMother::create(id: new Id(2), name: 'Filter 2'),
                FilterMother::create(id: new Id(3), name: 'Filter 3'),
            ]),
            'managerFilterRepositoryFindBy' => fn () => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(2), filterId: new Id(2)),
            ]),
            'expectedException' => PostManagerFiltersCommandHandlerException::userHasFilterAssigned(),
        ];
    }
}
