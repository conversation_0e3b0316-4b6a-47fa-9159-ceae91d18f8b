<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingDTOMother;
use App\V2\Application\Command\CreateVirtualMeeting;
use App\V2\Application\CommandHandler\CreateVirtualMeetingCommandHandler;
use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class CreateVirtualMeetingCommandHandlerTest extends TestCase
{
    private const string DEFAULT_URL = 'https://example.com/meeting';

    /**
     * @throws Exception
     * @throws InvalidUuidException
     */
    private function getHandler(
        ?VirtualMeetingRepository $virtualMeetingRepository = null,
        ?UuidGenerator $uuidGenerator = null,
    ): CreateVirtualMeetingCommandHandler {
        if (null === $uuidGenerator) {
            $uuidGenerator = $this->createMock(UuidGenerator::class);
            $uuidGenerator->method('generate')
                ->willReturn(UuidMother::create());
        }

        return new CreateVirtualMeetingCommandHandler(
            $virtualMeetingRepository ?? $this->createMock(VirtualMeetingRepository::class),
            $uuidGenerator,
        );
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function getCommand(
        ?VirtualMeetingDTO $virtualMeetingDto = null,
    ): CreateVirtualMeeting {
        return new CreateVirtualMeeting(
            $virtualMeetingDto ?? VirtualMeetingDTOMother::create(
                type: VirtualMeetingType::Fixed,
                url: self::DEFAULT_URL,
            ),
        );
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     */
    public function testIsInstantiable(): void
    {
        $handler = $this->getHandler();
        self::assertInstanceOf(CreateVirtualMeetingCommandHandler::class, $handler);
    }

    /**
     * @throws Exception
     * @throws InvalidVirtualMeetingException
     * @throws \DateMalformedStringException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     */
    public function testInvokeCreatesAndPersistsVirtualMeeting(): void
    {
        $dto = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            url: self::DEFAULT_URL,
        );
        $command = $this->getCommand($dto);
        $uuid = UuidMother::create();

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->once())
            ->method('generate')
            ->willReturn($uuid);

        $virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);
        $virtualMeetingRepository->expects($this->once())
            ->method('put')
            ->with($this->callback(function (VirtualMeeting $virtualMeeting) use ($dto) {
                return $virtualMeeting->getType() === $dto->getType()
                    && $virtualMeeting->getStartAt() === $dto->getStartAt()
                    && $virtualMeeting->getFinishAt() === $dto->getFinishAt()
                    && $virtualMeeting->getUrl() === $dto->getUrl();
            }));

        $handler = $this->getHandler(
            virtualMeetingRepository: $virtualMeetingRepository,
            uuidGenerator: $uuidGenerator,
        );

        $result = $handler->handle($command);

        $this->assertTrue($result->equals($uuid));
    }

    /**
     * @throws InfrastructureException
     * @throws \DateMalformedStringException
     * @throws Exception
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     */
    public function testThrowsExceptionOnInvalidDTO(): void
    {
        $dto = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            url: null // URL is required for Fixed type
        );

        $handler = $this->getHandler();

        $this->expectExceptionObject(
            InvalidVirtualMeetingException::urlRequiredForFixedType()
        );

        $handler->handle($this->getCommand($dto));
    }
}
