<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Entity\Announcement;
use App\Entity\User;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\V2\Application\Command\DeleteAnnouncementManagerCommand;
use App\V2\Application\CommandHandler\DeleteAnnouncementManagerCommandHandler;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\DeleteAnnouncementManagerException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class DeleteAnnouncementManagerCommandHandlerTest extends TestCase
{
    private DeleteAnnouncementManagerCommandHandler $handler;

    private UserRepository|MockObject $userRepository;
    private LegacyAnnouncementRepository|MockObject $legacyAnnouncementRepository;
    private AnnouncementManagerRepository|MockObject $announcementManagerRepository;
    private SettingsService|MockObject $settingsService;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->legacyAnnouncementRepository = $this->createMock(LegacyAnnouncementRepository::class);
        $this->announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);
        $this->settingsService = $this->createMock(SettingsService::class);

        $this->handler = new DeleteAnnouncementManagerCommandHandler(
            $this->userRepository,
            $this->legacyAnnouncementRepository,
            $this->announcementManagerRepository,
            $this->settingsService
        );
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws DeleteAnnouncementManagerException
     * @throws AnnouncementManagerNotFoundException
     * @throws CriteriaException
     */
    public function testHandleThrowExceptionIfManagerSharingDisabled(): void
    {
        $command = new DeleteAnnouncementManagerCommand(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: UserMother::create(roles: [User::ROLE_ADMIN]),
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(false);

        $this->legacyAnnouncementRepository
            ->expects($this->never())
            ->method('findOneBy');

        $this->expectException(ManagerNotAuthorizedException::class);
        $this->expectExceptionMessage('Announcement manager sharing is disabled');

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws ManagerNotAuthorizedException
     * @throws AnnouncementManagerNotFoundException
     * @throws CriteriaException
     */
    public function testHandleThrowExceptionIfUserNotFound(): void
    {
        $requestUser = UserMother::create(id: 3, roles: [User::ROLE_ADMIN]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: 1, course: $course);

        $command = new DeleteAnnouncementManagerCommand(
            announcementId: new Id(1),
            userId: new Id(9999),
            requestUser: $requestUser,
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserCriteria::createById(new Id(9999)))
            ->willThrowException(new UserNotFoundException('User not found'));

        $this->expectException(DeleteAnnouncementManagerException::class);
        $this->expectExceptionMessage('User not found');

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws DeleteAnnouncementManagerException
     * @throws AnnouncementManagerNotFoundException
     * @throws ManagerNotAuthorizedException
     * @throws CriteriaException
     */
    public function testHandleThrowsAnnouncementNotFound()
    {
        $command = new DeleteAnnouncementManagerCommand(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: UserMother::create(roles: [User::ROLE_ADMIN]),
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn(null);

        $this->expectException(AnnouncementNotFoundException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws AnnouncementNotFoundException
     * @throws DeleteAnnouncementManagerException
     * @throws ManagerNotAuthorizedException
     * @throws CriteriaException
     */
    public function testHandleThrowsAnnouncementManagerNotFound(): void
    {
        $announcementId = new Id(1);
        $userId = new Id(2);

        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn([User::ROLE_ADMIN]);

        $userToRemove = $this->createMock(User::class);
        $userToRemove->method('getId')->willReturn($userId->value());

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getId')->willReturn($announcementId->value());

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => $announcementId->value()])
            ->willReturn($announcement);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserCriteria::createById($userId))
            ->willReturn($userToRemove);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new AnnouncementManagerNotFoundException());

        $command = new DeleteAnnouncementManagerCommand(
            announcementId: $announcementId,
            userId: $userId,
            requestUser: $requestUser
        );

        $this->expectException(AnnouncementManagerNotFoundException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementManagerNotFoundException
     * @throws DeleteAnnouncementManagerException
     * @throws AnnouncementNotFoundException
     * @throws CriteriaException
     */
    public function testHandleThrowsWhenUserIsNotAnnouncementOwner(): void
    {
        $requestUser = UserMother::create(id: 3, roles: [User::ROLE_MANAGER]);
        $course = CourseMother::create(name: 'Test Course');
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $announcement->setCreatedBy(UserMother::create(id: 4, roles: [User::ROLE_ADMIN])); // Different creator

        $command = new DeleteAnnouncementManagerCommand(
            announcementId: new Id(1),
            userId: new Id(2),
            requestUser: $requestUser,
        );

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->expectException(ManagerNotAuthorizedException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementNotFoundException
     * @throws DeleteAnnouncementManagerException
     * @throws AnnouncementManagerNotFoundException
     * @throws CriteriaException
     * @throws Exception
     * @throws ManagerNotAuthorizedException
     */
    #[DataProvider('adminRolesProvider')]
    public function testHandleSuccessAsAdmin(array $roles): void
    {
        $announcementId = new Id(1);
        $userId = new Id(2);

        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn($roles);

        $userToRemove = $this->createMock(User::class);
        $userToRemove->method('getId')->willReturn($userId->value());

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getId')->willReturn($announcementId->value());

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => $announcementId->value()])
            ->willReturn($announcement);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserCriteria::createById($userId))
            ->willReturn($userToRemove);

        $announcementManager = $this->createMock(AnnouncementManager::class);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($announcementManager);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('delete')
            ->with($announcementManager);

        $command = new DeleteAnnouncementManagerCommand(
            announcementId: $announcementId,
            userId: $userId,
            requestUser: $requestUser
        );

        $this->handler->handle($command);
    }

    public static function adminRolesProvider(): \Generator
    {
        yield 'Admin' => [
            'roles' => [User::ROLE_ADMIN],
        ];

        yield 'Admin with creator role' => [
            'roles' => [User::ROLE_ADMIN, User::ROLE_CREATOR],
        ];
    }

    /**
     * @throws AnnouncementManagerNotFoundException
     * @throws InfrastructureException
     * @throws Exception
     * @throws AnnouncementNotFoundException
     * @throws DeleteAnnouncementManagerException
     * @throws ManagerNotAuthorizedException
     * @throws CriteriaException
     */
    public function testHandleSuccessAsManager(): void
    {
        $announcementId = new Id(1);
        $userId = new Id(2);

        // The request user is a manager and the creator of the announcement
        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn([User::ROLE_MANAGER]);
        $requestUser->method('getId')->willReturn(5);
        $requestUser->method('isAdmin')->willReturn(false);
        $requestUser->method('isManager')->willReturn(true);

        $userToRemove = $this->createMock(User::class);
        $userToRemove->method('getId')->willReturn($userId->value());

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getId')->willReturn($announcementId->value());
        $announcement->method('getCreatedBy')->willReturn($requestUser);

        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.announcement.managers.sharing')
            ->willReturn(true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => $announcementId->value()])
            ->willReturn($announcement);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserCriteria::createById($userId))
            ->willReturn($userToRemove);

        $announcementManager = $this->createMock(AnnouncementManager::class);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($announcementManager);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('delete')
            ->with($announcementManager);

        $command = new DeleteAnnouncementManagerCommand(
            announcementId: $announcementId,
            userId: $userId,
            requestUser: $requestUser
        );

        $this->handler->handle($command);
    }
}
