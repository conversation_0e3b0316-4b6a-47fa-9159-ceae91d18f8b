<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Command\DeletePurchasableItemCommand;
use App\V2\Application\CommandHandler\DeletePurchasableItemCommandHandler;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class DeletePurchasableItemCommandHandlerTest extends TestCase
{
    private PurchasableItemRepository|MockObject $purchasableItemRepository;
    private DeletePurchasableItemCommandHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $this->handler = new DeletePurchasableItemCommandHandler($this->purchasableItemRepository);
    }

    /**
     * @throws Exception
     */
    private function getHandler(
        ?PurchasableItemRepository $purchasableItemRepository = null,
    ): DeletePurchasableItemCommandHandler {
        return new DeletePurchasableItemCommandHandler(
            $purchasableItemRepository ?? $this->createMock(PurchasableItemRepository::class),
        );
    }

    /**
     * @throws InvalidUuidException
     */
    private function getCommand(?Uuid $purchasableItemId = null): DeletePurchasableItemCommand
    {
        return new DeletePurchasableItemCommand(
            purchasableItemId: $purchasableItemId ?? UuidMother::create(),
        );
    }

    /**
     * @throws Exception
     */
    public function testIsInstantiable(): void
    {
        $handler = $this->getHandler();
        self::assertInstanceOf(DeletePurchasableItemCommandHandler::class, $handler);
    }

    /**
     * @throws CriteriaException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidUuidException
     * @throws Exception
     */
    public function testHandleSuccessfullyDeletesPurchasableItem(): void
    {
        $purchasableItemId = UuidMother::create();
        $purchasableItem = PurchasableItemMother::create(id: $purchasableItemId);

        $this->purchasableItemRepository->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchasableItemCriteria $criteria) {
                // Verify that the criteria is created with the correct ID
                return true; // We can't easily inspect the criteria internals, so we trust the implementation
            }))
            ->willReturn($purchasableItem);

        $this->purchasableItemRepository->expects($this->once())
            ->method('delete')
            ->with($purchasableItem);

        $command = $this->getCommand($purchasableItemId);
        $this->handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidUuidException
     * @throws Exception
     */
    public function testHandleThrowsPurchasableItemNotFoundExceptionWhenItemDoesNotExist(): void
    {
        $purchasableItemId = UuidMother::create();

        $this->purchasableItemRepository->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchasableItemCriteria $criteria) {
                return true;
            }))
            ->willThrowException(new PurchasableItemNotFoundException());

        $this->purchasableItemRepository->expects($this->never())
            ->method('delete');

        $this->expectException(PurchasableItemNotFoundException::class);

        $command = $this->getCommand($purchasableItemId);
        $this->handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidUuidException
     * @throws Exception
     */
    public function testHandleThrowsPurchasableItemRepositoryExceptionOnRepositoryError(): void
    {
        $purchasableItemId = UuidMother::create();
        $purchasableItem = PurchasableItemMother::create(id: $purchasableItemId);

        $this->purchasableItemRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($purchasableItem);

        $this->purchasableItemRepository->expects($this->once())
            ->method('delete')
            ->with($purchasableItem)
            ->willThrowException(new PurchasableItemRepositoryException('Database error'));

        $this->expectException(PurchasableItemRepositoryException::class);
        $this->expectExceptionMessage('Database error');

        $command = $this->getCommand($purchasableItemId);
        $this->handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidUuidException
     * @throws Exception
     */
    public function testHandleThrowsCriteriaExceptionOnInvalidCriteria(): void
    {
        $purchasableItemId = UuidMother::create();

        $this->purchasableItemRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new CriteriaException('Invalid criteria'));

        $this->purchasableItemRepository->expects($this->never())
            ->method('delete');

        $this->expectException(CriteriaException::class);
        $this->expectExceptionMessage('Invalid criteria');

        $command = $this->getCommand($purchasableItemId);
        $this->handler->handle($command);
    }

    /**
     * @throws CriteriaException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidUuidException
     * @throws Exception
     */
    public function testHandleCallsRepositoryWithCorrectCriteria(): void
    {
        $purchasableItemId = UuidMother::create();
        $purchasableItem = PurchasableItemMother::create(id: $purchasableItemId);

        // Create a more specific expectation for the criteria
        $this->purchasableItemRepository->expects($this->once())
            ->method('findOneBy')
            ->with($this->isInstanceOf(PurchasableItemCriteria::class))
            ->willReturn($purchasableItem);

        $this->purchasableItemRepository->expects($this->once())
            ->method('delete')
            ->with($this->identicalTo($purchasableItem));

        $command = $this->getCommand($purchasableItemId);
        $this->handler->handle($command);
    }
}
