<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Entity\User;
use App\Repository\FilterRepository as LegacyFilterRepository;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\FilterMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Application\Command\DeleteUserFiltersCommand;
use App\V2\Application\CommandHandler\DeleteUserFiltersCommandHandler;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\DeleteUserFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use App\V2\Domain\User\UserRepository;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class DeleteUserFiltersCommandHandlerTest extends TestCase
{
    private function getHandler(
        ?UserFilterRepository $userFilterRepository = null,
        ?ManagerFilterRepository $managerFilterRepository = null,
        ?UserRepository $userRepository = null,
        ?LegacyFilterRepository $legacyFilterRepository = null,
        ?SettingsService $settingsService = null,
    ): DeleteUserFiltersCommandHandler {
        return new DeleteUserFiltersCommandHandler(
            userFilterRepository: $userFilterRepository ?? $this->createMock(UserFilterRepository::class),
            managerFilterRepository: $managerFilterRepository ?? $this->createMock(ManagerFilterRepository::class),
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            legacyFilterRepository: $legacyFilterRepository ?? $this->createMock(LegacyFilterRepository::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class),
        );
    }

    public static function provideHandle(): \Generator
    {
        yield '4 filters 2 assigned 2 managed, delete 1' => [
            'userRole' => User::ROLE_MANAGER,
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
                FilterMother::create(id: 4),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]),
            'assignedFilterCollection' => new UserFilterCollection([
                UserFilterMother::create(userId: new Id(2), filterId: new Id(1)),
                UserFilterMother::create(userId: new Id(2), filterId: new Id(3)),
            ]),
            'filtersToDelete' => new IdCollection([new Id(1)]),
            'exception' => null,
        ];

        yield '4 filters 2 assigned 1 managed, delete 1' => [
            'userRole' => User::ROLE_MANAGER,
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
                FilterMother::create(id: 4),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]),
            'assignedFilterCollection' => new UserFilterCollection([
                UserFilterMother::create(userId: new Id(2), filterId: new Id(1)),
                UserFilterMother::create(userId: new Id(2), filterId: new Id(3)),
            ]),
            'filtersToDelete' => new IdCollection([new Id(1)]),
            'exception' => DeleteUserFiltersCommandHandlerException::cannotRemoveUnmanagedFilter(),
        ];

        yield '4 filters 2 assigned 2 managed, delete 1 not assigned' => [
            'userRole' => User::ROLE_MANAGER,
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
                FilterMother::create(id: 4),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(2)),
                ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3)),
            ]),
            'assignedFilterCollection' => new UserFilterCollection([
                UserFilterMother::create(userId: new Id(2), filterId: new Id(1)),
                UserFilterMother::create(userId: new Id(2), filterId: new Id(3)),
            ]),
            'filtersToDelete' => new IdCollection([new Id(2)]),
            'exception' => DeleteUserFiltersCommandHandlerException::userFilterNotAssignedToUser(),
        ];

        yield 'ADMIN 4 filters 2 assigned 0 managed, delete 1' => [
            'userRole' => User::ROLE_ADMIN,
            'filters' => [
                FilterMother::create(id: 1),
                FilterMother::create(id: 2),
                FilterMother::create(id: 3),
                FilterMother::create(id: 4),
            ],
            'managerFilterCollection' => new ManagerFilterCollection([]),
            'assignedFilterCollection' => new UserFilterCollection([
                UserFilterMother::create(userId: new Id(2), filterId: new Id(1)),
                UserFilterMother::create(userId: new Id(2), filterId: new Id(3)),
            ]),
            'filtersToDelete' => new IdCollection([new Id(3)]),
            'exception' => null,
        ];
    }

    #[DataProvider('provideHandle')]
    public function testHandle(
        string $userRole,
        array $filters,
        ManagerFilterCollection $managerFilterCollection,
        UserFilterCollection $assignedFilterCollection,
        IdCollection $filtersToDelete,
        $exception = null,
    ): void {
        $user = UserMother::create(id: 2, roles: [$userRole]);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->method('findOneBy')
            ->willReturn($user);

        $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(Query::class);

        $query->method('getResult')
            ->willReturn($filters);
        $queryBuilder->method('where')
            ->willReturn($queryBuilder);
        $queryBuilder->method('setParameter')
            ->willReturn($queryBuilder);
        $queryBuilder->method('getQuery')
            ->willReturn($query);
        $legacyFilterRepository->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $userFilterRepository = $this->createMock(UserFilterRepository::class);
        $userFilterRepository
            ->method('findBy')
            ->willReturn($assignedFilterCollection);

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        $managerFilterRepository
            ->method('findBy')
            ->willReturn($managerFilterCollection);

        $userFilterRepository
            ->method('delete')
            ->willReturnCallback(function (UserFilter $userFilter) use ($user, $filtersToDelete) {
                $this->assertEquals($user->getId(), $userFilter->getUserId()->value());
                $inArray = $filtersToDelete->filter(fn (Id $id) => $userFilter->getFilterId()->equals($id));
                $this->assertCount(1, $inArray);
            });

        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturn(true);

        $handler = $this->getHandler(
            userFilterRepository: $userFilterRepository,
            managerFilterRepository: $managerFilterRepository,
            userRepository: $userRepository,
            legacyFilterRepository: $legacyFilterRepository,
            settingsService: $settingsService,
        );

        if (null !== $exception) {
            $this->expectExceptionObject($exception);
        }

        $handler->handle(
            new DeleteUserFiltersCommand(
                userId: new Id(1),
                filterIds: $filtersToDelete,
                requestedBy: $user,
            )
        );
    }

    #[DataProvider('provideExceptions')]
    public function testExceptions(
        User $requestedBy,
        callable $settingServiceCallback,
        ?callable $userFindOneBy,
        ?callable $legacyFilterGetResult,
        ?callable $managerFilterFindBy,
        ?callable $userFilterFindBy,
        $expectedException
    ): void {
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->expects($this->once())
            ->method('get')
            ->willReturnCallback($settingServiceCallback);

        $filterIds = new IdCollection([new Id(1)]);

        $userRepository = $this->createMock(UserRepository::class);
        if (null !== $userFindOneBy) {
            $userRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($userFindOneBy);
        }

        $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
        if (null !== $legacyFilterGetResult) {
            $legacyFilterRepository = $this->createMock(LegacyFilterRepository::class);
            $queryBuilder = $this->createMock(QueryBuilder::class);
            $query = $this->createMock(Query::class);

            $query->expects($this->once())->method('getResult')
                ->willReturnCallback($legacyFilterGetResult);
            $queryBuilder->expects($this->once())->method('where')
                ->willReturn($queryBuilder);
            $queryBuilder->expects($this->once())->method('setParameter')
                ->willReturn($queryBuilder);
            $queryBuilder->expects($this->once())->method('getQuery')
                ->willReturn($query);
            $legacyFilterRepository->expects($this->once())
                ->method('createQueryBuilder')
                ->willReturn($queryBuilder);
        }

        $userFilterRepository = $this->createMock(UserFilterRepository::class);
        if (null !== $userFilterFindBy) {
            $userFilterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($userFilterFindBy);
        }

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        if (null !== $managerFilterFindBy) {
            $managerFilterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($managerFilterFindBy);
        }

        $handler = $this->getHandler(
            userFilterRepository: $userFilterRepository,
            managerFilterRepository: $managerFilterRepository,
            userRepository: $userRepository,
            legacyFilterRepository: $legacyFilterRepository,
            settingsService: $settingsService,
        );

        $this->expectExceptionObject($expectedException);
        $handler->handle(
            new DeleteUserFiltersCommand(
                userId: new Id(1),
                filterIds: $filterIds,
                requestedBy: $requestedBy,
            )
        );
    }

    public static function provideExceptions(): \Generator
    {
        yield 'filters disabled' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => false,
            'userFindOneBy' => null,
            'legacyFilterGetResult' => null,
            'managerFilterFindBy' => null,
            'userFilterFindBy' => null,
            'expectedException' => UserForbiddenAction::filtersNotEnabled(),
        ];
        yield 'user not found' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => throw new UserNotFoundException(),
            'legacyFilterGetResult' => null,
            'managerFilterFindBy' => null,
            'userFilterFindBy' => null,
            'expectedException' => new UserNotFoundException(),
        ];

        yield 'filter does not exists' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'legacyFilterGetResult' => fn () => [],
            'managerFilterFindBy' => null,
            'userFilterFindBy' => null,
            'expectedException' => DeleteUserFiltersCommandHandlerException::filterDoesNotExist(),
        ];

        yield 'manager unmanaged filter' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_MANAGER]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 2),
            'legacyFilterGetResult' => fn () => [FilterMother::create(id: 1)],
            'managerFilterFindBy' => fn () => new ManagerFilterCollection([]),
            'userFilterFindBy' => null,
            'expectedException' => DeleteUserFiltersCommandHandlerException::cannotRemoveUnmanagedFilter(),
        ];

        yield 'filter already assigned' => [
            'requestedBy' => UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            'settingServiceCallback' => fn () => true,
            'userFindOneBy' => fn () => UserMother::create(id: 2),
            'legacyFilterGetResult' => fn () => [FilterMother::create(id: 1)],
            'managerFilterFindBy' => null,
            'userFilterFindBy' => fn () => new UserFilterCollection([]),
            'expectedException' => DeleteUserFiltersCommandHandlerException::userFilterNotAssignedToUser(),
        ];
    }
}
