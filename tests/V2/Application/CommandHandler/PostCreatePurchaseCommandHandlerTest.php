<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\Purchase\PurchasableItemCollectionMother;
use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\CurrencyMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Financial\TaxRateMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Command\PostCreatePurchaseCommand;
use App\V2\Application\CommandHandler\Exception\PostCreatePurchaseException;
use App\V2\Application\CommandHandler\PostCreatePurchaseCommandHandler;
use App\V2\Application\Purchase\PurchaseFactory;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Financial\TaxRate;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PostCreatePurchaseCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?PurchaseRepository $purchaseRepository = null,
        ?PurchasableItemRepository $purchasableItemRepository = null,
        ?UuidGenerator $uuidGenerator = null,
    ): PostCreatePurchaseCommandHandler {
        $uuidGenerator = $uuidGenerator ?? $this->createMock(UuidGenerator::class);
        $purchaseFactory = new PurchaseFactory($uuidGenerator);

        return new PostCreatePurchaseCommandHandler(
            purchaseRepository: $purchaseRepository ?? $this->createMock(PurchaseRepository::class),
            purchasableItemRepository: $purchasableItemRepository
                ?? $this->createMock(PurchasableItemRepository::class),
            purchaseFactory: $purchaseFactory,
        );
    }

    /**
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws \Exception
     */
    private function getCommand(
        ?UuidCollection $purchasableItemIds = null,
        ?Id $userId = null,
        ?TaxRate $taxRate = null,
        ?Currency $currency = null,
    ): PostCreatePurchaseCommand {
        return new PostCreatePurchaseCommand(
            purchasableItemIds: $purchasableItemIds ?? new UuidCollection([UuidMother::create()]),
            userId: $userId ?? IdMother::create(),
            taxRate: $taxRate ?? TaxRateMother::create(),
            currency: $currency ?? CurrencyMother::create(),
        );
    }

    /**
     * @throws Exception
     */
    public function testIsInstantiable(): void
    {
        $handler = $this->getHandler();
        self::assertInstanceOf(PostCreatePurchaseCommandHandler::class, $handler);
    }

    /**
     * @throws Exception
     */
    public function testHandleMethodExists(): void
    {
        $this->assertTrue(method_exists(PostCreatePurchaseCommandHandler::class, 'handle'));

        $handler = $this->getHandler();
        $this->assertTrue(\is_callable([$handler, 'handle']));
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws PostCreatePurchaseException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     * @throws CriteriaException
     */
    public function testHandleSuccessfulPurchaseCreation(): void
    {
        $currency = CurrencyMother::create();
        $item1Price = MoneyMother::create(amount: 1000, currency: $currency);
        $item2Price = MoneyMother::create(amount: 2000, currency: $currency);

        $item1 = PurchasableItemMother::create(price: $item1Price, isActive: true);
        $item2 = PurchasableItemMother::create(price: $item2Price, isActive: true);

        $purchasableItems = new PurchasableItemCollection([$item1, $item2]);
        $purchasableItemIds = new UuidCollection([$item1->getId(), $item2->getId()]);

        $command = $this->getCommand(
            purchasableItemIds: $purchasableItemIds,
            currency: $currency
        );

        $generatedPurchaseId = UuidMother::create();
        $expectedTotalAmount = 3000; // 1000 + 2000
        $expectedTaxAmount = (int) round($expectedTotalAmount * $command->getTaxRate()->value());

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->exactly(3)) // 1 for purchase + 2 for purchase items
            ->method('generate')
            ->willReturn($generatedPurchaseId);

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->with($this->isInstanceOf(PurchasableItemCriteria::class))
            ->willReturn($purchasableItems);

        $purchaseRepository = $this->createMock(PurchaseRepository::class);
        $purchaseRepository->expects($this->once())
            ->method('put')
            ->with(
                $this->callback(function (Purchase $purchase) use (
                    $generatedPurchaseId,
                    $command,
                    $expectedTotalAmount,
                    $expectedTaxAmount,
                    $currency
                ) {
                    return $purchase->getId()->equals($generatedPurchaseId)
                        && $purchase->getUserId()->equals($command->getUserId())
                        && PurchaseStatus::Pending === $purchase->getStatus()
                        && $purchase->getAmount()->value() === $expectedTotalAmount
                        && $purchase->getAmount()->currency()->equals($command->getCurrency())
                        && $purchase->getTaxRate()->equals($command->getTaxRate())
                        && $purchase->getTaxAmount()->value() === $expectedTaxAmount
                        && $purchase->getTaxAmount()->currency()->equals($currency);
                })
            );

        $handler = $this->getHandler(
            purchaseRepository: $purchaseRepository,
            purchasableItemRepository: $purchasableItemRepository,
            uuidGenerator: $uuidGenerator
        );

        $result = $handler->handle($command);

        $this->assertInstanceOf(Purchase::class, $result);
        $this->assertTrue($result->getId()->equals($generatedPurchaseId));
        $this->assertTrue($result->getUserId()->equals($command->getUserId()));
        $this->assertEquals(PurchaseStatus::Pending, $result->getStatus());
        $this->assertEquals($expectedTotalAmount, $result->getAmount()->value());
        $this->assertTrue($result->getAmount()->currency()->equals($command->getCurrency()));
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws PostCreatePurchaseException
     * @throws InvalidUuidException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException|\App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException
     */
    public function testHandleThrowsExceptionForInactivePurchasableItem(): void
    {
        $currency = CurrencyMother::create();
        $inactiveItem = PurchasableItemMother::create(
            price: MoneyMother::create(currency: $currency),
            isActive: false
        );

        // When filtering by isActive(true), inactive items should not be returned
        $emptyCollection = PurchasableItemCollectionMother::empty();
        $purchasableItemIds = new UuidCollection([$inactiveItem->getId()]);

        $command = $this->getCommand(
            purchasableItemIds: $purchasableItemIds,
            currency: $currency
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($emptyCollection);

        $handler = $this->getHandler(
            purchasableItemRepository: $purchasableItemRepository
        );

        $this->expectException(PostCreatePurchaseException::class);

        $handler->handle($command);
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws PostCreatePurchaseException
     * @throws InvalidUuidException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     */
    public function testHandleThrowsExceptionForCurrencyMismatch(): void
    {
        $itemCurrency = CurrencyMother::create();
        $commandCurrency = new Currency(CurrencyCode::USD); // Different currency

        $item = PurchasableItemMother::create(
            price: MoneyMother::create(currency: $itemCurrency),
            isActive: true
        );

        $purchasableItems = new PurchasableItemCollection([$item]);
        $purchasableItemIds = new UuidCollection([$item->getId()]);

        $command = $this->getCommand(
            purchasableItemIds: $purchasableItemIds,
            currency: $commandCurrency
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($purchasableItems);

        $handler = $this->getHandler(
            purchasableItemRepository: $purchasableItemRepository
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Currency mismatch');

        $handler->handle($command);
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws PostCreatePurchaseException
     * @throws InvalidUuidException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     */
    public function testHandleThrowsExceptionWhenPurchasableItemRepositoryFails(): void
    {
        $command = $this->getCommand();

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->willThrowException(new PurchasableItemRepositoryException('Repository error'));

        $handler = $this->getHandler(
            purchasableItemRepository: $purchasableItemRepository
        );

        $this->expectException(PurchasableItemRepositoryException::class);
        $this->expectExceptionMessage('Repository error');

        $handler->handle($command);
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws PostCreatePurchaseException
     * @throws InvalidUuidException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     */
    public function testHandleThrowsExceptionWhenPurchaseRepositoryFails(): void
    {
        $currency = CurrencyMother::create();
        $item = PurchasableItemMother::create(
            price: MoneyMother::create(currency: $currency),
            isActive: true
        );

        $purchasableItems = new PurchasableItemCollection([$item]);
        $purchasableItemIds = new UuidCollection([$item->getId()]);

        $command = $this->getCommand(
            purchasableItemIds: $purchasableItemIds,
            currency: $currency
        );

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->exactly(2)) // 1 for purchase + 1 for purchase item
            ->method('generate')
            ->willReturn(UuidMother::create());

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($purchasableItems);

        $purchaseRepository = $this->createMock(PurchaseRepository::class);
        $purchaseRepository->expects($this->once())
            ->method('put')
            ->willThrowException(new PurchaseRepositoryException('Save failed'));

        $handler = $this->getHandler(
            purchaseRepository: $purchaseRepository,
            purchasableItemRepository: $purchasableItemRepository,
            uuidGenerator: $uuidGenerator
        );

        $this->expectException(PurchaseRepositoryException::class);
        $this->expectExceptionMessage('Save failed');

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws PostCreatePurchaseException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     * @throws CriteriaException
     */
    public function testHandleCallsRepositoryWithCorrectCriteria(): void
    {
        $currency = CurrencyMother::create();
        $item1 = PurchasableItemMother::create(
            price: MoneyMother::create(currency: $currency),
            isActive: true
        );
        $item2 = PurchasableItemMother::create(
            price: MoneyMother::create(currency: $currency),
            isActive: true
        );

        $purchasableItems = new PurchasableItemCollection([$item1, $item2]);
        $purchasableItemIds = new UuidCollection([$item1->getId(), $item2->getId()]);

        $command = $this->getCommand(
            purchasableItemIds: $purchasableItemIds,
            currency: $currency
        );

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->exactly(3)) // 1 for purchase + 2 for purchase items
            ->method('generate')
            ->willReturn(UuidMother::create());

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->with($this->callback(function (PurchasableItemCriteria $criteria) use ($purchasableItemIds) {
                return null !== $criteria->getIds()
                    && $criteria->getIds()->count() === $purchasableItemIds->count();
            }))
            ->willReturn($purchasableItems);

        $handler = $this->getHandler(
            purchasableItemRepository: $purchasableItemRepository,
            uuidGenerator: $uuidGenerator
        );

        $handler->handle($command);
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws PostCreatePurchaseException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     * @throws CriteriaException
     */
    public function testHandleCalculatesCorrectTaxAmount(): void
    {
        $currency = CurrencyMother::create();
        $taxRate = TaxRateMother::create(rate: 0.15); // 15% tax rate
        $itemPrice = MoneyMother::create(amount: 2000, currency: $currency);

        $item = PurchasableItemMother::create(price: $itemPrice, isActive: true);
        $purchasableItems = new PurchasableItemCollection([$item]);
        $purchasableItemIds = new UuidCollection([$item->getId()]);

        $command = $this->getCommand(
            purchasableItemIds: $purchasableItemIds,
            taxRate: $taxRate,
            currency: $currency
        );

        $expectedTaxAmount = (int) round(2000 * 0.15); // 300

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->exactly(2)) // 1 for purchase + 1 for purchase item
            ->method('generate')
            ->willReturn(UuidMother::create());

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($purchasableItems);

        $purchaseRepository = $this->createMock(PurchaseRepository::class);
        $purchaseRepository->expects($this->once())
            ->method('put')
            ->with($this->callback(function (Purchase $purchase) use ($expectedTaxAmount, $currency) {
                return $purchase->getTaxAmount()->value() === $expectedTaxAmount
                    && $purchase->getTaxAmount()->currency()->equals($currency);
            }));

        $handler = $this->getHandler(
            purchaseRepository: $purchaseRepository,
            purchasableItemRepository: $purchasableItemRepository,
            uuidGenerator: $uuidGenerator
        );

        $result = $handler->handle($command);

        $this->assertEquals($expectedTaxAmount, $result->getTaxAmount()->value());
        $this->assertTrue($result->getTaxAmount()->currency()->equals($currency));
    }

    /**
     * @throws Exception
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws PostCreatePurchaseException
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     * @throws CriteriaException
     */
    public function testHandleWithEmptyPurchasableItemsCollection(): void
    {
        $currency = CurrencyMother::create();
        $emptyCollection = PurchasableItemCollectionMother::empty();
        $purchasableItemIds = new UuidCollection([UuidMother::create()]);

        $command = $this->getCommand(
            purchasableItemIds: $purchasableItemIds,
            currency: $currency
        );

        $purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->willReturn($emptyCollection);

        $handler = $this->getHandler(
            purchasableItemRepository: $purchasableItemRepository
        );

        $this->expectException(PostCreatePurchaseException::class);
        $handler->handle($command);
    }
}
