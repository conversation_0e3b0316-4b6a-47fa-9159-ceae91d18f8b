<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\Purchase;

use App\Tests\V2\Mother\Purchase\PurchaseItemMother;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\Purchase\Exception\PurchaseHydratorException;
use App\V2\Application\Hydrator\Purchase\PurchaseItemHydratorCollection;
use App\V2\Application\Hydrator\Purchase\PurchasePurchaseItemHydrator;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseItemHydrationCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PurchasePurchaseItemHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?PurchaseRepository $purchaseRepository = null,
        ?PurchaseItemHydratorCollection $purchaseItemHydratorCollection = null,
    ): PurchasePurchaseItemHydrator {
        return new PurchasePurchaseItemHydrator(
            purchaseRepository: $purchaseRepository ?? $this->createMock(PurchaseRepository::class),
            purchaseItemHydratorCollection: $purchaseItemHydratorCollection ?? $this->createMock(PurchaseItemHydratorCollection::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                PurchaseHydrationCriteria::createEmpty()->withPurchaseItem()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                PurchaseHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws Exception
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testEmptyCollection(): void
    {
        $collection = new PurchaseCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            PurchaseHydrationCriteria::createEmpty()->withPurchaseItem()
        );

        $this->assertEmpty($collection->all());
    }

    /**
     * @throws Exception
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testHydrate(): void
    {
        $purchase1 = PurchaseMother::create();
        $purchase2 = PurchaseMother::create();
        $purchase3 = PurchaseMother::create();

        $purchaseItem1 = PurchaseItemMother::create(purchaseId: $purchase1->getId());
        $purchaseItem2 = PurchaseItemMother::create(purchaseId: $purchase2->getId());
        $purchaseItem3 = PurchaseItemMother::create(purchaseId: $purchase3->getId());

        $collection = new PurchaseCollection([$purchase1, $purchase2, $purchase3]);

        $purchaseRepository = $this->createMock(PurchaseRepository::class);
        $purchaseRepository->expects($this->once())
            ->method('findPurchaseItemsBy')
            ->willReturn(new PurchaseItemCollection([$purchaseItem1, $purchaseItem2, $purchaseItem3]));

        $hydrator = $this->getHydrator(
            purchaseRepository: $purchaseRepository
        );

        $hydrator->hydrate(
            $collection,
            PurchaseHydrationCriteria::createEmpty()->withPurchaseItem()
        );

        $this->assertEquals($purchaseItem1, $purchase1->getPurchaseItems()->first());
        $this->assertEquals($purchaseItem2, $purchase2->getPurchaseItems()->first());
        $this->assertEquals($purchaseItem3, $purchase3->getPurchaseItems()->first());
    }

    /**
     * @throws Exception
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws InvalidUuidException|
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testHydrateWithPurchasableItems(): void
    {
        $purchase1 = PurchaseMother::create();
        $purchase2 = PurchaseMother::create();

        $purchaseItem1 = PurchaseItemMother::create(purchaseId: $purchase1->getId());
        $purchaseItem2 = PurchaseItemMother::create(purchaseId: $purchase2->getId());

        $collection = new PurchaseCollection([$purchase1, $purchase2]);
        $purchaseItemCollection = new PurchaseItemCollection([$purchaseItem1, $purchaseItem2]);

        $purchaseRepository = $this->createMock(PurchaseRepository::class);
        $purchaseRepository->expects($this->once())
            ->method('findPurchaseItemsBy')
            ->willReturn($purchaseItemCollection);

        $purchaseItemHydratorCollection = $this->createMock(PurchaseItemHydratorCollection::class);
        $purchaseItemHydratorCollection->expects($this->once())
            ->method('hydrate')
            ->with(
                $this->equalTo($purchaseItemCollection),
                $this->callback(function (PurchaseItemHydrationCriteria $criteria) {
                    return $criteria->needsPurchasableItem();
                })
            );

        $hydrator = $this->getHydrator(
            purchaseRepository: $purchaseRepository,
            purchaseItemHydratorCollection: $purchaseItemHydratorCollection
        );

        $criteria = PurchaseHydrationCriteria::createEmpty()->withPurchaseItem(true);

        $hydrator->hydrate($collection, $criteria);

        $this->assertEquals($purchaseItem1, $purchase1->getPurchaseItems()->first());
        $this->assertEquals($purchaseItem2, $purchase2->getPurchaseItems()->first());
    }

    /**
     * @throws Exception
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     */
    public function testHydrateWithoutPurchasableItems(): void
    {
        $purchase1 = PurchaseMother::create();

        $purchaseItem1 = PurchaseItemMother::create(purchaseId: $purchase1->getId());

        $collection = new PurchaseCollection([$purchase1]);
        $purchaseItemCollection = new PurchaseItemCollection([$purchaseItem1]);

        $purchaseRepository = $this->createMock(PurchaseRepository::class);
        $purchaseRepository->expects($this->once())
            ->method('findPurchaseItemsBy')
            ->willReturn($purchaseItemCollection);

        $purchaseItemHydratorCollection = $this->createMock(PurchaseItemHydratorCollection::class);
        // Verify that purchaseItemHydratorCollection is not called
        $purchaseItemHydratorCollection->expects($this->never())
            ->method('hydrate');

        $hydrator = $this->getHydrator(
            purchaseRepository: $purchaseRepository,
            purchaseItemHydratorCollection: $purchaseItemHydratorCollection
        );

        $criteria = PurchaseHydrationCriteria::createEmpty()->withPurchaseItem();

        $hydrator->hydrate($collection, $criteria);

        $this->assertEquals($purchaseItem1, $purchase1->getPurchaseItems()->first());
        $this->assertFalse($criteria->needsPurchasableItem());
    }

    /**
     * @throws Exception
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testHydrateThrowsPurchaseHydratorExceptionOnHydratorException(): void
    {
        $purchase1 = PurchaseMother::create();

        $purchaseItem1 = PurchaseItemMother::create(purchaseId: $purchase1->getId());

        $collection = new PurchaseCollection([$purchase1]);
        $purchaseItemCollection = new PurchaseItemCollection([$purchaseItem1]);

        $purchaseRepository = $this->createMock(PurchaseRepository::class);
        $purchaseRepository->expects($this->once())
            ->method('findPurchaseItemsBy')
            ->willReturn($purchaseItemCollection);

        $hydratorException = new HydratorException('Hydration failed');

        $purchaseItemHydratorCollection = $this->createMock(PurchaseItemHydratorCollection::class);
        $purchaseItemHydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willThrowException($hydratorException);

        $hydrator = $this->getHydrator(
            purchaseRepository: $purchaseRepository,
            purchaseItemHydratorCollection: $purchaseItemHydratorCollection
        );

        $criteria = PurchaseHydrationCriteria::createEmpty()->withPurchaseItem(true);

        $this->expectExceptionObject(PurchaseHydratorException::fromPrevious($hydratorException));

        $hydrator->hydrate($collection, $criteria);
    }
}
