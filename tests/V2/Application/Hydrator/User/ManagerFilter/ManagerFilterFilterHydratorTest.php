<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\User\ManagerFilter;

use App\Tests\V2\Mother\Filter\FilterMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\User\ManagerFilter\ManagerFilterFilterHydrator;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterHydrationCriteria;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class ManagerFilterFilterHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?FilterRepository $filterRepository = null,
    ): ManagerFilterFilterHydrator {
        return new ManagerFilterFilterHydrator(
            filterRepository: $filterRepository ?? $this->createMock(FilterRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                ManagerFilterHydrationCriteria::createEmpty()->withFilters()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                ManagerFilterHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws HydratorException
     * @throws CollectionException
     */
    public function testEmptyCollection(): void
    {
        $collection = new ManagerFilterCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            ManagerFilterHydrationCriteria::createEmpty()
                ->withFilters()
        );

        $this->assertEmpty($collection->all());
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws HydratorException
     * @throws CollectionException
     */
    public function testWithFilters(): void
    {
        $filter1 = FilterMother::create(
            id: new Id(1),
            filterCategoryId: new Id(1),
            name: 'Filter 1'
        );
        $filter2 = FilterMother::create(
            id: new Id(2),
            filterCategoryId: new Id(1),
            name: 'Filter 2'
        );
        $filter3 = FilterMother::create(
            id: new Id(3),
            filterCategoryId: new Id(2),
            name: 'Filter 3'
        );

        $filterRepository = $this->createMock(FilterRepository::class);
        $filterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new FilterCollection([$filter1, $filter2, $filter3]));

        $managerFilter1 = ManagerFilterMother::create(userId: new Id(1), filterId: new Id(1));
        $managerFilter2 = ManagerFilterMother::create(userId: new Id(1), filterId: new Id(2));
        $managerFilter3 = ManagerFilterMother::create(userId: new Id(1), filterId: new Id(3));
        $managerFilter4 = ManagerFilterMother::create(userId: new Id(1), filterId: new Id(4));

        $collection = new ManagerFilterCollection([$managerFilter1, $managerFilter2, $managerFilter3, $managerFilter4]);

        $hydrator = $this->getHydrator(
            filterRepository: $filterRepository,
        );

        $hydrator->hydrate($collection, ManagerFilterHydrationCriteria::createEmpty()->withFilters());

        foreach ($collection->all() as $managerFilter) {
            if (4 === $managerFilter->getFilterId()->value()) {
                $this->assertNull($managerFilter->getFilter());
            } else {
                $this->assertNotNull($managerFilter->getFilter());
                $filter = $managerFilter->getFilter();
                switch ($managerFilter->getFilterId()->value()) {
                    case 1:
                        $this->assertEquals(1, $filter->getId()->value());
                        $this->assertEquals('Filter 1', $filter->getName());
                        $this->assertEquals(1, $filter->getCategoryId()->value());
                        break;
                    case 2:
                        $this->assertEquals(2, $filter->getId()->value());
                        $this->assertEquals('Filter 2', $filter->getName());
                        $this->assertEquals(1, $filter->getCategoryId()->value());
                        break;
                    case 3:
                        $this->assertEquals(3, $filter->getId()->value());
                        $this->assertEquals('Filter 3', $filter->getName());
                        $this->assertEquals(2, $filter->getCategoryId()->value());
                        break;
                }
            }
        }
    }
}
