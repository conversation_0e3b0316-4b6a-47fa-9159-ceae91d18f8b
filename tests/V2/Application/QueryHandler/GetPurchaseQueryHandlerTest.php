<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\Entity\User;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Query\GetPurchase;
use App\V2\Application\QueryHandler\GetPurchaseQueryHandler;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetPurchaseQueryHandlerTest extends TestCase
{
    private PurchaseRepository&MockObject $purchaseRepository;
    private GetPurchaseQueryHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->purchaseRepository = $this->createMock(PurchaseRepository::class);
        $this->handler = new GetPurchaseQueryHandler($this->purchaseRepository);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws Exception
     * @throws \Exception
     */
    public function testOk(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $purchase = PurchaseMother::create(
            id: $purchaseId,
            userId: $userId
        );

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId, $userId) {
                return $criteria->getId()->equals($purchaseId)
                    && $criteria->getUserId()->equals($userId);
            }))
            ->willReturn($purchase);

        $result = $this->handler->handle($query);

        $this->assertSame($purchase, $result);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws \Exception
     */
    public function testThrowsPurchaseNotFoundExceptionWhenUserIsNotOwner(): void
    {
        $otherUserId = IdMother::create(456);
        $purchaseId = UuidMother::create();

        $otherUser = $this->createMock(User::class);
        $otherUser->method('getId')->willReturn($otherUserId->value());

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $otherUser
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId, $otherUserId) {
                return $criteria->getId()->equals($purchaseId)
                    && $criteria->getUserId()->equals($otherUserId);
            }))
            ->willThrowException(new PurchaseNotFoundException());

        $this->expectException(PurchaseNotFoundException::class);
        $this->handler->handle($query);
    }

    /**
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     */
    public function testThrowsPurchaseNotFoundExceptionWhenPurchaseDoesNotExist(): void
    {
        $userIdInt = 123;
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userIdInt);

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new PurchaseNotFoundException());

        $this->expectException(PurchaseNotFoundException::class);

        $this->handler->handle($query);
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws PurchaseNotFoundException
     * @throws \Exception
     * @throws Exception
     */
    public function testThrowsRepositoryExceptionWhenRepositoryFails(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new PurchaseRepositoryException('Database error'));

        $this->expectException(PurchaseRepositoryException::class);
        $this->expectExceptionMessage('Database error');

        $this->handler->handle($query);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws \Exception
     * @throws Exception
     */
    public function testThrowsCriteriaExceptionWhenCriteriaIsInvalid(): void
    {
        $userId = IdMother::create(123);
        $purchaseId = UuidMother::create();

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $query = new GetPurchase(
            purchaseId: $purchaseId,
            purchaseOwner: $user
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new CriteriaException('Criteria error'));

        $this->expectException(CriteriaException::class);
        $this->expectExceptionMessage('Criteria error');

        $this->handler->handle($query);
    }
}
