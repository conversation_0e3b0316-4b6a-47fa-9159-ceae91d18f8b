<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Campus;

use App\Service\SettingsService;
use App\V2\Application\Query\Campus\GetLocalesQuery;
use App\V2\Application\QueryHandler\Campus\GetLocalesQueryHandler;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetCampusLocalesQueryHandlerTest extends TestCase
{
    private GetLocalesQueryHandler $handler;
    private SettingsService|MockObject $settingsService;

    private array $locales;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->handler = new GetLocalesQueryHandler($this->settingsService);
        $this->locales = ['es', 'en'];
    }

    public function testHandleCampusLocales(): void
    {
        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.languages', [])
            ->willReturn($this->locales);

        $result = $this->handler->handle(new GetLocalesQuery());

        $this->assertSame($this->locales, $result);
    }

    public function testHandleEmptyCampusLocales(): void
    {
        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.languages', [])
            ->willReturn([]);

        $result = $this->handler->handle(new GetLocalesQuery());

        $this->assertSame([], $result);
    }
}
