<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Service\SettingsService;
use App\V2\Application\Query\Admin\GetLocalesQuery;
use App\V2\Application\QueryHandler\Admin\GetLocalesQueryHandler;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetAdminLocalesQueryHandlerTest extends TestCase
{
    private GetLocalesQueryHandler $handler;
    private SettingsService|MockObject $settingsService;

    private array $adminLocales;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->handler = new GetLocalesQueryHandler($this->settingsService);
        $this->adminLocales = ['es, en'];
    }

    public function testHandleAdminLocales(): void
    {
        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.languages.admin', [])
            ->willReturn($this->adminLocales);

        $result = $this->handler->handle(new GetLocalesQuery());

        $this->assertSame($this->adminLocales, $result);
    }

    public function testHandleEmptyAdminLocales(): void
    {
        $this->settingsService
            ->expects($this->once())
            ->method('get')
            ->with('app.languages.admin', [])
            ->willReturn([]);

        $result = $this->handler->handle(new GetLocalesQuery());

        $this->assertSame([], $result);
    }
}
