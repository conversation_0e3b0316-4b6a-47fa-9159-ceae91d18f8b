<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Entity\User;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\User\FilterMother;
use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Application\Hydrator\User\UserFilter\UserFilterHydratorCollection;
use App\V2\Application\Query\Admin\GetUserFiltersQuery;
use App\V2\Application\QueryHandler\Admin\GetUserFiltersQueryHandler;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\GetUserFiltersQueryHandlerException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class GetUserFiltersQueryHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?UserRepository $userRepository = null,
        ?UserFilterRepository $userFilterRepository = null,
        ?UserFilterHydratorCollection $hydratorCollection = null,
    ): GetUserFiltersQueryHandler {
        return new GetUserFiltersQueryHandler(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            userFilterRepository: $userFilterRepository ?? $this->createMock(UserFilterRepository::class),
            hydratorCollection: $hydratorCollection ?? $this->createMock(UserFilterHydratorCollection::class)
        );
    }

    public function testHandleWithFilters(): void
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userCriteria = UserCriteria::createById(new Id(1));
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->with($userCriteria)
            ->willReturn(UserMother::create(id: 1));

        $userFilter1 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1),
        );

        $userFilter2 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2),
        );

        $userFilter3 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(3),
        );

        $userFilterRepository = $this->createMock(UserFilterRepository::class);
        $userFilterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new UserFilterCollection([$userFilter1, $userFilter2, $userFilter3]));

        $hydratorCollection = $this->createMock(UserFilterHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(function (UserFilterCollection $collection, HydrationCriteria $criteria) {
                foreach ($collection->all() as $userFilter) {
                    $userFilter->setFilter(
                        match ($userFilter->getFilterId()->value()) {
                            1 => FilterMother::create(id: new Id(1), name: 'Filter 1', categoryId: new Id(1)),
                            2 => FilterMother::create(id: new Id(2), name: 'Filter 2', categoryId: new Id(1)),
                            3 => FilterMother::create(id: new Id(3), name: 'Filter 3', categoryId: new Id(2)),
                            default => $this->fail('Unexpected filter id')
                        }
                    );
                }
            });

        $handler = $this->getHandler(
            userRepository: $userRepository,
            userFilterRepository: $userFilterRepository,
            hydratorCollection: $hydratorCollection,
        );
        $result = $handler->handle(
            new GetUserFiltersQuery(
                userId: new Id(1),
                withFilters: true,
            )
        );
        $this->assertCount(3, $result);
        foreach ($result->all() as $userFilter) {
            $this->assertNotNull($userFilter->getFilter());
        }
    }

    public static function provideHandleFiltersWithExceptions(): \Generator
    {
        yield 'user not found' => [
            'user' => null,
            'userFiltersFindBy' => null,
            'hydratorCallable' => null,
            'expectedException' => new UserNotFoundException(),
        ];

        yield 'hydrator exception' => [
            'user' => UserMother::create(id: 1),
            'userFiltersFindBy' => fn () => new UserFilterCollection([UserFilterMother::create()]),
            'hydratorCallable' => fn () => throw new HydratorException(),
            'expectedException' => GetUserFiltersQueryHandlerException::fromPrevious(new HydratorException()),
        ];
    }

    #[DataProvider('provideHandleFiltersWithExceptions')]
    public function testHandleWithFiltersExceptions(
        ?User $user,
        ?callable $userFiltersFindBy,
        ?callable $hydratorCallable,
        $expectedException,
    ): void {
        $userRepository = $this->createMock(UserRepository::class);
        $findOneUser = $userRepository->expects($this->once())
            ->method('findOneBy');
        if (null !== $user) {
            $findOneUser->willReturn($user);
        } else {
            $findOneUser->willThrowException(new UserNotFoundException());
        }

        $userFilterRepository = $this->createMock(UserFilterRepository::class);
        if (null !== $userFiltersFindBy) {
            $userFilterRepository->expects($this->once())
                ->method('findBy')
                ->willReturnCallback($userFiltersFindBy);
        }

        $hydratorCollection = $this->createMock(UserFilterHydratorCollection::class);
        if (null !== $userFiltersFindBy) {
            $hydratorCollection->expects($this->once())
                ->method('hydrate')
                ->willReturnCallback($hydratorCallable);
        }

        $handler = $this->getHandler(
            userRepository: $userRepository,
            userFilterRepository: $userFilterRepository,
            hydratorCollection: $hydratorCollection,
        );
        $this->expectExceptionObject($expectedException);
        $handler->handle(
            new GetUserFiltersQuery(
                userId: new Id(1),
                withFilters: true,
            )
        );
    }
}
