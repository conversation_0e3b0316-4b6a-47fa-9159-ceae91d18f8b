<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\User\FilterMother;
use App\Tests\V2\Mother\User\ManagerFilter\ManagerFilterMother;
use App\V2\Application\Hydrator\User\ManagerFilter\ManagerFilterHydratorCollection;
use App\V2\Application\Query\Admin\GetManagerFiltersQuery;
use App\V2\Application\QueryHandler\Admin\GetManagerFiltersQueryHandler;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\GetManagerFiltersQueryHandlerException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class GetManagerFiltersQueryHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?UserRepository $userRepository = null,
        ?ManagerFilterRepository $managerFilterRepository = null,
        ?ManagerFilterHydratorCollection $hydratorCollection = null,
    ): GetManagerFiltersQueryHandler {
        return new GetManagerFiltersQueryHandler(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            managerFilterRepository: $managerFilterRepository ?? $this->createMock(ManagerFilterRepository::class),
            hydratorCollection: $hydratorCollection ?? $this->createMock(ManagerFilterHydratorCollection::class),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws GetManagerFiltersQueryHandlerException
     * @throws CriteriaException
     * @throws CollectionException
     * @throws UserNotFoundException
     */
    public function testHandleWithFilters(): void
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(UserMother::create(id: 1));

        $managerFilterRepository = $this->createMock(ManagerFilterRepository::class);
        $managerFilterRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new ManagerFilterCollection([
                ManagerFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(1),
                ),
                ManagerFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(2),
                ),
                ManagerFilterMother::create(
                    userId: new Id(1),
                    filterId: new Id(3),
                ),
            ]));

        $hydratorCollection = $this->createMock(ManagerFilterHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(function (ManagerFilterCollection $collection) {
                foreach ($collection->all() as $managerFilter) {
                    $managerFilter->setFilter(
                        FilterMother::create(
                            id: $managerFilter->getFilterId(),
                            name: 'Filter ' . $managerFilter->getFilterId()->value(),
                            categoryId: new Id(1)
                        )
                    );
                }
            });

        $handler = $this->getHandler(
            userRepository: $userRepository,
            managerFilterRepository: $managerFilterRepository,
            hydratorCollection: $hydratorCollection,
        );

        $result = $handler->handle(new GetManagerFiltersQuery(
            userId: new Id(1),
            withFilters: true
        ));

        $this->assertCount(3, $result);
        foreach ($result->all() as $managerFilter) {
            $this->assertNotNull($managerFilter->getFilter());
        }
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws GetManagerFiltersQueryHandlerException
     * @throws CriteriaException
     * @throws UserNotFoundException
     */
    public function testHydratorException(): void
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(UserMother::create(id: 1));

        $hydratorCollection = $this->createMock(ManagerFilterHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willThrowException(new HydratorException());

        $handler = $this->getHandler(
            userRepository: $userRepository,
            hydratorCollection: $hydratorCollection,
        );

        $this->expectExceptionObject(GetManagerFiltersQueryHandlerException::fromPrevious(new HydratorException()));
        $handler->handle(new GetManagerFiltersQuery(
            userId: new Id(1),
            withFilters: true
        ));
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws GetManagerFiltersQueryHandlerException
     * @throws CriteriaException
     * @throws UserNotFoundException
     */
    public function testUserNotFound(): void
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new UserNotFoundException());
        $handler = $this->getHandler(
            userRepository: $userRepository,
        );

        $this->expectExceptionObject(new UserNotFoundException());
        $handler->handle(new GetManagerFiltersQuery(
            userId: new Id(1),
            withFilters: true
        ));
    }
}
