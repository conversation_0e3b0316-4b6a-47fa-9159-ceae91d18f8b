<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Tests\V2\Mother\Purchase\PurchasableItemCollectionMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\V2\Application\Query\Admin\GetPurchasableItemsQuery;
use App\V2\Application\QueryHandler\Admin\GetPurchasableItemsQueryHandler;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PaginatedPurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetPurchasableItemsQueryHandlerTest extends TestCase
{
    private PurchasableItemRepository|MockObject $purchasableItemRepository;
    private GetPurchasableItemsQueryHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->purchasableItemRepository = $this->createMock(PurchasableItemRepository::class);
        $this->handler = new GetPurchasableItemsQueryHandler(
            purchasableItemRepository: $this->purchasableItemRepository,
        );
    }

    /**
     * @throws PurchasableItemRepositoryException
     */
    #[DataProvider('correctCriteriaHandlingProvider')]
    public function testCorrectCriteriaHandling(
        GetPurchasableItemsQuery $query,
        PurchasableItemCriteria $expectedCriteria,
        PurchasableItemCollection $expectedCollection,
        int $expectedTotalItems
    ): void {
        // Mock the repository to return a specific collection and count based on criteria
        $this->purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->with($expectedCriteria)
            ->willReturn($expectedCollection);

        $this->purchasableItemRepository->expects($this->once())
            ->method('countBy')
            ->with($expectedCriteria)
            ->willReturn($expectedTotalItems);

        $result = $this->handler->handle($query);

        $this->assertInstanceOf(PaginatedPurchasableItemCollection::class, $result);
        $this->assertEquals($expectedCollection, $result->getCollection());
        $this->assertEquals($expectedTotalItems, $result->getTotalItems());
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws CollectionException
     */
    public function testHandleWithEmptyResults(): void
    {
        $criteria = PurchasableItemCriteria::createEmpty();
        $query = new GetPurchasableItemsQuery($criteria);
        $emptyCollection = PurchasableItemCollectionMother::empty();

        $this->purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willReturn($emptyCollection);

        $this->purchasableItemRepository->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willReturn(0);

        $result = $this->handler->handle($query);

        $this->assertInstanceOf(PaginatedPurchasableItemCollection::class, $result);
        $this->assertTrue($result->getCollection()->isEmpty());
        $this->assertEquals(0, $result->getTotalItems());
    }

    /**
     * @throws PurchasableItemRepositoryException
     */
    public function testHandleThrowsRepositoryException(): void
    {
        $criteria = PurchasableItemCriteria::createEmpty();
        $query = new GetPurchasableItemsQuery($criteria);

        $this->purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willThrowException(new PurchasableItemRepositoryException('Repository error'));

        $this->expectException(PurchasableItemRepositoryException::class);
        $this->expectExceptionMessage('Repository error');

        $this->handler->handle($query);
    }

    /**
     * @throws CollectionException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testHandleThrowsRepositoryExceptionOnCountBy(): void
    {
        $criteria = PurchasableItemCriteria::createEmpty();
        $query = new GetPurchasableItemsQuery($criteria);
        $collection = PurchasableItemCollectionMother::withSingleItem();

        $this->purchasableItemRepository->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willReturn($collection);

        $this->purchasableItemRepository->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willThrowException(new PurchasableItemRepositoryException('Count error'));

        $this->expectException(PurchasableItemRepositoryException::class);
        $this->expectExceptionMessage('Count error');

        $this->handler->handle($query);
    }

    /**
     * @throws CollectionException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public static function correctCriteriaHandlingProvider(): \Generator
    {
        yield 'Empty criteria returns all items' => [
            'query' => new GetPurchasableItemsQuery(
                criteria: PurchasableItemCriteria::createEmpty(),
            ),
            'expectedCriteria' => PurchasableItemCriteria::createEmpty(),
            'expectedCollection' => PurchasableItemCollectionMother::createWithCount(3),
            'expectedTotalItems' => 3,
        ];

        yield 'Search criteria filters items' => [
            'query' => new GetPurchasableItemsQuery(
                criteria: PurchasableItemCriteria::createEmpty()
                    ->filterBySearch('Python Course'),
            ),
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('Python Course'),
            'expectedCollection' => PurchasableItemCollectionMother::withSingleItem(),
            'expectedTotalItems' => 1,
        ];

        yield 'Price range criteria filters items' => [
            'query' => new GetPurchasableItemsQuery(
                criteria: PurchasableItemCriteria::createEmpty()
                    ->filterByMinPrice(MoneyMother::create(amount: 1000))
                    ->filterByMaxPrice(MoneyMother::create(amount: 5000)),
            ),
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 1000))
                ->filterByMaxPrice(MoneyMother::create(amount: 5000)),
            'expectedCollection' => PurchasableItemCollectionMother::createWithCount(2),
            'expectedTotalItems' => 2,
        ];

        yield 'Active status criteria filters items' => [
            'query' => new GetPurchasableItemsQuery(
                criteria: PurchasableItemCriteria::createEmpty()
                    ->filterByIsActive(true),
            ),
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByIsActive(true),
            'expectedCollection' => PurchasableItemCollectionMother::createWithCount(5),
            'expectedTotalItems' => 5,
        ];

        yield 'Resource criteria filters items' => [
            'query' => new GetPurchasableItemsQuery(
                criteria: PurchasableItemCriteria::createEmpty()
                    ->filterByResource(ResourceMother::create(id: new Id(1))),
            ),
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterByResource(ResourceMother::create(id: new Id(1))),
            'expectedCollection' => PurchasableItemCollectionMother::withSingleItem(),
            'expectedTotalItems' => 1,
        ];

        yield 'Complex criteria with multiple filters' => [
            'query' => new GetPurchasableItemsQuery(
                criteria: PurchasableItemCriteria::createEmpty()
                    ->filterBySearch('Advanced')
                    ->filterByMinPrice(MoneyMother::create(amount: 2000))
                    ->filterByIsActive(true),
            ),
            'expectedCriteria' => PurchasableItemCriteria::createEmpty()
                ->filterBySearch('Advanced')
                ->filterByMinPrice(MoneyMother::create(amount: 2000))
                ->filterByIsActive(true),
            'expectedCollection' => PurchasableItemCollectionMother::empty(),
            'expectedTotalItems' => 0,
        ];
    }
}
