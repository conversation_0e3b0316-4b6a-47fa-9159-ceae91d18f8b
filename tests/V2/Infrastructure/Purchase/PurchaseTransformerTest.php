<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase;

use App\Tests\V2\Mother\Purchase\PurchaseItemMother;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Financial\TaxRateMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Purchase\PurchaseTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PurchaseTransformerTest extends TestCase
{
    #[DataProvider('validPurchaseProvider')]
    public function testFromPurchaseToArrayWithValidInputs(
        Purchase $purchase,
        array $expectedArray,
    ): void {
        $result = PurchaseTransformer::fromPurchaseToArray($purchase);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
    }

    public function testFromPurchaseToArrayMethodExists(): void
    {
        $this->assertTrue(method_exists(PurchaseTransformer::class, 'fromPurchaseToArray'));
        $this->assertTrue(\is_callable([PurchaseTransformer::class, 'fromPurchaseToArray']));
    }

    #[DataProvider('returnValueConsistencyProvider')]
    public function testFromPurchaseToArrayReturnValueConsistency(Purchase $purchase): void
    {
        $result1 = PurchaseTransformer::fromPurchaseToArray($purchase);
        $result2 = PurchaseTransformer::fromPurchaseToArray($purchase);

        $this->assertIsArray($result1);
        $this->assertIsArray($result2);
        $this->assertEquals($result1, $result2);
    }

    #[DataProvider('dataTransformationProvider')]
    public function testDataTransformationAccuracy(
        Purchase $purchase,
        string $expectedId,
        int $expectedUserId,
        string $expectedStatus,
        int $expectedAmount,
        string $expectedCurrencyCode,
        float $expectedTaxRate,
        int $expectedTaxAmount,
        string $expectedCreatedAt,
    ): void {
        $result = PurchaseTransformer::fromPurchaseToArray($purchase);

        $this->assertSame($expectedId, $result['id']);
        $this->assertSame($expectedUserId, $result['user_id']);
        $this->assertSame($expectedStatus, $result['status']);
        $this->assertSame($expectedAmount, $result['amount']);
        $this->assertSame($expectedCurrencyCode, $result['currency_code']);
        $this->assertSame($expectedTaxRate, $result['tax_rate']);
        $this->assertSame($expectedTaxAmount, $result['tax_amount']);
        $this->assertSame($expectedCreatedAt, $result['created_at']);
    }

    #[DataProvider('purchaseItemCollectionProvider')]
    public function testFromPurchaseItemCollectionToArrayWithValidInputs(
        PurchaseItemCollection $collection,
        array $expectedArray,
    ): void {
        $result = PurchaseTransformer::fromPurchaseItemCollectionToArray($collection);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
        $this->assertCount($collection->count(), $result);
    }

    public function testFromPurchaseItemCollectionToArrayMethodExists(): void
    {
        $this->assertTrue(method_exists(PurchaseTransformer::class, 'fromPurchaseItemCollectionToArray'));
        $this->assertTrue(\is_callable([PurchaseTransformer::class, 'fromPurchaseItemCollectionToArray']));
    }

    #[DataProvider('purchaseCollectionProvider')]
    public function testFromPurchaseCollectionToArrayWithValidInputs(
        PurchaseCollection $collection,
        array $expectedArray,
    ): void {
        $result = PurchaseTransformer::fromPurchaseCollectionToArray($collection);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
        $this->assertCount($collection->count(), $result);
    }

    public function testFromPurchaseCollectionToArrayMethodExists(): void
    {
        $this->assertTrue(method_exists(PurchaseTransformer::class, 'fromPurchaseCollectionToArray'));
        $this->assertTrue(\is_callable([PurchaseTransformer::class, 'fromPurchaseCollectionToArray']));
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function validPurchaseProvider(): \Generator
    {
        $uuid = UuidMother::create();
        $userId = IdMother::create(123);
        $createdAt = new \DateTimeImmutable('2023-01-01 10:00:00');

        $purchase = PurchaseMother::create(
            id: $uuid,
            userId: $userId,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 2500, currency: new Currency(CurrencyCode::EUR)),
            taxRate: TaxRateMother::create(rate: 0.21), // 21%
            taxAmount: MoneyMother::create(amount: 525, currency: new Currency(CurrencyCode::EUR)),
            createdAt: $createdAt
        );

        yield 'basic purchase without items' => [
            'purchase' => $purchase,
            'expectedArray' => [
                'id' => $uuid->value(),
                'user_id' => 123,
                'status' => 'completed',
                'amount' => 2500,
                'currency_code' => 'EUR',
                'tax_rate' => 0.21,
                'tax_amount' => 525,
                'created_at' => '2023-01-01 10:00:00',
            ],
        ];

        // Create a purchase with items
        $purchaseWithItems = clone $purchase;
        $purchaseItem1Id = UuidMother::create();
        $purchaseItem2Id = UuidMother::create();

        $purchaseItem1 = PurchaseItemMother::create(
            id: $purchaseItem1Id,
            purchaseId: $uuid,
            price: MoneyMother::create(amount: 1500, currency: new Currency(CurrencyCode::EUR))
        );
        $purchaseItem2 = PurchaseItemMother::create(
            id: $purchaseItem2Id,
            purchaseId: $uuid,
            price: MoneyMother::create(amount: 1000, currency: new Currency(CurrencyCode::EUR))
        );

        $purchaseItemCollection = new PurchaseItemCollection([$purchaseItem1, $purchaseItem2]);
        $purchaseWithItems->setPurchaseItems($purchaseItemCollection);

        yield 'purchase with items' => [
            'purchase' => $purchaseWithItems,
            'expectedArray' => [
                'id' => $uuid->value(),
                'user_id' => 123,
                'status' => 'completed',
                'amount' => 2500,
                'currency_code' => 'EUR',
                'tax_rate' => 0.21,
                'tax_amount' => 525,
                'created_at' => '2023-01-01 10:00:00',
                'items' => [
                    [
                        'id' => $purchaseItem1Id->value(),
                        'price_amount' => 1500,
                        'price_currency' => 'EUR',
                    ],
                    [
                        'id' => $purchaseItem2Id->value(),
                        'price_amount' => 1000,
                        'price_currency' => 'EUR',
                    ],
                ],
            ],
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function returnValueConsistencyProvider(): \Generator
    {
        yield 'returns consistent array for same purchase' => [
            'purchase' => PurchaseMother::create(
                status: PurchaseStatus::Pending
            ),
        ];

        yield 'returns consistent array for different currencies' => [
            'purchase' => PurchaseMother::create(
                amount: MoneyMother::create(amount: 5000, currency: new Currency(CurrencyCode::USD)),
                taxAmount: MoneyMother::create(amount: 1000, currency: new Currency(CurrencyCode::USD))
            ),
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function dataTransformationProvider(): \Generator
    {
        $uuid = UuidMother::create();
        $userId = new Id(456);
        $createdAt = new \DateTimeImmutable('2023-02-15 15:30:00');

        yield 'pending purchase transformation' => [
            'purchase' => PurchaseMother::create(
                id: $uuid,
                userId: $userId,
                status: PurchaseStatus::Pending,
                amount: MoneyMother::create(amount: 3000, currency: new Currency(CurrencyCode::EUR)),
                taxRate: TaxRateMother::create(rate: 0.21), // 21%
                taxAmount: MoneyMother::create(amount: 630, currency: new Currency(CurrencyCode::EUR)),
                createdAt: $createdAt
            ),
            'expectedId' => $uuid->value(),
            'expectedUserId' => 456,
            'expectedStatus' => 'pending',
            'expectedAmount' => 3000,
            'expectedCurrencyCode' => 'EUR',
            'expectedTaxRate' => 0.21,
            'expectedTaxAmount' => 630,
            'expectedCreatedAt' => '2023-02-15 15:30:00',
        ];

        $uuid2 = UuidMother::create();
        $userId2 = new Id(789);
        $createdAt2 = new \DateTimeImmutable('2023-03-20 09:45:00');

        yield 'cancelled purchase transformation' => [
            'purchase' => PurchaseMother::create(
                id: $uuid2,
                userId: $userId2,
                status: PurchaseStatus::Cancelled,
                amount: MoneyMother::create(amount: 9900, currency: new Currency(CurrencyCode::USD)),
                taxRate: TaxRateMother::create(rate: 0.10), // 10%
                taxAmount: MoneyMother::create(amount: 990, currency: new Currency(CurrencyCode::USD)),
                createdAt: $createdAt2
            ),
            'expectedId' => $uuid2->value(),
            'expectedUserId' => 789,
            'expectedStatus' => 'cancelled',
            'expectedAmount' => 9900,
            'expectedCurrencyCode' => 'USD',
            'expectedTaxRate' => 0.10,
            'expectedTaxAmount' => 990,
            'expectedCreatedAt' => '2023-03-20 09:45:00',
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws CollectionException
     */
    public static function purchaseItemCollectionProvider(): \Generator
    {
        $item1Id = UuidMother::create();
        $item2Id = UuidMother::create();

        $item1 = PurchaseItemMother::create(
            id: $item1Id,
            price: MoneyMother::create(amount: 1000, currency: new Currency(CurrencyCode::EUR))
        );
        $item2 = PurchaseItemMother::create(
            id: $item2Id,
            price: MoneyMother::create(amount: 2000, currency: new Currency(CurrencyCode::EUR))
        );

        $collection = new PurchaseItemCollection([$item1, $item2]);

        yield 'collection with multiple items' => [
            'collection' => $collection,
            'expectedArray' => [
                [
                    'id' => $item1Id->value(),
                    'price_amount' => 1000,
                    'price_currency' => 'EUR',
                ],
                [
                    'id' => $item2Id->value(),
                    'price_amount' => 2000,
                    'price_currency' => 'EUR',
                ],
            ],
        ];

        yield 'empty collection' => [
            'collection' => new PurchaseItemCollection([]),
            'expectedArray' => [],
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function purchaseCollectionProvider(): \Generator
    {
        $purchase1Id = UuidMother::create();
        $purchase2Id = UuidMother::create();

        $purchase1 = PurchaseMother::create(
            id: $purchase1Id,
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 1500)
        );
        $purchase2 = PurchaseMother::create(
            id: $purchase2Id,
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 3000)
        );

        $collection = new PurchaseCollection([$purchase1, $purchase2]);

        yield 'collection with multiple purchases' => [
            'collection' => $collection,
            'expectedArray' => [
                PurchaseTransformer::fromPurchaseToArray($purchase1),
                PurchaseTransformer::fromPurchaseToArray($purchase2),
            ],
        ];

        yield 'empty collection' => [
            'collection' => new PurchaseCollection([]),
            'expectedArray' => [],
        ];
    }
}
