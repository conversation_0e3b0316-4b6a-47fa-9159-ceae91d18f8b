<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase;

use App\Tests\V2\Mother\Purchase\PurchasableItemCollectionMother;
use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Purchase\PurchasableItemTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PurchasableItemTransformerTest extends TestCase
{
    #[DataProvider('validPurchasableItemProvider')]
    public function testFromPurchasableItemToArrayWithValidInputs(
        PurchasableItem $item,
        array $expectedArray,
    ): void {
        $result = PurchasableItemTransformer::fromPurchasableItemToArray($item);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
    }

    public function testFromPurchasableItemToArrayMethodExists(): void
    {
        $this->assertTrue(method_exists(PurchasableItemTransformer::class, 'fromPurchasableItemToArray'));
        $this->assertTrue(\is_callable([PurchasableItemTransformer::class, 'fromPurchasableItemToArray']));
    }

    #[DataProvider('returnValueConsistencyProvider')]
    public function testFromPurchasableItemToArrayReturnValueConsistency(PurchasableItem $item): void
    {
        $result1 = PurchasableItemTransformer::fromPurchasableItemToArray($item);
        $result2 = PurchasableItemTransformer::fromPurchasableItemToArray($item);

        $this->assertIsArray($result1);
        $this->assertIsArray($result2);
        $this->assertEquals($result1, $result2);
    }

    #[DataProvider('dataTransformationProvider')]
    public function testDataTransformationAccuracy(
        PurchasableItem $item,
        string $expectedId,
        string $expectedName,
        string $expectedDescription,
        int $expectedPriceAmount,
        string $expectedPriceCurrency,
        string $expectedResourceType,
        mixed $expectedResourceId,
        bool $expectedIsActive,
    ): void {
        $result = PurchasableItemTransformer::fromPurchasableItemToArray($item);

        $this->assertSame($expectedId, $result['id']);
        $this->assertSame($expectedName, $result['name']);
        $this->assertSame($expectedDescription, $result['description']);
        $this->assertSame($expectedPriceAmount, $result['price_amount']);
        $this->assertSame($expectedPriceCurrency, $result['price_currency']);
        $this->assertSame($expectedResourceType, $result['resource_type']);
        $this->assertSame($expectedResourceId, $result['resource_id']);
        $this->assertSame($expectedIsActive, $result['is_active']);
    }

    #[DataProvider('collectionProvider')]
    public function testFromCollectionToArrayWithValidInputs(
        PurchasableItemCollection $collection,
        array $expectedArray,
    ): void {
        $result = PurchasableItemTransformer::fromCollectionToArray($collection);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
        $this->assertCount($collection->count(), $result);
    }

    public function testFromCollectionToArrayMethodExists(): void
    {
        $this->assertTrue(method_exists(PurchasableItemTransformer::class, 'fromCollectionToArray'));
        $this->assertTrue(\is_callable([PurchasableItemTransformer::class, 'fromCollectionToArray']));
    }

    #[DataProvider('collectionReturnValueConsistencyProvider')]
    public function testFromCollectionToArrayReturnValueConsistency(PurchasableItemCollection $collection): void
    {
        $result1 = PurchasableItemTransformer::fromCollectionToArray($collection);
        $result2 = PurchasableItemTransformer::fromCollectionToArray($collection);

        $this->assertIsArray($result1);
        $this->assertIsArray($result2);
        $this->assertEquals($result1, $result2);
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public static function validPurchasableItemProvider(): \Generator
    {
        $uuid = UuidMother::create();
        $item = PurchasableItemMother::create(
            id: $uuid,
            name: 'Test Course',
            description: 'Test Description',
            price: MoneyMother::create(amount: 2500, currency: new Currency(CurrencyCode::EUR)),
            resource: ResourceMother::createCourse(new Id(123)),
            isActive: true
        );

        yield 'basic purchasable item' => [
            'item' => $item,
            'expectedArray' => [
                'id' => $uuid->value(),
                'name' => 'Test Course',
                'description' => 'Test Description',
                'price_amount' => 2500,
                'price_currency' => 'EUR',
                'resource_type' => 'course',
                'resource_id' => 123,
                'is_active' => true,
            ],
        ];
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public static function returnValueConsistencyProvider(): \Generator
    {
        yield 'returns consistent array for same item' => [
            'item' => PurchasableItemMother::create(name: 'Consistency Test'),
        ];

        yield 'returns consistent array for different currencies' => [
            'item' => PurchasableItemMother::create(
                price: MoneyMother::create(amount: 5000, currency: new Currency(CurrencyCode::USD))
            ),
        ];
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public static function dataTransformationProvider(): \Generator
    {
        $uuid = UuidMother::create();

        yield 'course transformation' => [
            'item' => PurchasableItemMother::create(
                id: $uuid,
                name: 'PHP Course',
                description: 'Learn PHP programming',
                price: MoneyMother::create(amount: 3000, currency: new Currency(CurrencyCode::EUR)),
                resource: ResourceMother::createCourse(new Id(456)),
                isActive: false
            ),
            'expectedId' => $uuid->value(),
            'expectedName' => 'PHP Course',
            'expectedDescription' => 'Learn PHP programming',
            'expectedPriceAmount' => 3000,
            'expectedPriceCurrency' => 'EUR',
            'expectedResourceType' => 'course',
            'expectedResourceId' => 456,
            'expectedIsActive' => false,
        ];

        $subscriptionUuid = UuidMother::create();
        $subscriptionResourceUuid = UuidMother::create();

        yield 'subscription transformation' => [
            'item' => PurchasableItemMother::create(
                id: $subscriptionUuid,
                name: 'Premium Subscription',
                description: 'Access to all courses',
                price: MoneyMother::create(amount: 9900, currency: new Currency(CurrencyCode::USD)),
                resource: ResourceMother::createSubscription($subscriptionResourceUuid),
                isActive: true
            ),
            'expectedId' => $subscriptionUuid->value(),
            'expectedName' => 'Premium Subscription',
            'expectedDescription' => 'Access to all courses',
            'expectedPriceAmount' => 9900,
            'expectedPriceCurrency' => 'USD',
            'expectedResourceType' => 'subscription',
            'expectedResourceId' => $subscriptionResourceUuid->value(),
            'expectedIsActive' => true,
        ];
    }

    /**
     * @throws CollectionException
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public static function collectionProvider(): \Generator
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: MoneyMother::create(amount: 1000)
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: MoneyMother::create(amount: 2000)
        );

        $collection = new PurchasableItemCollection([$item1, $item2]);

        yield 'collection with multiple items' => [
            'collection' => $collection,
            'expectedArray' => [
                PurchasableItemTransformer::fromPurchasableItemToArray($item1),
                PurchasableItemTransformer::fromPurchasableItemToArray($item2),
            ],
        ];

        yield 'empty collection' => [
            'collection' => PurchasableItemCollectionMother::empty(),
            'expectedArray' => [],
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws InvalidPurchasableItemException
     * @throws CollectionException
     */
    public static function collectionReturnValueConsistencyProvider(): \Generator
    {
        yield 'returns consistent array for same collection' => [
            'collection' => PurchasableItemCollectionMother::createWithCount(2),
        ];

        yield 'returns consistent array for empty collection' => [
            'collection' => PurchasableItemCollectionMother::empty(),
        ];
    }
}
