<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase;

use App\V2\Application\DTO\Purchase\PatchPurchasableItemInputDTO;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Infrastructure\Purchase\PatchPurchasableItemInputTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PatchPurchasableItemInputTransformerTest extends TestCase
{
    /**
     * Test basic functionality with valid inputs.
     */
    #[DataProvider('validPayloadProvider')]
    public function testFromPayloadWithValidInputs(array $payload, PatchPurchasableItemInputDTO $expectedInput): void
    {
        $input = PatchPurchasableItemInputTransformer::fromPayload($payload);

        $this->assertEquals($expectedInput, $input);
    }

    public function testFromPayloadMethodExists(): void
    {
        $this->assertTrue(method_exists(PatchPurchasableItemInputTransformer::class, 'fromPayload'));
        $this->assertTrue(\is_callable([PatchPurchasableItemInputTransformer::class, 'fromPayload']));
    }

    /**
     * @throws InvalidCurrencyCodeException
     */
    public function testFromPayloadReturnValueConsistency(): void
    {
        $payload = [
            'price_amount' => 1000,
            'price_currency' => 'EUR',
            'is_active' => true,
        ];

        $result1 = PatchPurchasableItemInputTransformer::fromPayload($payload);
        $result2 = PatchPurchasableItemInputTransformer::fromPayload($payload);

        $this->assertEquals($result1, $result2);
        $this->assertInstanceOf(PatchPurchasableItemInputDTO::class, $result1);
        $this->assertInstanceOf(PatchPurchasableItemInputDTO::class, $result2);
    }

    public function testInterfaceCompliance(): void
    {
        $payload = [];
        $result = PatchPurchasableItemInputTransformer::fromPayload($payload);

        $this->assertInstanceOf(PatchPurchasableItemInputDTO::class, $result);
    }

    public function testIntegrationContext(): void
    {
        $payload = [
            'price_amount' => 2500,
            'price_currency' => 'USD',
            'is_active' => false,
        ];

        $input = PatchPurchasableItemInputTransformer::fromPayload($payload);

        // Verify the input can be used as expected
        $this->assertNotNull($input->getPrice());
        $this->assertEquals(2500, $input->getPrice()->value());
        $this->assertEquals(CurrencyCode::USD, $input->getPrice()->currency()->code());
        $this->assertFalse($input->getIsActive());
    }

    /**
     * @throws InvalidCurrencyCodeException
     */
    #[DataProvider('invalidPayloadProvider')]
    public function testFromPayloadWithInvalidInputs(array $payload, string $expectedException): void
    {
        $this->expectException($expectedException);

        PatchPurchasableItemInputTransformer::fromPayload($payload);
    }

    /**
     * @throws InvalidCurrencyCodeException
     */
    #[DataProvider('partialUpdateProvider')]
    public function testPatchSemantics(array $payload, PatchPurchasableItemInputDTO $expectedInput): void
    {
        $input = PatchPurchasableItemInputTransformer::fromPayload($payload);

        $this->assertEquals($expectedInput, $input);
    }

    /**
     * @throws InvalidCurrencyCodeException
     */
    #[DataProvider('priceInCentsProvider')]
    public function testPriceValidationInCents(array $payload, string $expectedDescription): void
    {
        $input = PatchPurchasableItemInputTransformer::fromPayload($payload);

        $this->assertNotNull($input->getPrice(), $expectedDescription);
        $this->assertIsInt($input->getPrice()->value(), 'Price amount should be integer (cents)');
    }

    /**
     * @throws InvalidCurrencyCodeException
     */
    #[DataProvider('comprehensiveValidationProvider')]
    public function testComprehensiveValidation(array $payload, bool $shouldHavePrice, bool $shouldHaveIsActive): void
    {
        $input = PatchPurchasableItemInputTransformer::fromPayload($payload);

        if ($shouldHavePrice) {
            $this->assertNotNull($input->getPrice());
        } else {
            $this->assertNull($input->getPrice());
        }

        if ($shouldHaveIsActive) {
            $this->assertNotNull($input->getIsActive());
        } else {
            $this->assertNull($input->getIsActive());
        }
    }

    public static function validPayloadProvider(): \Generator
    {
        yield 'Complete payload with all fields' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => 'EUR',
                'is_active' => true,
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: Money::create(1000, Currency::fromCode(CurrencyCode::EUR)),
                isActive: true,
            ),
        ];

        yield 'Only price fields' => [
            'payload' => [
                'price_amount' => 2500,
                'price_currency' => 'USD',
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: Money::create(2500, Currency::fromCode(CurrencyCode::USD)),
                isActive: null,
            ),
        ];

        yield 'Only is_active field' => [
            'payload' => [
                'is_active' => false,
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: null,
                isActive: false,
            ),
        ];

        yield 'Empty payload' => [
            'payload' => [],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: null,
                isActive: null,
            ),
        ];

        yield 'Large price amount' => [
            'payload' => [
                'price_amount' => 999999999,
                'price_currency' => 'EUR',
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: Money::create(999999999, Currency::fromCode(CurrencyCode::EUR)),
                isActive: null,
            ),
        ];

        yield 'Zero price amount' => [
            'payload' => [
                'price_amount' => 0,
                'price_currency' => 'USD',
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: Money::create(0, Currency::fromCode(CurrencyCode::USD)),
                isActive: null,
            ),
        ];
    }

    public static function invalidPayloadProvider(): \Generator
    {
        yield 'Invalid currency code' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => 'INVALID',
            ],
            'expectedException' => InvalidCurrencyCodeException::class,
        ];

        yield 'Empty price currency' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => '',
            ],
            'expectedException' => InvalidCurrencyCodeException::class,
        ];

        yield 'Empty price amount' => [
            'payload' => [
                'price_amount' => '',
                'price_currency' => 'EUR',
            ],
            'expectedException' => \TypeError::class,
        ];
    }

    public static function partialUpdateProvider(): \Generator
    {
        yield 'Only price update' => [
            'payload' => [
                'price_amount' => 1500,
                'price_currency' => 'EUR',
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: Money::create(1500, Currency::fromCode(CurrencyCode::EUR)),
                isActive: null,
            ),
        ];

        yield 'Only is_active update to true' => [
            'payload' => [
                'is_active' => true,
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: null,
                isActive: true,
            ),
        ];

        yield 'Only is_active update to false' => [
            'payload' => [
                'is_active' => false,
            ],
            'expectedInput' => new PatchPurchasableItemInputDTO(
                price: null,
                isActive: false,
            ),
        ];
    }

    public static function priceInCentsProvider(): \Generator
    {
        yield 'Price represents 10.00 EUR in cents' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            'expectedDescription' => 'Price amount should represent cents (1000 = 10.00 EUR)',
        ];

        yield 'Price represents 0.01 USD in cents' => [
            'payload' => [
                'price_amount' => 1,
                'price_currency' => 'USD',
            ],
            'expectedDescription' => 'Price amount should represent cents (1 = 0.01 USD)',
        ];

        yield 'Price represents 999.99 EUR in cents' => [
            'payload' => [
                'price_amount' => 99999,
                'price_currency' => 'EUR',
            ],
            'expectedDescription' => 'Price amount should represent cents (99999 = 999.99 EUR)',
        ];
    }

    public static function comprehensiveValidationProvider(): \Generator
    {
        yield 'Both price and is_active provided' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => 'EUR',
                'is_active' => true,
            ],
            'shouldHavePrice' => true,
            'shouldHaveIsActive' => true,
        ];

        yield 'Only price provided' => [
            'payload' => [
                'price_amount' => 2000,
                'price_currency' => 'USD',
            ],
            'shouldHavePrice' => true,
            'shouldHaveIsActive' => false,
        ];

        yield 'Only is_active provided' => [
            'payload' => [
                'is_active' => false,
            ],
            'shouldHavePrice' => false,
            'shouldHaveIsActive' => true,
        ];

        yield 'Neither price nor is_active provided' => [
            'payload' => [],
            'shouldHavePrice' => false,
            'shouldHaveIsActive' => false,
        ];
    }
}
