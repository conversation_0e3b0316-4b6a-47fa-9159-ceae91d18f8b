<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\User\ManagerFilter;

use App\Tests\V2\Domain\User\ManagerFilter\ManagerFilterRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Infrastructure\Persistence\User\ManagerFilter\DBALManagerFilterRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALManagerFilterRepositoryTest extends ManagerFilterRepositoryTestCase
{
    private const string TABLE_NAME = 'manager_filter';
    private Connection $connection;

    /**
     * @throws Exception
     * @throws SchemaException
     */
    protected function getRepository(): ManagerFilterRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALManagerFilterRepository(
            connection: $this->connection,
            managerFilterTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws SchemaException
     * @throws Exception
     */
    private function createTable(): void
    {
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('user_id', 'integer');
        $table->addColumn('filter_id', 'integer');
        $table->setPrimaryKey(['user_id', 'filter_id']);

        $this->connection->createSchemaManager()->createTable($table);
    }
}
