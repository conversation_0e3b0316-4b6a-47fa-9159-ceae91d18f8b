<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\User\UserFilter;

use App\Tests\V2\Domain\User\UserFilter\UserFilterRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use App\V2\Infrastructure\Persistence\User\UserFilter\DBALUserFilterRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALUserFilterRepositoryTest extends UserFilterRepositoryTestCase
{
    private const string TABLE_NAME = 'user_filter';
    private Connection $connection;

    /**
     * @throws Exception
     * @throws SchemaException
     */
    protected function getRepository(): UserFilterRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALUserFilterRepository(
            connection: $this->connection,
            userFilterTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws SchemaException
     * @throws Exception
     */
    private function createTable(): void
    {
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('user_id', 'integer');
        $table->addColumn('filter_id', 'integer');
        $table->setPrimaryKey(['user_id', 'filter_id']);

        $this->connection->createSchemaManager()->createTable($table);
    }
}
