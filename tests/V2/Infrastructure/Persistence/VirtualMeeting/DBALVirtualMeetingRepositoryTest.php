<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\VirtualMeeting;

use App\Tests\V2\Domain\VirtualMeeting\VirtualMeetingRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Infrastructure\Persistence\VirtualMeeting\DBALVirtualMeetingRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALVirtualMeetingRepositoryTest extends VirtualMeetingRepositoryTestCase
{
    private const string TABLE_NAME = 'virtual_meeting';
    private Connection $connection;

    /**
     * @throws SchemaException
     * @throws Exception
     */
    protected function getRepository(): VirtualMeetingRepository
    {
        /**
         * @var Schema $schema
         */
        [$this->connection, $schema] = DBALConnectionFactory::create();

        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string');
        $table->addColumn('type', 'string');
        $table->addColumn('start_at', 'datetime');
        $table->addColumn('finish_at', 'datetime');
        $table->addColumn('url', 'string', ['notnull' => false]);
        $table->addColumn('created_at', 'datetime');
        $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
        $table->addColumn('deleted_at', 'datetime', ['notnull' => false]);

        $table->setPrimaryKey(['id']);

        foreach ($this->connection->getDatabasePlatform()->getCreateTableSQL($table) as $sql) {
            $this->connection->executeStatement($sql);
        }

        return new DBALVirtualMeetingRepository(
            connection: $this->connection,
            virtualMeetingTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    /**
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->dropTable();
        parent::tearDown();
    }
}
