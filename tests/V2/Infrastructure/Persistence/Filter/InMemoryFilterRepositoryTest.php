<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Filter;

use App\Tests\V2\Domain\Filter\FilterRepositoryTestCase;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Infrastructure\Persistence\Filter\InMemoryFilterRepository;

class InMemoryFilterRepositoryTest extends FilterRepositoryTestCase
{
    private ?InMemoryFilterRepository $repository = null;

    protected function getRepository(): FilterRepository
    {
        $this->repository = new InMemoryFilterRepository();

        return $this->repository;
    }

    protected function addFilter(Filter $filter): void
    {
        if (null === $this->repository) {
            $this->fail('call getRepository before calling addFilter()');
        }

        $this->repository->add($filter);
    }
}
