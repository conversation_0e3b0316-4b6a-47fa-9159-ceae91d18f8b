<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Request;

use App\Entity\User;
use App\V2\Infrastructure\Listener\RequestListener;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;

class RequestAttributeExtractorTest extends TestCase
{
    /**
     * @throws RequestAttributeExtractorException
     * @throws Exception
     */
    public function testExtractUserWhenUserExists(): void
    {
        $user = $this->createMock(User::class);
        $request = new Request();
        $request->attributes->set(RequestListener::LOGGED_USER_ATTRIBUTE, $user);

        $extractedUser = RequestAttributeExtractor::extractUser($request);

        $this->assertSame($user, $extractedUser);
    }

    public function testExtractUserUserDoesNotExist(): void
    {
        $request = new Request();

        $this->expectException(RequestAttributeExtractorException::class);
        $this->expectExceptionMessage('User not found');

        RequestAttributeExtractor::extractUser($request);
    }

    public function testExtractUserUserIsNotUserInstance(): void
    {
        $request = new Request();
        $request->attributes->set('user', 'not a user instance');

        $this->expectException(RequestAttributeExtractorException::class);
        $this->expectExceptionMessage('User not found');

        RequestAttributeExtractor::extractUser($request);
    }
}
