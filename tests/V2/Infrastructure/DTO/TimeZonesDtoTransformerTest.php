<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\DTO;

use App\V2\Application\DTO\TimeZonesDto;
use App\V2\Infrastructure\DTO\TimeZonesDtoTransformer;
use PHPUnit\Framework\TestCase;

class TimeZonesDtoTransformerTest extends TestCase
{
    private array $timeZones;

    protected function setUp(): void
    {
        $this->timeZones = ['Europe/Madrid', 'America/New_York', 'Asia/Tokyo'];
    }

    public function testToArrayCorrectFormat(): void
    {
        $defaultTimezone = 'Europe/Madrid';
        $timeZonesDto = new TimeZonesDto($this->timeZones, $defaultTimezone);

        $result = TimeZonesDtoTransformer::toArray($timeZonesDto);

        $this->assertCount(3, $result);
        $this->assertEquals([
            [
                'value' => 'Europe/Madrid',
                'default' => true,
            ],
            [
                'value' => 'America/New_York',
                'default' => false,
            ],
            [
                'value' => 'Asia/Tokyo',
                'default' => false,
            ],
        ], $result);
    }

    public function testToArrayWithDifferentDefaultTimezone(): void
    {
        $defaultTimezone = 'America/New_York';
        $timeZonesDto = new TimeZonesDto($this->timeZones, $defaultTimezone);

        $result = TimeZonesDtoTransformer::toArray($timeZonesDto);

        $this->assertCount(3, $result);
        $this->assertEquals([
            [
                'value' => 'Europe/Madrid',
                'default' => false,
            ],
            [
                'value' => 'America/New_York',
                'default' => true,
            ],
            [
                'value' => 'Asia/Tokyo',
                'default' => false,
            ],
        ], $result);
    }

    public function testToArrayWithEmptyTimezones(): void
    {
        $timezones = [];
        $defaultTimezone = '';
        $timeZonesDto = new TimeZonesDto($timezones, $defaultTimezone);

        $result = TimeZonesDtoTransformer::toArray($timeZonesDto);

        $this->assertCount(0, $result);
        $this->assertEquals([], $result);
    }
}
