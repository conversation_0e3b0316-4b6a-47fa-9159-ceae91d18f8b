<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Filter;

use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Filter\FilterCriteriaTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class FilterCategoryTransformerTest extends TestCase
{
    #[DataProvider('provideFromArrayCases')]
    public function testFromArray(
        array $input,
        ?string $expectedSearch,
        ?Id $expectedCategoryId,
        ?Id $expectedParentId
    ): void {
        $criteria = FilterCriteriaTransformer::fromArray($input);
        $this->assertInstanceOf(FilterCriteria::class, $criteria);
        $this->assertSame($expectedSearch, $criteria->getSearch());
        $this->assertEquals($expectedCategoryId, $criteria->getCategoryId());
        $this->assertEquals($expectedParentId, $criteria->getParentId());
    }

    public static function provideFromArrayCases(): \Generator
    {
        yield 'search, category_id and parent_id' => [
            'input' => [
                'search' => 'foo',
                'category_id' => '1',
                'parent_id' => '1',
            ],
            'expectedSearch' => 'foo',
            'expectedCategoryId' => new Id(1),
            'expectedParentId' => new Id(1),
        ];

        yield 'only search' => [
            'input' => [
                'search' => 'foo',
            ],
            'expectedSearch' => 'foo',
            'expectedCategoryId' => null,
            'expectedParentId' => null,
        ];

        yield 'only category id' => [
            'input' => [
                'category_id' => '1',
            ],
            'expectedSearch' => null,
            'expectedCategoryId' => new Id(1),
            'expectedParentId' => null,
        ];

        yield 'only parent id' => [
            'input' => [
                'parent_id' => '1',
            ],
            'expectedSearch' => null,
            'expectedCategoryId' => null,
            'expectedParentId' => new Id(1),
        ];

        yield 'empty values' => [
            'input' => [],
            'expectedSearch' => null,
            'expectedCategoryId' => null,
            'expectedParentId' => null,
        ];
    }
}
