<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Filter;

use App\Tests\V2\Mother\Filter\FilterMother;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Filter\FilterTransformer;
use PHPUnit\Framework\TestCase;

class FilterTransformerTest extends TestCase
{
    public function testToArray(): void
    {
        $filter = FilterMother::create(id: new Id(1), name: 'Filter 1');

        $this->assertEquals([
            'id' => 1,
            'name' => 'Filter 1',
        ], FilterTransformer::toArray($filter));
    }

    /**
     * @throws CollectionException
     */
    public function testCollectionToArray(): void
    {
        $filter = FilterMother::create(id: new Id(1), name: 'Filter 1');
        $this->assertEquals([
            [
                'id' => 1,
                'name' => 'Filter 1',
            ],
        ], FilterTransformer::fromCollectionToArray(new FilterCollection([$filter])));
    }
}
