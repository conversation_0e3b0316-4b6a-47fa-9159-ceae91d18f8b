<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Shared\Resource;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class ResourceTypeTransformerTest extends TestCase
{
    #[DataProvider('validResourceTypeProvider')]
    public function testFromStringWithValidResourceType(string $resourceType, ResourceType $expected): void
    {
        $result = ResourceTypeTransformer::fromString($resourceType);

        $this->assertSame($expected, $result);
    }

    #[DataProvider('invalidResourceTypeProvider')]
    public function testFromStringWithInvalidResourceType(string $invalidResourceType): void
    {
        $this->expectException(\UnhandledMatchError::class);

        ResourceTypeTransformer::fromString($invalidResourceType);
    }

    #[DataProvider('validResourceTypeProvider')]
    public function testToString(string $expected, ResourceType $resourceType): void
    {
        $result = ResourceTypeTransformer::toString($resourceType);

        $this->assertSame($expected, $result);
    }

    public static function validResourceTypeProvider(): array
    {
        return [
            'course' => [
                'resourceType' => 'course',
                'expected' => ResourceType::Course,
            ],
            'subscription' => [
                'resourceType' => 'subscription',
                'expected' => ResourceType::Subscription,
            ],
        ];
    }

    public static function invalidResourceTypeProvider(): array
    {
        return [
            'user' => [
                'invalidResourceType' => 'user',
            ],
            'lesson' => [
                'invalidResourceType' => 'lesson',
            ],
            'module' => [
                'invalidResourceType' => 'module',
            ],
            'empty string' => [
                'invalidResourceType' => '',
            ],
            'uppercase course' => [
                'invalidResourceType' => 'COURSE',
            ],
            'mixed case course' => [
                'invalidResourceType' => 'Course',
            ],
            'course with spaces' => [
                'invalidResourceType' => ' course ',
            ],
            'invalid format' => [
                'invalidResourceType' => 'invalid_type',
            ],
            'numeric string' => [
                'invalidResourceType' => '123',
            ],
            'special characters' => [
                'invalidResourceType' => 'course@#$',
            ],
        ];
    }

    public function testGetIdentifierMethodExists(): void
    {
        $this->assertTrue(
            method_exists(ResourceTypeTransformer::class, 'getIdentifier'),
            'getIdentifier method should exist in ResourceTypeTransformer class'
        );

        $this->assertTrue(
            \is_callable([ResourceTypeTransformer::class, 'getIdentifier']),
            'getIdentifier method should be callable'
        );
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('validGetIdentifierProvider')]
    public function testGetIdentifierWithValidInputs(
        ResourceType $resourceType,
        string $id,
        string $expectedClass
    ): void {
        $result = ResourceTypeTransformer::getIdentifier($resourceType, $id);

        $this->assertInstanceOf(Identifier::class, $result);
        $this->assertInstanceOf($expectedClass, $result);
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('consistencyTestProvider')]
    public function testGetIdentifierConsistency(ResourceType $resourceType, string $id): void
    {
        $result1 = ResourceTypeTransformer::getIdentifier($resourceType, $id);
        $result2 = ResourceTypeTransformer::getIdentifier($resourceType, $id);

        $this->assertEquals($result1, $result2);
        $this->assertSame($result1->value(), $result2->value());
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('courseIdProvider')]
    public function testGetIdentifierWithCourseResourceType(string $id, int $expectedValue): void
    {
        $result = ResourceTypeTransformer::getIdentifier(ResourceType::Course, $id);

        $this->assertInstanceOf(Id::class, $result);
        $this->assertSame($expectedValue, $result->value());
        $this->assertSame((string) $expectedValue, (string) $result);
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('subscriptionUuidProvider')]
    public function testGetIdentifierWithSubscriptionResourceType(string $uuid): void
    {
        $result = ResourceTypeTransformer::getIdentifier(ResourceType::Subscription, $uuid);

        $this->assertInstanceOf(Uuid::class, $result);
        $this->assertSame($uuid, $result->value());
        $this->assertSame($uuid, (string) $result);
    }

    #[DataProvider('invalidUuidProvider')]
    public function testGetIdentifierWithInvalidUuidThrowsException(string $invalidUuid): void
    {
        $this->expectException(InvalidUuidException::class);
        $this->expectExceptionMessage("Invalid UUID: {$invalidUuid}");

        ResourceTypeTransformer::getIdentifier(ResourceType::Subscription, $invalidUuid);
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('integrationContextProvider')]
    public function testGetIdentifierInIntegrationContext(
        ResourceType $resourceType,
        string $id,
        string $expectedIdentifierClass
    ): void {
        $identifier = ResourceTypeTransformer::getIdentifier($resourceType, $id);

        // Test that the identifier can be used to create a Resource object
        $resource = new \App\V2\Domain\Shared\Resource\Resource($resourceType, $identifier);

        $this->assertSame($resourceType, $resource->getType());
        $this->assertInstanceOf($expectedIdentifierClass, $resource->getId());
        $this->assertTrue($identifier->equals($resource->getId()));
    }

    /**
     * @throws InvalidUuidException
     */
    #[DataProvider('courseIdEdgeCasesProvider')]
    public function testGetIdentifierCourseIdEdgeCases(string $id, int $expectedValue): void
    {
        $result = ResourceTypeTransformer::getIdentifier(ResourceType::Course, $id);

        $this->assertInstanceOf(Id::class, $result);
        $this->assertSame($expectedValue, $result->value());
    }

    /**
     * @throws InvalidUuidException
     */
    public static function validGetIdentifierProvider(): array
    {
        return [
            'course with integer ID' => [
                'resourceType' => ResourceType::Course,
                'id' => '123',
                'expectedClass' => Id::class,
            ],
            'course with string integer ID' => [
                'resourceType' => ResourceType::Course,
                'id' => '456',
                'expectedClass' => Id::class,
            ],
            'subscription with valid UUID' => [
                'resourceType' => ResourceType::Subscription,
                'id' => UuidMother::create()->value(),
                'expectedClass' => Uuid::class,
            ],
        ];
    }

    /**
     * @throws InvalidUuidException
     */
    public static function consistencyTestProvider(): array
    {
        return [
            'course ID consistency' => [
                'resourceType' => ResourceType::Course,
                'id' => '789',
            ],
            'subscription UUID consistency' => [
                'resourceType' => ResourceType::Subscription,
                'id' => UuidMother::create()->value(),
            ],
        ];
    }

    public static function courseIdProvider(): array
    {
        return [
            'positive integer' => [
                'id' => '123',
                'expectedValue' => 123,
            ],
            'single digit' => [
                'id' => '1',
                'expectedValue' => 1,
            ],
            'large integer' => [
                'id' => '999999',
                'expectedValue' => 999999,
            ],
            'zero' => [
                'id' => '0',
                'expectedValue' => 0,
            ],
        ];
    }

    /**
     * @throws InvalidUuidException
     */
    public static function subscriptionUuidProvider(): array
    {
        return [
            'valid UUID v4' => [
                'uuid' => UuidMother::create()->value(),
            ],
            'another valid UUID v4' => [
                'uuid' => UuidMother::create()->value(),
            ],
            'specific valid UUID' => [
                'uuid' => '550e8400-e29b-41d4-a716-************',
            ],
        ];
    }

    public static function invalidUuidProvider(): array
    {
        return [
            'empty string' => [
                'invalidUuid' => '',
            ],
            'invalid format' => [
                'invalidUuid' => 'invalid-uuid',
            ],
            'too short' => [
                'invalidUuid' => '550e8400-e29b-41d4-a716',
            ],
            'too long' => [
                'invalidUuid' => '550e8400-e29b-41d4-a716-************-extra',
            ],
            'invalid characters' => [
                'invalidUuid' => '550e8400-e29b-41d4-a716-44665544000g',
            ],
            'missing hyphens' => [
                'invalidUuid' => '550e8400e29b41d4a716************',
            ],
            'wrong version' => [
                'invalidUuid' => '550e8400-e29b-61d4-a716-************',
            ],
        ];
    }

    /**
     * @throws InvalidUuidException
     */
    public static function integrationContextProvider(): array
    {
        return [
            'course integration' => [
                'resourceType' => ResourceType::Course,
                'id' => '42',
                'expectedIdentifierClass' => Id::class,
            ],
            'subscription integration' => [
                'resourceType' => ResourceType::Subscription,
                'id' => UuidMother::create()->value(),
                'expectedIdentifierClass' => Uuid::class,
            ],
        ];
    }

    public static function courseIdEdgeCasesProvider(): array
    {
        return [
            'minimum positive integer' => [
                'id' => '1',
                'expectedValue' => 1,
            ],
            'zero value' => [
                'id' => '0',
                'expectedValue' => 0,
            ],
            'large integer' => [
                'id' => '**********', // PHP_INT_MAX on 32-bit systems
                'expectedValue' => **********,
            ],
            'string with leading zeros' => [
                'id' => '000123',
                'expectedValue' => 123,
            ],
            'string with whitespace gets cast' => [
                'id' => ' 456 ',
                'expectedValue' => 456,
            ],
        ];
    }
}
