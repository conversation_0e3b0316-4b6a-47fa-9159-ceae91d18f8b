<?php

namespace App\Repository;

use App\Entity\AnnouncementConfigurationType;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseTranslation;
use App\Enum\TypeCourse as EnumTypeCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends ServiceEntityRepository<TypeCourse>
 *
 * @method TypeCourse|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeCourse|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeCourse[]    findAll()
 * @method TypeCourse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeCourseRepository extends ServiceEntityRepository
{
    private TranslatorInterface $translator;

    public function __construct(ManagerRegistry $registry, TranslatorInterface $translator)
    {
        parent::__construct($registry, TypeCourse::class);
        $this->translator = $translator;
    }

    public function add(TypeCourse $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeCourse $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getActiveTranslatedList(?string $locale = null): array
    {
        $data = [];

        foreach ($this->findBy(['active' => true, 'denomination' => EnumTypeCourse::INTERN]) as $typeCourse) {
            $data[] = $this->buildTypeCourseArray($typeCourse, $locale);
        }

        return $data;
    }

    public function getActiveTranslatedListAndConfigurations(?string $locale = null, ?string $denomination = EnumTypeCourse::INTERN): array
    {
        $criteria = ['active' => true];

        if ($denomination !== null) {
            $criteria['denomination'] = $denomination;
        }

        $data = [];

        foreach ($this->findBy($criteria) as $typeCourse) {
            $data[] = $this->buildTypeCourseArray($typeCourse, $locale, true, true);
        }

        return $data;
    }

    public function buildTypeCourseArray(TypeCourse $typeCourse, ?string $locale = null, bool $includeConfigurations = false, bool $includeDenomination = false): array
    {
        $name = null;
        $denomination = $typeCourse->getDenomination();

        if (!empty($locale)) {
            /** @var TypeCourseTranslation $translation */
            $translation = $typeCourse->translate($locale);
            $name = $translation->getName();
        }

        if ($includeDenomination && in_array($denomination, [EnumTypeCourse::INTERN, EnumTypeCourse::EXTERN])) {
            $translatedDenomination = $this->translator->trans(
                'announcements.formativeActionTypes.' . strtolower($denomination),
                [],
                'messages',
                $locale
            );
            $name = ($name ?? $typeCourse->getName()) . ' (' . ($translatedDenomination) . ')';
        } else {
            $name = $name ?? $typeCourse->getName();
        }

        $typeCourseArray = [
            'id' => $typeCourse->getId(),
            'name' => $name,
            'icon' => $typeCourse->getIcon(),
            'type' => $typeCourse->getType()
        ];

        if ($includeConfigurations) {
            $stepsCreations = [];

            foreach ($typeCourse->getTypeCourseAnnouncementStepCreations() as $stepCreation) {
                $announcementConfigurations = [];

                foreach ($stepCreation->getTypeCourseAnnouncementStepConfigurations() as $configuration) {
                    if (
                        null === $configuration->getAnnouncementConfigurationType()
                        || !$configuration->getAnnouncementConfigurationType()->isActive()
                    ) {
                        continue;
                    }

                    $announcementConfigurations[] = [
                        'id' => $configuration->getAnnouncementConfigurationType()->getId(),
                        'name' => $configuration->getAnnouncementConfigurationType()->getName(),
                        'description' => $configuration->getAnnouncementConfigurationType()->getDescription(),
                        'image' => 'assets_announcement/configuration/'
                            . $configuration->getAnnouncementConfigurationType()->getImage(),
                        'type' => $configuration->getAnnouncementConfigurationType()->getType(),
                    ];
                }

                $stepsCreations[] = [
                    'id' => $stepCreation->getId(),
                    'name' => $stepCreation->getName(),
                    'active' => $stepCreation->isActive(),
                    'configurations' => $announcementConfigurations,
                ];
            }

            $typeCourseArray['steps'] = $stepsCreations;
        }

        return $typeCourseArray;
    }

    public function getList(): array
    {
        $typeCourses = $this->findBy(['denomination' => EnumTypeCourse::INTERN, 'active' => true]);
        $list = [];
        foreach ($typeCourses as $typeCourse) {
            $list[$typeCourse->getId()] = $typeCourse->getName();
        }
        asort($list);

        return $list;
    }
}
