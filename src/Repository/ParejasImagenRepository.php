<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ParejasImagen;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ParejasImagen>
 *
 * @method ParejasImagen|null find($id, $lockMode = null, $lockVersion = null)
 * @method ParejasImagen|null findOneBy(array $criteria, array $orderBy = null)
 * @method ParejasImagen[]    findAll()
 * @method ParejasImagen[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ParejasImagenRepository extends ServiceEntityRepository
{
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ParejasImagen::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(ParejasImagen $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(ParejasImagen $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getMemMatchTextsByChapterId(int $chapterId)
    {
        $qb = $this->createQueryBuilder('pi')
            ->select('GROUP_CONCAT(pi.texto SEPARATOR \', \') AS pairTexts')
            ->join('pi.parejas', 'p')
            ->where('p.chapter = :chapterId')
            ->setParameter('chapterId', $chapterId)
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * Find a ParejasImagen entity by ID, including soft-deleted entities.
     * This method temporarily disables the soft delete filter to retrieve
     * entities that may have been soft-deleted.
     */
    public function findWithDeleted(int $id): ?ParejasImagen
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $parejasImagen = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $parejasImagen;
    }
}
