<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Videopreguntas;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Videopreguntas>
 *
 * @method Videopreguntas|null find($id, $lockMode = null, $lockVersion = null)
 * @method Videopreguntas|null findOneBy(array $criteria, array $orderBy = null)
 * @method Videopreguntas[]    findAll()
 * @method Videopreguntas[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class VideopreguntasRepository extends ServiceEntityRepository
{
    private const string SOFT_DELETE_FILTER = 'softdeleteable';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Videopreguntas::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(Videopreguntas $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(Videopreguntas $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * Find a Videopreguntas entity by ID, including soft-deleted entities.
     * This method temporarily disables the soft delete filter to retrieve
     * entities that may have been soft-deleted.
     */
    public function findWithDeleted(int $id): ?Videopreguntas
    {
        $filters = $this->getEntityManager()->getFilters();
        $filters->disable(self::SOFT_DELETE_FILTER);

        try {
            $videopreguntas = $this->findOneBy(criteria: ['id' => $id]);
        } finally {
            $filters->enable(self::SOFT_DELETE_FILTER);
        }

        return $videopreguntas;
    }
}
