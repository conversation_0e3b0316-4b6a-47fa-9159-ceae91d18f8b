<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Course;
use App\Entity\LtiChapter;
use App\Entity\Season;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Chapter|null find($id, $lockMode = null, $lockVersion = null)
 * @method Chapter|null findOneBy(array $criteria, array $orderBy = null)
 * @method Chapter[]    findAll()
 * @method Chapter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChapterRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Chapter::class);
    }

    public function findOneByChapter($id)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getResult();
    }

    public function countByType($conditions): array
    {
        $dateFilter = $this->filterDateString($conditions);
        $sql = 'SELECT ct.id, ct.name, COALESCE(count, 0) AS count
            FROM chapter_type ct
            LEFT JOIN (
                SELECT type_id, COUNT(1) AS count
                FROM chapter
                WHERE deleted_at IS NULL
                ' . ($dateFilter['where'] ?: '') . '
                GROUP BY type_id
            ) chapter_counts ON chapter_counts.type_id = ct.id
            ORDER BY count DESC';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        if (!empty($dateFilter['params'])) {
            foreach ($dateFilter['params'] as $key => $value) {
                $stmt->bindParam($key, $value);
            }
        }

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function countByTypeWithCategory($conditions): array
    {
        $dateFilter = $this->filterDateString($conditions, 'chapter.');
        $sql = 'SELECT ct.id, ct.name, COALESCE(count, 0) AS count
            FROM chapter_type ct
            LEFT JOIN (
                SELECT type_id, COUNT(*) AS count
                FROM chapter
                JOIN course ON chapter.course_id = course.id
                JOIN course_professional_category ON course_professional_category.course_id = course.id 
                	AND course_professional_category.professional_category_id IN (' . implode(',', $conditions['category']) . ')
                WHERE chapter.deleted_at IS NULL 
                ' . ($dateFilter['where'] ?: '') . '
                GROUP BY type_id
            ) chapter_counts ON chapter_counts.type_id = ct.id';

        $conn = $this->getEntityManager()->getConnection();
        $stmt = $conn->prepare($sql);

        if (!empty($dateFilter['params'])) {
            foreach ($dateFilter['params'] as $key => $value) {
                $stmt->bindParam($key, $value);
            }
        }

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    private function filterDateString($conditions, $prefix = ''): array
    {
        $filter = [
            'where' => '',
            'params' => [],
        ];

        if (!empty($conditions['dateFrom'])) {
            $filter['where'] .= " AND {$prefix}created_at >= :dateFrom ";
            $filter['params']['dateFrom'] = $conditions['dateFrom'];
        }

        if (!empty($conditions['dateTo'])) {
            $filter['where'] .= " AND {$prefix}created_at <= '{$conditions['dateTo']}' ";
            $filter['params']['dateTo'] = $conditions['dateTo'];
        }

        return $filter;
    }

    public function countOriginalChapter($conditions)
    {
        $query = $this->createQueryBuilder('ch')
            ->select('count(ch.id)')
            ->andWhere('ch.deletedAt IS NULL');

        if (!empty($conditions['category'])) {
            $query->leftJoin('ch.course', 'c');
            $this->setProfessionalCategoriesQueryFilters($query, $conditions);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('ch.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('ch.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getSingleScalarResult();
    }

    public function getTotalGameUsed(array $conditions = [], $type = null)
    {
        $query = $this->createQueryBuilder('c')
            ->select('ct.name, count(c.id) as count, 0 as url, 0 as hits, 0 as mistake')
            ->innerJoin(
                ChapterType::class,
                'ct',
                Join::WITH,
                'c.type = ct.id'
            )
            ->andWhere('ct.type = :type')
            ->setParameter('type', 'game')
            ->groupBy('ct.name');

        if (!\is_null($type)) {
            $query = $query
                ->leftJoin(
                    UserCourseChapter::class,
                    'ucc',
                    Join::WITH,
                    'ucc.chapter = c.id'
                );
        }

        if (true === $type) {
            $query = $query
                ->andWhere('ucc.data like :like')
                ->setParameter('like', '%correct": true%');
        } elseif (false === $type) {
            $query = $query
                ->andWhere('ucc.data like :like')
                ->setParameter('like', '%correct": false%');
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getGameHitsMistake(array $conditions = [])
    {
        $query = $this->createQueryBuilder('c')
            ->select('ct.name, count(c.id) as count, 0 as url, 0 as hits, 0 as mistake')
            ->leftJoin(
                ChapterType::class,
                'ct',
                Join::WITH,
                'c.type = ct.id'
            )
            ->leftJoin(
                UserCourseChapter::class,
                'ucc',
                Join::WITH,
                'ucc.chapter = c.id'
            )
            ->andWhere('ct.type = :type')
            ->setParameter('type', 'game')
            ->groupBy('ct.name');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getResult();
    }

    public function getTotalGamesByResult(array $conditions = [], $findOnlyGame = true, $type = null)
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id) as count')
            ->innerJoin(
                ChapterType::class,
                'ct',
                Join::WITH,
                'c.type = ct.id'
            );

        if ($findOnlyGame) {
            $query
                ->andWhere('ct.type = :type')
                ->setParameter('type', 'game');
        }

        $query
            ->leftJoin(
                UserCourseChapter::class,
                'ucc',
                Join::WITH,
                'ucc.chapter = c.id'
            )
            ->leftJoin('ucc.userCourse', 'uc');

        if (true === $type) {
            $query
                ->andWhere('ucc.data like :like')
                ->setParameter('like', '%correct": true%');
        } elseif (false === $type) {
            $query
                ->andWhere('ucc.data like :like')
                ->setParameter('like', '%correct": false%');
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setSearchFilters($query, 'uc', $conditions);

        return $query->getQuery()->getResult();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws Exception
     */
    public function getChaptersData(array $filterCoursesByIds = []): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $filterByIdsCondition = '';
        if (!empty($filterCoursesByIds)) {
            $courseIds = implode(',', $filterCoursesByIds);
            $filterByIdsCondition = " AND id IN ($courseIds)";
        }

        $courses = "
			SELECT id, translation_id, name, locale, category_id FROM course
			WHERE active IS NOT NULL AND deleted_at IS NULL{$filterByIdsCondition}";

        $timeByChapters = "
			SELECT
				c.id,
				c.translation_id,
				ch.id as chapter_id,
				SUM( ucc.time_spent ) AS time
			FROM
				(SELECT id, translation_id FROM course WHERE active = 1 AND deleted_at IS NULL) c
				JOIN chapter ch ON c.id = ch.course_id AND deleted_at IS NULL
				LEFT JOIN user_course_chapter ucc ON ch.id = ucc.chapter_id
				JOIN user_course uc ON ucc.user_course_id = uc.id AND uc.finished_at IS NOT NULL
				JOIN (SELECT id FROM `user` WHERE roles NOT LIKE '%ADMIN%' AND roles NOT LIKE '%MANAGER%') u ON uc.user_id = u.id 
			GROUP BY ch.id";

        $usersByCourses = "
			SELECT
				c.id,
				c.translation_id,
				COUNT( DISTINCT uc.user_id ) AS users
			FROM
				(SELECT id, translation_id FROM course WHERE active = 1 AND deleted_at IS NULL) c
				JOIN chapter ch ON c.id = ch.course_id AND deleted_at IS NULL
				LEFT JOIN user_course_chapter ucc ON ch.id = ucc.chapter_id
				JOIN user_course uc ON ucc.user_course_id = uc.id AND uc.finished_at IS NOT NULL
				JOIN (SELECT id FROM `user` WHERE roles NOT LIKE '%ADMIN%' AND roles NOT LIKE '%MANAGER%') u ON uc.user_id = u.id 
			GROUP BY c.id";

        $avgTimeData = "
			SELECT time.chapter_id, IFNULL(time.time_spent, 0) / users.finished AS avg_time
			FROM (
				SELECT
					td.chapter_id, 
				    IFNULL(td.translation_id, td.id) as course_id, 
				    SUM( td.time ) as time_spent
				FROM ( $timeByChapters ) td
				GROUP BY 1
			) as time
			JOIN (
				SELECT
				    IFNULL(td.translation_id, td.id) as course_id, 
				    SUM( td.users ) as finished
				FROM ( $usersByCourses ) td
				GROUP BY 1
			) as users ON users.course_id = time.course_id
		";

        $chapters = "
			SELECT 
				ch.id,
				ch.course_id, 
				ch.title, 
				ch.description as description, 
				ch.position, 
				ch.type_id as type_id,
				IFNULL(timedata.avg_time, 0) as avg_time,
				ch.season_id as season_id
			FROM chapter ch
			LEFT JOIN ( $avgTimeData ) timedata ON timedata.chapter_id = ch.id
			WHERE ch.deleted_at IS NULL
		";

        $query = $conn->prepare("
			SELECT 
				IFNULL(c.translation_id, c.id) as id,
			    ct.position,
				c.name as course_name,
				IFNULL(s.name, '') as season,
				ct.title as content_name,
				REGEXP_REPLACE(LOWER(ct.description), '<.+?>', '') as description,
				UPPER(cpt.`name`) as type,
				cc.`name` as category,
				UPPER(c.locale) as locale,
				ROUND(IFNULL(ct.avg_time, 0), 2) as avg_time
			FROM ($courses) c
			JOIN course_category cc ON cc.id = c.category_id
			JOIN ($chapters) ct ON ct.course_id = c.id
			LEFT JOIN chapter_type cpt ON cpt.id = ct.type_id
			LEFT JOIN season s ON s.id = ct.season_id
			GROUP BY ct.id
			ORDER BY 1, 2");

        return $query->executeQuery([])->fetchAllAssociative();
    }

    public function getSeasonChapters($course, $season)
    {
        return $this->createQueryBuilder('c')
            ->where('c.course = :course')
            ->andWhere('c.season = :season')
            ->setParameters(['course' => $course, 'season' => $season])
            ->orderBy('c.position', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Update max question.
     */
    public function updateMaxQuestion($maxQuestion, $chapterId, bool $doMaxQuestions = false)
    {
        $chapter = $this->find($chapterId);
        if ($chapter) {
            if ($doMaxQuestions) {
                $chapter->setMaxQuestion(null);
            } else {
                $chapter->setMaxQuestion($maxQuestion);
            }
            $this->_em->persist($chapter);
            $this->_em->flush();
        }
    }

    public function getChaptersByCourse(Course $course, array $chaptersIds)
    {
        $query = $this->createQueryBuilder('c')
            ->andWhere('c.course = :course')
            ->setParameter('course', $course);
        $query->andWhere($query->expr()->in('c.id', $chaptersIds));

        return $query->getQuery()->getResult();
    }

    public function createUsersCourseStatisticsNativeQuery(Course $course, $resultMap, array $chaptersIds, array $userIds)
    {
        $QB_courseStatistics = $this->getEntityManager()->createNativeQuery("
            SELECT
                u.id AS userId,
                u.code as userCode,
                u.first_name AS firstName,
                u.last_name AS lastName,
                u.email,
                IF(u.is_active, 'yes', 'no') AS active,
                c.locale AS courseLocale,
                uc.started_at,
                uc.finished_at,
                scoreInfo.timeSpent,
                CASE 
                    WHEN uc.started_at IS NOT NULL 
                    AND uc.finished_at IS NOT NULL THEN
                        'finished' 
                        WHEN uc.started_at IS NOT NULL THEN
                        'in_progess'  ELSE 'pending' 
                END AS `status`,
                IFNULL( scoreInfo.points, 0 ) AS points 
                FROM
                    `user` u
                    LEFT JOIN user_course uc ON uc.user_id = u.id 
                    AND uc.course_id = :courseId
                    LEFT JOIN course c ON c.id = uc.course_id
                    LEFT JOIN (
                    SELECT
                        ucc.user_course_id,
                        SUM(IFNULL( ucc.points, 0 )) AS points,
                        SUM(IFNULL( ucc.time_spent, 0 )) AS timeSpent 
                    FROM
                        chapter ch
                        LEFT JOIN user_course_chapter ucc ON ucc.chapter_id = ch.id 
                    WHERE
                        ch.id IN (:chapterIDs) AND ch.course_id = :courseId
                        AND ch.deleted_at IS NULL 
                    GROUP BY
                        ucc.user_course_id 
                    ) AS scoreInfo ON scoreInfo.user_course_id = uc.id 
                WHERE
                    u.id IN (:USER_IDS)
        ", $resultMap);
        $QB_courseStatistics->setParameters(['chapterIDs' => $chaptersIds, 'USER_IDS' => $userIds, 'courseId' => $course->getId()]);

        return $QB_courseStatistics->getResult();
    }

    /**
     * Check chapters and courses.
     */
    public function getByIdentifier(string $identifier, int $userId, bool $checkUserCourse = false, bool $checkCourseChapter = false)
    {
        $query = $this->createQueryBuilder('c')
            ->innerJoin(
                LtiChapter::class,
                'lc',
                Join::WITH,
                'lc.chapter = c.id'
            )
            ->andWhere('lc.identifier = :identifier')
            ->setParameter('identifier', $identifier);

        if ($checkUserCourse) {
            $query
                ->innerJoin(
                    UserCourse::class,
                    'uc',
                    Join::WITH,
                    'uc.course = c.course'
                )
                ->andWhere('uc.user = :user')
                ->setParameter('user', $userId);
        }

        if ($checkCourseChapter) {
            $query
                ->innerJoin(
                    UserCourseChapter::class,
                    'ucc',
                    Join::WITH,
                    'ucc.chapter = lc.chapter'
                );
        }

        return $query
            ->getQuery()
            ->getResult();
    }

    public function loadCourseChapters(Course $course)
    {
        return $this->createQueryBuilder('c')
            ->select('c.id', 't.name as typeName', 'c.description')
            ->join('c.type', 't')
            ->where('c.course = :course')
            ->setParameter('course', $course)
            ->getQuery()->getResult();
    }

    public function getAnnouncementCourseChapters(Course $course)
    {
        return $this->createQueryBuilder('c')
            ->select('c.id', 'c.title', 'c.createdAt', 'c.image', 'c.description', 'c.position')
            ->addSelect('type.id as typeId', 'type.name as chapterType')
            ->join('c.type', 'type')
            ->where('c.course =:course')->setParameter('course', $course)
            ->orderBy('type.id', 'ASC')
            ->addOrderBy('c.position', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function loadCourseChaptersPaginated(Course $course, $page = 1, $limit = 10)
    {
        $query = $this->createQueryBuilder('c')
            ->select('c')
            ->where('c.course = :course')
            ->andWhere('c.isActive = 1 OR c.isActive IS NULL')
            ->setParameter('course', $course)
            ->setFirstResult(($page - 1) * $limit)
            ->setMaxResults($limit)
            ->orderBy('c.position', 'ASC')
            ->getQuery()->getResult();

        return [
            'chapters' => $query,
            'totalPages' => ceil($this->count(['course' => $course]) / $limit),
            'totalItems' => $this->count(['course' => $course]),
        ];
    }

    public function getChaptersByCourseSeason(Course $course, Season $season, array $chaptersIds)
    {
        $query = $this->createQueryBuilder('c')
            ->andWhere('c.course = :course')
            ->andWhere('c.season = :season')
            ->setParameter('course', $course)
            ->setParameter('season', $season);
        $query->andWhere($query->expr()->in('c.id', $chaptersIds));

        return $query->getQuery()->getResult();
    }

    public function getChaptersBySeason(Season $season, Chapter $chapter)
    {
        $query = $this->createQueryBuilder('c')
            ->andWhere('c.season = :season')
            ->andWhere('c = :chapter')
            ->setParameter('season', $season)
            ->setParameter('chapter', $chapter);

        return $query->getQuery()->getResult();
    }
}
