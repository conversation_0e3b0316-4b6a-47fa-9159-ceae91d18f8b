<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Department;
use App\Entity\Filter;
use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;
use App\Entity\ProfessionalCategory;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Course|null find($id, $lockMode = null, $lockVersion = null)
 * @method Course|null findOneBy(array $criteria, array $orderBy = null)
 * @method Course[]    findAll()
 * @method Course[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CourseRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Course::class);
    }

    /**
     * @return Course[]
     */
    public function findByProfessionalCategory(ProfessionalCategory $category)
    {
        return $this->createQueryBuilder('c')
            ->innerJoin('c.categories', 'pc')
            ->andWhere('pc.id = :professional_category_id')
            ->andWhere('c.active = 1')
            ->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'))
            ->setParameter('professional_category_id', $category->getId())
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Course[]
     */
    public function findByProfessionalCategoryAndCategory(?ProfessionalCategory $professionalCategory = null, CourseCategory $category, bool $showDeactivatedCourses = false, bool $showOpenCampus = true)
    {
        $query = $this->createQueryBuilder('c')
            ->innerJoin('c.categories', 'pc')
            ->innerJoin('c.typeCourse', 'tc');

        if ($professionalCategory) {
            $query->andWhere('pc.id = :professional_category_id');
        }

        $query->andWhere('c.category = :course_category')
            ->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'))
            ->andWhere('tc.id = 1');

        if ($professionalCategory) {
            $query->setParameter('professional_category_id', $professionalCategory->getId());
        }

        $query->setParameter('course_category', $category)
            ->orderBy('c.isNew', 'desc');

        if ($showOpenCampus) {
            $query->andWhere('c.open = true');
        }

        if (!$showDeactivatedCourses) {
            $query->andWhere('c.active = true');
        }

        return $query->getQuery()->getResult();
    }

    /**
     * @return Course[]
     */
    public function findByCategoryAndUser(CourseCategory $category, User $user)
    {
        return $this->createQueryBuilder('c')
            ->innerJoin('c.announcements', 'a')
            ->innerJoin('a.called', 'ac')
            ->innerJoin('c.typeCourse', 'tc')
            ->andWhere('tc.id = 1')
            ->andWhere('ac.user = :user')
            ->andWhere($this->createQueryBuilder('a')->expr()->gt('a.finishAt', 'CURRENT_TIMESTAMP()'))
            ->andWhere('c.category = :course_category')
            ->andWhere('c.active = true')
            ->setParameter('course_category', $category)
            ->setParameter('user', $user)
            ->getQuery()
            ->getResult();
    }

    public function checkAccessByProfesssionalCategory(Course $course, ProfessionalCategory $category)
    {
        return $this->createQueryBuilder('c')
            ->innerJoin('c.categories', 'pc')
            ->andWhere('pc.id = :professional_category_id')
            ->andWhere('c.id = :course_id')
            ->setParameter('professional_category_id', $category->getId())
            ->setParameter('course_id', $course->getId())
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function countByProfessionalCategory(ProfessionalCategory $category)
    {
        return $this->createQueryBuilder('c')
            ->select('count(c.id)')
            ->innerJoin('c.categories', 'pc')
            ->andWhere('pc.id = :professional_category_id')
            ->setParameter('professional_category_id', $category->getId())
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function countOriginalCourses($conditions)
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id)')
            ->andWhere('c.translation IS NULL')
            ->andWhere('c.deletedAt IS NULL');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setProfessionalCategoriesQueryFilters($query, $conditions);

        return $query->getQuery()->getSingleScalarResult();
    }

    public function getOriginalCourses()
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.translation IS NULL')
            ->andWhere('c.deletedAt IS NULL')
            ->addOrderBy('c.code', 'ASC')
            ->addOrderBy('c.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Course[]
     */
    public function findOtherProfessionalCategoryAndCategory(CourseCategory $category, array $coursesToSend = [], bool $showDeactivatedCourses = false)
    {
        $query = $this->createQueryBuilder('c');
        $query
            ->andWhere('c.category = :course_category')
            ->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'))
            ->andWhere('c.open = true')
            ->setParameter('course_category', $category)
            ->addOrderBy('c.isNew', 'desc')
            ->addOrderBy('c.createdAt', 'asc');

        if (!empty($coursesToSend)) {
            $courseIds = [];
            foreach ($coursesToSend as $courseToSend) {
                $courseIds[] = $courseToSend['id'];
            }
            $query->andWhere($query->expr()->notIn('c.id', $courseIds));
        }

        if (!$showDeactivatedCourses) {
            $query->andWhere('c.active = true');
        }

        return $query->getQuery()->getResult();
    }

    /**
     * @return Course[]
     */
    public function courseSectionOpenCampus(CourseCategory $category, $showDeactivatedCourses = false)
    {
        $query = $this->createQueryBuilder('c');
        $query
            ->andWhere('c.category = :course_category')
            ->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'))
            ->andWhere('c.open_visible = true');

        if (!$showDeactivatedCourses) {
            $query->andWhere('c.active = true');
        }

        $query->setParameter('course_category', $category);

        if (!\is_null($category)) {
            $this->getOrderPropertiesCourseCategory($query, $category);
        }

        return $query->getQuery()->getResult();
    }

    private function getOrderPropertiesCourseCategory($query, CourseCategory $category)
    {
        switch ($category->getOrderType()) {
            case CourseCategory::ORDER_TYPE_MANUAL:
                return $this->applyManualOrderCourseCategory($query, $category);
            case CourseCategory::ORDER_TYPE_AUTO:
                return $this->applyAutoOrderCourseCategory($query, $category);

            default:
                return $query->addOrderBy('c.isNew', 'desc');
        }
    }

    private function applyManualOrderCourseCategory($query, CourseCategory $category)
    {
        if (CourseCategory::ORDER_TYPE_MANUAL == $category->getOrderType()) {
            return $query->addOrderBy('c.sort', 'ASC');
        }

        return $query;
    }

    private function applyAutoOrderCourseCategory($query, CourseCategory $category)
    {
        $orderProperties = $category->getOrderProperties();

        if (\is_null($orderProperties)) {
            return $query;
        }

        if (true == $orderProperties['showNewAtStart']) {
            $query->addOrderBy('c.isNew', 'desc');
        }

        switch ($orderProperties['orderCriteria']) {
            case 'createdAt':
                $query->addOrderBy('c.createdAt', 'asc');
                break;
            case 'alphabetic':
                $query->addOrderBy('c.name', 'asc');
                break;
        }

        return $query;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getCategoriesByOpenCampusThanUserHasAccess(User $user): array
    {
        $isOpenCampus = $user->getOpen();

        $categories = $this->_em->createQueryBuilder()
            ->select('c')
            ->from(CourseCategory::class, 'c')
            ->getQuery()
            ->getResult();

        $categoriesForUser = [];

        if ($isOpenCampus) {
            foreach ($categories as $category) {
                $courseOpenCampus = $this->searchCourseCategoryLimitOne(category: $category);

                if (!$courseOpenCampus) {
                    continue;
                }

                $categoriesForUser[] = [
                    'idCategory' => $courseOpenCampus['idcategory'],
                ];
            }
        }

        return array_filter($categoriesForUser);
    }

    /**
     * @throws NonUniqueResultException
     */
    private function searchCourseCategoryLimitOne(CourseCategory $category)
    {
        $query = $this->createQueryBuilder('c');
        $query->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'));

        $query
            ->select('c.id as idCourse, c.name as namecourse', 'c.open_visible', 'cc.id as idcategory', 'cc.name as namecategory')
            ->leftJoin('c.category', 'cc')
            ->andWhere('c.category = :course_category')
            ->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'))
            ->andWhere('c.active = true')
            ->andWhere('c.open_visible = true')
            ->setParameter('course_category', $category)
            ->addOrderBy('c.isNew', 'desc')
            ->addOrderBy('c.createdAt', 'asc');

        $query->setMaxResults(1);

        return $query->getQuery()->getOneOrNullResult();
    }

    /**
     * @param Filter[] $filters
     */
    public function searchCourseByFilter($filters)
    {
        $query = $this->createQueryBuilder('c');

        $query->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'));

        $query->select('cc.id AS idCategory, c.id as idCourse, c.name as nameCourse')
            ->leftJoin('c.category', 'cc')
            ->leftJoin('c.translation', 'ct')
            ->join('c.typeCourse', 'tc')
            ->innerJoin('c.filters', 'f')
            ->where('c.open = true')
            ->andWhere($query->expr()->isNull('ct.id'))
            ->andWhere('tc.id = 1')
            ->andWhere('c.active = true');

        if (!empty($filters)) {
            $filterIds = [];
            foreach ($filters as $filter) {
                $filterIds[] = $filter->getId();
            }
            $query->andWhere($query->expr()->in('f.id', $filterIds));
        }

        return $query->getQuery()->getResult();
    }

    /**
     * @param Filter[] $filters
     *
     * @return Course[]
     */
    public function findByFilters($filters, ?CourseCategory $category = null, $showDeactivatedCourses = false)
    {
        $query = $this->createQueryBuilder('c');
        $query->andWhere('c.open = true');

        $query->andWhere($this->createQueryBuilder('c')->expr()->isNull('c.translation'));

        $query->join('c.typeCourse', 'tc')
            ->andWhere('tc.id = 1');
        $query->innerJoin('c.filters', 'f');

        $filterIds = [];
        foreach ($filters as $filter) {
            $filterIds[] = $filter->getId();
        }
        if (!empty($filterIds)) {
            $query->andWhere($query->expr()->in('f.id', $filterIds));
        }

        if (!\is_null($category)) {
            $query->andWhere('c.category = :category')
                ->setParameter('category', $category);
        }

        if (!$showDeactivatedCourses) {
            $query->andWhere('c.active = true');
        }

        if (!\is_null($category)) {
            $this->getOrderPropertiesCourseCategory($query, $category);
        }

        return $query->getQuery()->getResult();
    }

    public function getCoursesManager($user)
    {
        $query = $this->createQueryBuilder('c');
        $query->where(':user MEMBER OF c.managers');
        $query->setParameter('user', $user);

        return $query->getQuery()->getResult();
    }

    public function applyUserRoleFilters(QueryBuilder $query, User $user, $coursesManager, array $sharedAsCreatorCourseIds = []): QueryBuilder
    {
        $roles = $user->getRoles();

        if ($user->isAdmin()) {
            return $query;
        }

        if ($user->isCreator() && $user->isManager() && \count($coursesManager) <= 0) {
            return $query;
        }

        $conditions = [];

        if ($user->isCreator()) {
            $creatorConditions = [];
            $creatorConditions[] = 'c.createdBy = :user';

            if (!empty($sharedAsCreatorCourseIds)) {
                $creatorConditions[] = 'c.id IN (:creator_course_ids)';
                $query->setParameter('creator_course_ids', $sharedAsCreatorCourseIds);
            }

            if (!empty($creatorConditions)) {
                $conditions[] = '(' . implode(' OR ', $creatorConditions) . ')';
            }
        }

        if ($user->isManager()) {
            if ($coursesManager && \count($coursesManager) > 0) {
                $conditions[] = ':user MEMBER OF c.managers';
                $query->setParameter('user', $user);
            }
        }

        if (!empty($conditions)) {
            $query->andWhere('(' . implode(' OR ', $conditions) . ')')
                ->setParameter('user', $user);
        }

        return $query;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function canManagerAccessCourse(Course $course, User $user): bool
    {
        return null !== $this->createQueryBuilder('c')
                ->where(':user MEMBER OF c.managers')
                ->andWhere('c.id = :courseId')
                ->setParameter('user', $user)
                ->setParameter('courseId', $course->getId())
                ->getQuery()
                ->getOneOrNullResult();
    }

    public function checkItineraryAccess(Course $course, User $user): bool
    {
        /** @var ItineraryRepository $itineraryRepository */
        $itineraryRepository = $this->_em->getRepository(Itinerary::class);
        $byUserAndCourse = $itineraryRepository->findByUserAndCourse($user, $course);
        if (\count($byUserAndCourse) > 0) {
            return true;
        } // Already in an itinerary where the user was added manually

        // If is here, check by filters
        $filters = [];
        /** @var Filter $filter */
        foreach ($user->getFilter() as $filter) {
            // $filters[$filter->getFilterCategory()->getId()] = $filter->getId();
            $filters[] = $filter->getId();
        }
        if (empty($filters)) {
            return false;
        }

        /*   $itineraryQuery = $itineraryRepository->createQueryBuilder('i');

        $subQuery = $this->_em->createQueryBuilder()->select('distinct(i2.id)')->from(Itinerary::class, 'i2');
        foreach ($filters as $categoryID => $filter) {
            $subQuery->join('i2.filters', "f$categoryID")->orWhere($subQuery->expr()->in("f${categoryID}.id", $filter));
        }



        $itineraryQuery->join('i.itineraryCourses', 'ic')
            ->andWhere($itineraryQuery->expr()->in('i.id', $subQuery->getDQL()))
            ->andWhere('ic.course =:course')
            ->setParameter('course', $course);

        $itineraryQuery->select('distinct(ic.id)');
        $result = $itineraryQuery->getQuery()->getResult(); */

        $result = $this->checkItinerarySqlTemporal($filters, $course->getId());

        return \count($result) > 0;
    }

    private function checkItinerarySqlTemporal($filters, $idCourse)
    {
        $filters = implode(',', $filters);
        $sql = "SELECT DISTINCT (i.id) from itinerary i WHERE i.id in (SELECT DISTINCT (if2.itinerary_id)
         from itinerary_filter if2 where if2.filter_id in ($filters)) and (i.id in (SELECT DISTINCT (ic.itinerary_id)
         from itinerary_course ic where ic.course_id = :course or ic.course_id 
         in (select translation_id from course c where id = :course)))";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);

        return $stmt->executeQuery(['course' => $idCourse])
            ->fetchAllAssociative();
    }

    public function departmentMoreActive()
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id) as count, d.code')
            ->innerJoin(
                Department::class,
                'd',
                Join::WITH,
                'd.code = c.code'
            )
            ->andWhere('c.deletedAt is null')
            ->groupBy('c.id');

        $query->addOrderBy('d.code', 'DESC');

        return $query
            ->setMaxResults(10)
            ->getQuery()
            ->getArrayResult();
    }

    public function top10courseBySchool($conditions)
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id) as count, cc.name')
            ->leftJoin('c.category', 'cc')
            ->groupBy('cc.name')
            ->addOrderBy('count', 'DESC');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query
            ->setMaxResults(10)
            ->getQuery()
            ->getArrayResult();
    }

    public function schoolFinishedAndProgress($ccName = '', $conditions = [])
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id) as count, cc.name, 0 as finished, 0 as inCourse')
            ->innerJoin(
                CourseCategory::class,
                'cc',
                Join::WITH,
                'cc.id = c.category'
            )
            ->innerJoin(
                UserCourse::class,
                'uc',
                Join::WITH,
                'uc.course = c.id'
            )
            ->andWhere('c.deletedAt is null')
            ->groupBy('cc.name');

        if ('' !== $ccName) {
            $query = $query
                ->andWhere('uc.finishedAt is null')
                ->andWhere('cc.name = :name')
                ->setParameter('name', $ccName);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setSearchFilters($query, 'uc', $conditions);

        //        $this->setProfessionalCategoriesQueryFilters($query, $conditions);

        $query->addOrderBy('cc.name', 'DESC');

        return $query
            ->setMaxResults(10)
            ->getQuery()
            ->getArrayResult();
    }

    public function getstatOpenCourse($conditions)
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id)')
            ->innerJoin(
                ItineraryCourse::class,
                'i',
                Join::WITH,
                'i.course = c.id'
            )
            ->innerJoin(
                Announcement::class,
                'a',
                Join::WITH,
                'a.course = c.id'
            );

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('c.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setProfessionalCategoriesQueryFilters($query, $conditions);

        return $query->getQuery()->getSingleScalarResult();
    }

    public function countOpenCourses()
    {
        $query = $this->createQueryBuilder('c')
            ->select('count(c.id)')
            ->andWhere('c.open = true');

        try {
            return $query->getQuery()->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            return 0;
        }
    }

    /**
     * @throws Exception
     */
    public function getCoursesData(array $filterByIds = []): array
    {
        $conn = $this->_em->getConnection();

        $filterByIdsCondition = '';
        if (!empty($filterByIds)) {
            $courseIds = implode(',', $filterByIds);
            $filterByIdsCondition = " AND c.id IN ($courseIds)";
        }

        $courses = "
            SELECT c.id, c.name, c.code, c.active, c.open, c.open_visible, c.is_new, c.locale, c.category_id, c.created_by_id, c.created_at
            FROM course c
            WHERE c.translation_id IS NULL AND c.deleted_at IS NULL{$filterByIdsCondition}";

        $chapters = '
			SELECT course_id, COUNT(1) as chapters, COUNT(DISTINCT season_id) as seasons 
			FROM chapter 
			WHERE deleted_at is NULL 
			GROUP BY course_id';

        $npsAVG = '
			SELECT uc.course_id, AVG(n.value) / 2 as average 
			FROM nps n 
			JOIN user_course uc ON uc.id = n.course_id
			JOIN nps_question nq ON nq.id = n.question_id
			WHERE nq.main = 1 AND nq.type = "nps" AND n.deleted_at IS NULL
			GROUP BY uc.course_id';

        $timeAVG = '
			SELECT
			IFNULL(td.translation_id, td.id) as id, 
			SUM( td.time ) as time_spent,
			SUM( td.users ) as total_users
			FROM (
				SELECT
					c.id,
					c.translation_id,
					COUNT(DISTINCT IF(uc.finished_at IS NOT NULL, uc.user_id, NULL) ) AS users,
					SUM(IF(uc.finished_at IS NOT NULL, ucc.time_spent, 0) ) AS time
				FROM
					(SELECT id, translation_id FROM course WHERE deleted_at IS NULL) c
					JOIN chapter ch ON c.id = ch.course_id AND ch.deleted_at IS NULL
					LEFT JOIN user_course_chapter ucc ON ch.id = ucc.chapter_id
					JOIN user_course uc ON ucc.user_course_id = uc.id
					JOIN user u ON uc.user_id = u.id AND u.deleted_at IS NULL
				GROUP BY c.id
			) td
			GROUP BY 1';

        $locales = '
			SELECT 
			    translation_id, 
			    GROUP_CONCAT(DISTINCT locale) as locale 
			FROM course
			WHERE translation_id IS NOT NULL AND deleted_by_id IS NULL AND active is not null 
			GROUP BY translation_id
		';

        $usersCounter = '
			SELECT IFNULL(c.translation_id, c.id) as course_id, COUNT(uc.id) as started, COUNT(IF(uc.finished_at IS NULL, NULL, u.id)) as finished
			FROM user_course uc
			JOIN course c ON c.id = uc.course_id AND c.deleted_at IS NULL
			JOIN user u ON u.id = uc.user_id AND u.deleted_at IS NULL AND uc.deleted_at IS NULL
			GROUP BY 1
		';

        $query = $conn->prepare("
			SELECT  
				c.id, 
				c.name, 
				c.code, 
				cc.`name` as category,
				u.email as created_by,
                c.created_at as created_at,
				ct.chapters as no_chapters,
				IFNULL(ct.seasons, 0) as no_seasons,
				IF(c.active = 1, 'YES', 'NO') as active,
				IF(c.open = 1, 'YES', 'NO') as open,
				IF(c.open_visible = 1, 'YES', 'NO') as open_campus,
                IF(c.is_new = 1, 'YES', 'NO') as is_new,
				IF(cl.locale IS NULL, c.locale, CONCAT(c.locale, ',', cl.locale)) as langs,
				ROUND(IFNULL(nps.average, 0), 2) as avg_rating,
				ROUND(IFNULL(time.time_spent / time.total_users, 0), 2) as avg_time,
				ROUND(IFNULL(time.time_spent, 0), 2) as time_spent,
				IFNULL(users.started, 0) as started,
				IFNULL(users.finished, 0) as finished
			
			FROM ($courses) c
			JOIN course_category cc ON cc.id = c.category_id
			JOIN ($chapters) ct ON ct.course_id = c.id
			LEFT JOIN user u ON u.id = c.created_by_id
			LEFT JOIN ($npsAVG) nps ON nps.course_id = c.id
			LEFT JOIN ($timeAVG) time ON time.id = c.id
			LEFT JOIN ($locales) cl ON cl.translation_id = c.id
			LEFT JOIN ($usersCounter) users ON users.course_id = c.id
			ORDER BY 1");

        return $query->executeQuery([])->fetchAllAssociative();
    }

    public function findCoursesByFilters($filter, $typeCourse, $user, $page = 1, $pageSize = 10)
    {
        $courseQuery = $this->createQueryBuilder('c')
            ->leftJoin('c.chapters', 'chap')
            ->leftJoin('chap.type', 'chapter_type')
            ->andWhere('chapter_type.active = 1');

        if (!empty($filter)) {
            $courseQuery->andWhere('c.name LIKE :filter OR c.code LIKE :filter')
                ->setParameter('filter', "%$filter%");
        }

        if (!empty($typeCourse)) {
            if (1 == $typeCourse) {
                $courseQuery->andWhere('c.typeCourse IS NULL')
                    ->orWhere('c.typeCourse = :typeCourse');
            } else {
                $courseQuery->leftJoin('c.typeCourse', 'typeCourse')
                    ->orWhere('typeCourse = :typeCourse');
                $courseQuery->Where('c.typeCourse = :typeCourse');
            }
            $courseQuery->setParameter('typeCourse', $typeCourse);
        }

        if (!\in_array('ROLE_ADMIN', $user->getRoles())) {
            $courseQuery->andWhere('c.createdBy = :user OR :user MEMBER OF c.managers')
                ->setParameter('user', $user);
        }

        return $courseQuery->select('c')
            ->getQuery()
            ->getResult();
    }

    public function findAnnouncementByUser(User $user)
    {
        return $this->createQueryBuilder('c')
            ->select('a')
            ->from('App\Entity\Announcement', 'a')
            ->innerJoin('c.announcements', 'a')
            ->innerJoin('a.called', 'ac')
            ->andWhere('ac.user = :user')
            ->andWhere('c.active = true')
            ->setParameter('user', $user)
            ->orderBy('a.id', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function setCourseFiltersFromIdsArray(Course $course, array $ids = [])
    {
        $filters = $this->_em->getRepository(Filter::class)
            ->createQueryBuilder('f')
            ->where('f.id IN (:ids)')
            ->setParameter('ids', $ids)
            ->getQuery()
            ->execute();
        $course->setFilters($filters);
    }

    public function setCourseManagersFromArray(Course $course, array $managers = [])
    {
        $ids = [];
        foreach ($managers as $m) {
            $ids[] = $m['id'];
        }
        $managers = $this->_em->getRepository(User::class)->createQueryBuilder('u')
            ->where('u.id IN (:ids)')
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getResult();

        $course->setManagers($managers);
    }

    public function setCourseProfessionalCategoriesFromArray(Course $course, array $managers = [])
    {
        $ids = [];
        foreach ($managers as $m) {
            $ids[] = $m['id'];
        }
        $categories = $this->_em->getRepository(ProfessionalCategory::class)->createQueryBuilder('pc')
            ->where('pc.id IN (:ids)')
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getResult();

        $course->setCategories($categories);
    }

    public function getCoursesToOrder($category)
    {
        return $this->createQueryBuilder('c')
            ->select('c.id', 'c.name', 'c.sort', 'c.image')
            ->addSelect('COUNT(cc.id) as totalChapters')
            ->addSelect('tc.id as typeCourseId')
            ->leftJoin('c.chapters', 'cc')
            ->join('c.typeCourse', 'tc')
            ->where('c.category =:category')
            ->setParameter('category', $category)
            ->addGroupBy('c.id')
            ->addOrderBy('c.sort', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getCourseInfo()
    {
        return $this->createQueryBuilder('c')
            ->select('c.id', "CONCAT(c.code, '-', c.name) as name")
            ->groupBy('c.id')
            ->getQuery()
            ->getResult();
    }

    public function getCourseInfoForIds($courseIds)
    {
        $courseQuery = $this->createQueryBuilder('c');

        return $courseQuery->select('c')
            ->andWhere($courseQuery->expr()->in('c.id', $courseIds))
            ->orderBy('c.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findCoursesDiplomaReport(array $conditions)
    {
        $courseId = 'null' != $conditions['courseId'] ? $conditions['courseId'] : null;
        $query = $this->createQueryBuilder('c')->where('c.typeCourse = 1');

        if (null != $courseId) {
            $query->andWhere('c.id = :courseId')
                ->setParameter('courseId', $courseId);
        }

        return $query->getQuery()->getResult();
    }

    public function findCourseStatsByFilters(User $user, array $conditions = [], int $page = 1, $sortBy = 'c.name', $sortDir = 'ASC', array $sharedAsCreatorCourseIds = []): array
    {
        $pageSize = 10;
        $coursesManager = $this->getCoursesManager($user);

        $courseQuery = $this->createQueryBuilder('c')
            ->select('c');

        if (!empty($conditions)) {
            $courseQuery = $this->setQueryConditions($courseQuery, $conditions);
        }

        if (empty($conditions['locale']) && empty($conditions['search'])) {
            $courseQuery->andWhere('c.translation IS NULL');
        }

        $courseQuery->andWhere('c.deletedAt IS NULL');

        $courseQuery->setFirstResult(($page - 1) * $pageSize)
            ->setMaxResults(10);

        if ('c.totalChapters' == $sortBy) {
            $courseQuery->leftJoin('c.chapters', 'ch')->where('ch.isActive = 1 or ch.isActive IS NULL')
                ->addSelect('COUNT(ch.id) as HIDDEN chapterCount')
                ->groupBy('c.id')
                ->orderBy('chapterCount', $sortDir);
        } elseif ('c.category' == $sortBy) {
            $userLocale = $user->getLocale();
            $courseQuery
                ->leftJoin('c.category', 'cat')
                ->leftJoin('cat.translations', 'trans', 'WITH', 'trans.locale = :locale')
                ->setParameter('locale', $userLocale)
                ->addSelect('COALESCE(trans.name, cat.name) AS HIDDEN categoryOrderName')
                ->orderBy('categoryOrderName', $sortDir);
        } else {
            $courseQuery->orderBy($sortBy, $sortDir);
        }

        $courseQuery = $this->applyUserRoleFilters($courseQuery, $user, $coursesManager, $sharedAsCreatorCourseIds);

        return $courseQuery->getQuery()
            ->getResult();
    }

    public function findTotalCourseStatsByFilters(array $conditions = [], User $user, array $sharedAsCreatorCourseIds = []): array
    {
        $coursesManager = $this->getCoursesManager($user);
        $courseQuery = $this->createQueryBuilder('c')
            ->select('c');

        if (!empty($conditions)) {
            $courseQuery = $this->setQueryConditions($courseQuery, $conditions);
        }

        if (empty($conditions['locale']) && empty($conditions['search'])) {
            $courseQuery->andWhere('c.translation IS NULL');
        }

        $courseQuery->andWhere('c.deletedAt IS NULL');

        $courseQuery = $this->applyUserRoleFilters($courseQuery, $user, $coursesManager, $sharedAsCreatorCourseIds);

        return $courseQuery->getQuery()
            ->getResult();
    }

    public function setQueryConditions(QueryBuilder $query, array $conditions = []): QueryBuilder
    {
        $validationsByJoins = ['category', 'typeCourse', 'createdBy'];
        $tableJoin = ['category' => 'cc', 'typeCourse' => 'tc', 'createdBy' => 'u'];
        $fieldsJoin = ['category' => 'id', 'typeCourse' => 'id', 'createdBy' => 'id'];

        foreach ($validationsByJoins as $value) {
            if (!empty($conditions[$value])) {
                $query->innerJoin('c.' . $value, $tableJoin[$value])
                    ->andWhere($tableJoin[$value] . '.' . $fieldsJoin[$value] . ' = :' . $value)
                    ->setParameter(':' . $value, $conditions[$value]);
            }
        }

        $equalKeys = ['open', 'open_visible', 'isNew', 'active'];
        foreach ($equalKeys as $value) {
            if (!empty($conditions[$value])) {
                $query->andWhere('c.' . $value . ' = :' . $value)
                    ->setParameter($value, $conditions[$value]);
            }
        }

        if (!empty($conditions['createdAt'])) {
            $query->andWhere('c.createdAt >= :createdAt')->setParameter('createdAt', $conditions['createdAt']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('c.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }
        if (!empty($conditions['locale'])) {
            $locales = explode(',', $conditions['locale']);
            $query->andWhere('c.locale IN (:locale)')->setParameter('locale', $locales);
        }

        if (!empty($conditions['search'])) {
            $searchTerm = $conditions['search'];

            if (is_numeric($searchTerm)) {
                $query->andWhere('c.id = :id')
                    ->setParameter('id', $searchTerm);
            } else {
                $query->andWhere(
                    $query->expr()->orX(
                        $query->expr()->like('c.name', ':name'),
                        $query->expr()->like('c.code', ':code')
                    )
                )
                    ->setParameter('name', '%' . $searchTerm . '%')
                    ->setParameter('code', '%' . $searchTerm . '%');
            }
        }

        return $query;
    }

    public function findAllCreatorsCourses(): array|float|int|string
    {
        return $this->createQueryBuilder('c')
            ->select('IDENTITY(c.createdBy) as creatorId')
            ->leftJoin('App\Entity\User', 'u', 'WITH', 'u.id = c.createdBy')
            ->where('u.id IS NOT NULL')
            ->groupBy('c.createdBy')
            ->orderBy('creatorId', 'ASC')
            ->getQuery()
            ->getSingleColumnResult();
    }

    public function findCoursesByPagination(array $paramsToPaginated, User $user, &$typeDenomination): array
    {
        $query = $paramsToPaginated['query'];
        $typeCourse = $paramsToPaginated['typeCourse'];
        $page = $paramsToPaginated['page'];
        $pageSize = $paramsToPaginated['pageSize'];
        $coursesStatus = $paramsToPaginated['coursesStatus'];
        $coursesManager = $this->getCoursesManager($user);

        $courseQB = $this->createQueryBuilder('c');

        if (!empty($query)) {
            $courseQB->andWhere('c.name LIKE :query OR c.code LIKE :query')
                ->setParameter('query', "%$query%");
        }

        if ('null' != $coursesStatus) {
            if ('0' == $coursesStatus) {
                $courseQB->andWhere('c.active = 0');
            }
            if ('1' == $coursesStatus) {
                $courseQB->andWhere('c.active = 1');
            }
        }

        if (empty($typeCourse)) {
            $courseQB->andWhere('c.typeCourse = 1');
        } else {
            $type = $this->_em->getRepository(TypeCourse::class)->find($typeCourse);
            $courseQB->andWhere('c.typeCourse =:type_course')->setParameter('type_course', $type);
            $typeDenomination = $type ? $type->getDenomination() : null;
        }

        if (\in_array('ROLE_MANAGER', $user->getRoles()) && \count($coursesManager) > 0) {
            $courseQB->andWhere('c.createdBy = :user OR :user MEMBER OF c.managers')
                ->setParameter('user', $user);
        }

        $totalItems = (clone $courseQB)->select('count(c.id)')->getQuery()->getSingleScalarResult();
        $courses = $courseQB->select('c')
            ->setMaxResults($pageSize)
            ->setFirstResult($pageSize * ($page - 1))
            ->getQuery()
            ->getResult();

        return ['courses' => $courses, 'totalItems' => $totalItems];
    }

    public function fetchChapterByPagination(array $params, Course $course): array
    {
        $page = $params['page'];
        $pageSize = $params['pageSize'];

        $chapterQB = $this->createQueryBuilder('ch');

        $totalItems = (clone $chapterQB)->select('count(ch.id)')->getQuery()->getSingleScalarResult();
        $chapters = $chapterQB->select('ch')
            ->andWhere('ch.course = :course')
            ->setParameter('course', $course)
            ->setMaxResults($pageSize)
            ->setFirstResult($pageSize * ($page - 1))
            ->getQuery()
            ->getResult();

        return ['chapters' => $chapters, 'totalItems' => $totalItems];
    }
}
