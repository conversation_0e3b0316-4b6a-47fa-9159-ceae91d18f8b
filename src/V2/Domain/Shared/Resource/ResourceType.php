<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Resource;

use App\Entity\Course;
use App\V2\Domain\Subscription\Subscription;

enum ResourceType
{
    case Course;
    case Subscription;

    public static function fromEntity(mixed $entity): self
    {
        return match (true) {
            $entity instanceof Course => self::Course,
            $entity instanceof Subscription => self::Subscription,
            default => throw new \InvalidArgumentException('Unsupported entity type'),
        };
    }

    public static function entityClass(self $resourceType): string
    {
        return match ($resourceType) {
            self::Course => Course::class,
            self::Subscription => Subscription::class,
        };
    }
}
