<?php

declare(strict_types=1);

namespace App\V2\Domain\User\ManagerFilter;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\ManagerFilterNotFoundException;
use App\V2\Domain\User\Exception\ManagerFilterRepositoryException;

interface ManagerFilterRepository
{
    /**
     * @throws ManagerFilterRepositoryException
     * @throws InfrastructureException
     */
    public function insert(ManagerFilter $managerFilter): void;

    /**
     * @throws ManagerFilterNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(ManagerFilterCriteria $criteria): ManagerFilter;

    /**
     * @throws InfrastructureException
     */
    public function findBy(ManagerFilterCriteria $criteria): ManagerFilterCollection;

    /**
     * @throws InfrastructureException
     */
    public function delete(ManagerFilter $managerFilter): void;
}
