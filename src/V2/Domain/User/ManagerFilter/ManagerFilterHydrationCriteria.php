<?php

declare(strict_types=1);

namespace App\V2\Domain\User\ManagerFilter;

use App\V2\Domain\Shared\Hydrator\HydrationCriteria;

class ManagerFilterHydrationCriteria extends HydrationCriteria
{
    private bool $withFilters = false;

    public function isEmpty(): bool
    {
        return false === $this->withFilters;
    }

    public function withFilters(): self
    {
        $this->withFilters = true;

        return $this;
    }

    public function needsFilters(): bool
    {
        return $this->withFilters;
    }
}
