<?php

declare(strict_types=1);

namespace App\V2\Domain\User\Exception;

class DeleteUserFiltersCommandHandlerException extends \Exception
{
    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function filterDoesNotExist(): self
    {
        return new self('At least one of the filters does not exist');
    }

    public static function cannotRemoveUnmanagedFilter(): self
    {
        return new self('At least one of the filters is not managed by the user');
    }

    public static function userFilterNotAssignedToUser(): self
    {
        return new self('At least one of the filters is not assigned to a user');
    }
}
