<?php

declare(strict_types=1);

namespace App\V2\Domain\User\Exception;

class PostManagerFiltersCommandHandlerException extends \Exception
{
    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function filterDoesNotExist(): self
    {
        return new self('At least one of the filters does not exist');
    }

    public static function userHasFilterAssigned(): self
    {
        return new self('At least one of the filters is already assigned to a user');
    }
}
