<?php

declare(strict_types=1);

namespace App\V2\Domain\User\Exception;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;

class ManagerFilterRepositoryException extends InfrastructureException
{
    public static function duplicateFilter(Id $userId, Id $filterId): self
    {
        return new self(
            \sprintf(
                'Manager filter with id: %d and user id: %d already exists',
                $filterId->value(),
                $userId->value(),
            )
        );
    }
}
