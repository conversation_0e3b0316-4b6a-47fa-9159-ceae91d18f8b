<?php

declare(strict_types=1);

namespace App\V2\Domain\Filter;

use App\V2\Domain\Shared\Criteria\CriteriaWithId;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;

/**
 * @extends CriteriaWithId<FilterCriteria>
 */
class FilterCriteria extends CriteriaWithId
{
    private ?Id $categoryId = null;
    private ?IdCollection $categoriesIds = null;
    private ?Id $parentId = null;
    private ?string $search = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->categoryId
            && null === $this->categoriesIds
            && null === $this->parentId
            && null === $this->search;
    }

    public function filterByCategoryId(Id $id): self
    {
        $this->categoryId = $id;

        return $this;
    }

    public function filterByCategoriesIds(IdCollection $ids): self
    {
        $this->categoriesIds = $ids;

        return $this;
    }

    public function filterByParentId(Id $id): self
    {
        $this->parentId = $id;

        return $this;
    }

    public function filterBySearch(string $query): self
    {
        $this->search = $query;

        return $this;
    }

    public function getCategoryId(): ?Id
    {
        return $this->categoryId;
    }

    public function getCategoriesIds(): ?IdCollection
    {
        return $this->categoriesIds;
    }

    public function getParentId(): ?Id
    {
        return $this->parentId;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }
}
