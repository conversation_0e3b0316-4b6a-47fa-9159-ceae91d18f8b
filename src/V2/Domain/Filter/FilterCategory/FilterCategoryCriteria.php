<?php

declare(strict_types=1);

namespace App\V2\Domain\Filter\FilterCategory;

use App\V2\Domain\Shared\Criteria\CriteriaWithId;
use App\V2\Domain\Shared\Id\Id;

class FilterCategoryCriteria extends CriteriaWithId
{
    private ?string $name = null;
    private ?Id $parentId = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty() && null === $this->name;
    }

    public function filterByName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function filterByParentId(Id $id): self
    {
        $this->parentId = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getParentId(): ?Id
    {
        return $this->parentId;
    }
}
