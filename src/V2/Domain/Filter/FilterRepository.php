<?php

declare(strict_types=1);

namespace App\V2\Domain\Filter;

use App\V2\Domain\Filter\Exception\FilterNotFoundException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface FilterRepository
{
    /**
     * @throws FilterNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(FilterCriteria $criteria): Filter;

    /**
     * @throws CollectionException
     * @throws InfrastructureException
     */
    public function findBy(FilterCriteria $criteria): FilterCollection;
}
