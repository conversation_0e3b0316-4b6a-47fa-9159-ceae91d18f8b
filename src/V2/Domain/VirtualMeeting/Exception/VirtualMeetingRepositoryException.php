<?php

declare(strict_types=1);

namespace App\V2\Domain\VirtualMeeting\Exception;

use App\V2\Domain\Shared\Exception\InfrastructureException;

class VirtualMeetingRepositoryException extends InfrastructureException
{
    public static function invalidVirtualMeetingType(string $type): self
    {
        return new self(\sprintf(
            'Invalid virtual meeting type "%s".',
            $type
        ));
    }
}
