<?php

declare(strict_types=1);

namespace App\V2\Domain\VirtualMeeting;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;

/**
 * @extends CriteriaWithUuid<VirtualMeetingCriteria>
 */
class VirtualMeetingCriteria extends CriteriaWithUuid
{
    private ?VirtualMeetingType $type = null;
    private ?\DateTimeImmutable $startAtFrom = null;
    private ?\DateTimeImmutable $startAtTo = null;
    private ?\DateTimeImmutable $finishAtFrom = null;
    private ?\DateTimeImmutable $finishAtTo = null;

    protected function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->type
            && null === $this->startAtFrom
            && null === $this->startAtTo
            && null === $this->finishAtFrom
            && null === $this->finishAtTo;
    }

    public function filterByType(VirtualMeetingType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getType(): ?VirtualMeetingType
    {
        return $this->type;
    }

    public function filterByStartAtFrom(\DateTimeImmutable $startAtFrom): self
    {
        $this->startAtFrom = $startAtFrom;

        return $this;
    }

    public function getStartAtFrom(): ?\DateTimeImmutable
    {
        return $this->startAtFrom;
    }

    public function filterByStartAtTo(\DateTimeImmutable $startAtTo): self
    {
        $this->startAtTo = $startAtTo;

        return $this;
    }

    public function getStartAtTo(): ?\DateTimeImmutable
    {
        return $this->startAtTo;
    }

    public function filterByFinishAtFrom(\DateTimeImmutable $finishAtFrom): self
    {
        $this->finishAtFrom = $finishAtFrom;

        return $this;
    }

    public function getFinishAtFrom(): ?\DateTimeImmutable
    {
        return $this->finishAtFrom;
    }

    public function filterByFinishAtTo(\DateTimeImmutable $finishAtTo): self
    {
        $this->finishAtTo = $finishAtTo;

        return $this;
    }

    public function getFinishAtTo(): ?\DateTimeImmutable
    {
        return $this->finishAtTo;
    }
}
