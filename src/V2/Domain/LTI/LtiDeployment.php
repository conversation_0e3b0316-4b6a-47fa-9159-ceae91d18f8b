<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends EntityWithId<Uuid>
 */
class LtiDeployment extends EntityWithId
{
    public function __construct(
        Uuid $id,
        private readonly Uuid $registrationId,
        private readonly string $name,
        private readonly string $deploymentId,// Unique in registration context
    ) {
        parent::__construct($id);
    }

    public function getRegistrationId(): Uuid
    {
        return $this->registrationId;
    }

    public function getDeploymentId(): string
    {
        return $this->deploymentId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function __toString(): string
    {
        return json_encode([
            'id' => $this->id->value(),
            'registration_id' => $this->registrationId->value(),
            'name' => $this->name,
            'deployment_id' => $this->deploymentId,
        ]);
    }
}
