<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends EntityWithId<Uuid>
 */
class LtiRegistration extends EntityWithId
{
    private ?LtiPlatform $platform = null;
    private ?LtiTool $tool = null;
    private ?LtiDeploymentCollection $deployments = null;

    public function __construct(
        Uuid $id,// LTI: identifier
        private readonly string $name,
        private readonly string $clientId,
    ) {
        parent::__construct($id);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getClientId(): string
    {
        return $this->clientId;
    }

    public function getPlatform(): ?LtiPlatform
    {
        return $this->platform;
    }

    public function setPlatform(LtiPlatform $platform): self
    {
        $this->platform = $platform;

        return $this;
    }

    public function getTool(): ?LtiTool
    {
        return $this->tool;
    }

    public function setTool(LtiTool $tool): self
    {
        $this->tool = $tool;

        return $this;
    }

    public function getDeployments(): ?LtiDeploymentCollection
    {
        return $this->deployments;
    }

    public function setDeployments(LtiDeploymentCollection $deployments): self
    {
        $this->deployments = $deployments;

        return $this;
    }

    public function __toString(): string
    {
        return json_encode([
            'id' => $this->id->value(),
            'name' => $this->name,
            'clientId' => $this->clientId,
        ]);
    }
}
