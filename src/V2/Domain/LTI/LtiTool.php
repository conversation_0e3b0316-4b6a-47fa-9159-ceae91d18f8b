<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends EntityWithId<Uuid>
 */
class LtiTool extends EntityWithId
{
    public function __construct(
        Uuid $id,// LTI: identifier
        private readonly Uuid $registrationId,
        private readonly string $name,
        private readonly string $audience,
        private readonly Url $oidcInitiationUrl,
        private readonly Url $launchUrl,
        private readonly Url $deepLinkingUrl,
        private readonly Url $jwksUrl,
    ) {
        parent::__construct($id);
    }

    public function getRegistrationId(): Uuid
    {
        return $this->registrationId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getAudience(): string
    {
        return $this->audience;
    }

    public function getOidcInitiationUrl(): Url
    {
        return $this->oidcInitiationUrl;
    }

    public function getLaunchUrl(): Url
    {
        return $this->launchUrl;
    }

    public function getDeepLinkingUrl(): Url
    {
        return $this->deepLinkingUrl;
    }

    public function getJwksUrl(): Url
    {
        return $this->jwksUrl;
    }

    public function __toString(): string
    {
        return json_encode([
            'id' => $this->id->value(),
            'registration_id' => $this->registrationId->value(),
            'name' => $this->name,
            'audience' => $this->audience,
            'oidc_initiation_url' => $this->oidcInitiationUrl->value(),
            'launch_url' => $this->launchUrl->value(),
            'deep_linking_url' => $this->deepLinkingUrl->value(),
            'jwks_url' => $this->jwksUrl->value(),
        ]);
    }
}
