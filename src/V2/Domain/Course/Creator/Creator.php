<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Creator;

use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Id\Id;

/**
 * @extends EntityWithId<Id>
 */
class Creator extends EntityWithId
{
    public function __construct(
        Id $id,
        private readonly Email $email,
        private readonly string $firstName,
        private readonly string $lastName,
    ) {
        parent::__construct($id);
    }

    public function getEmail(): Email
    {
        return $this->email;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }
}
