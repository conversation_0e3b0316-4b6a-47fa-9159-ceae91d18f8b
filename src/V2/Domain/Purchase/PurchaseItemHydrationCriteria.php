<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Hydrator\HydrationCriteria;

class PurchaseItemHydrationCriteria extends HydrationCriteria
{
    private bool $withPurchasableItem = false;

    public function isEmpty(): bool
    {
        return false === $this->withPurchasableItem;
    }

    public function withPurchasableItem(): self
    {
        $this->withPurchasableItem = true;

        return $this;
    }

    public function needsPurchasableItem(): bool
    {
        return $this->withPurchasableItem;
    }
}
