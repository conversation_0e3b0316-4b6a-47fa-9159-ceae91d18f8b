<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase\Exception;

class PostCreatePurchaseException extends \Exception
{
    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function noPurchasableItemsFound(): self
    {
        return new self('No purchasable items found or some items are inactive');
    }
}
