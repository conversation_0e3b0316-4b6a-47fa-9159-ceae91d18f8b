<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\PaginatedCollection;

class PaginatedPurchaseCollection extends PaginatedCollection
{
    public function __construct(
        private readonly PurchaseCollection $collection,
        int $totalItems
    ) {
        parent::__construct($totalItems);
    }

    public function getCollection(): Collection
    {
        return $this->collection;
    }
}
