<?php

declare(strict_types=1);

namespace App\V2\Domain\Subscription;

use App\V2\Domain\Shared\Entity\LifeCycleEntity;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends LifeCycleEntity<Uuid>
 */
class Subscription extends LifeCycleEntity
{
    public function __construct(
        Uuid $id,
        private readonly string $name,
        private readonly string $description,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ) {
        parent::__construct(
            id: $id,
            createdAt: $createdAt,
            updatedAt: $updatedAt,
            deletedAt: $deletedAt
        );
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }
}
