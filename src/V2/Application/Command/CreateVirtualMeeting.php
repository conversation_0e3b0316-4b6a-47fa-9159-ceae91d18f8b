<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\Bus\Command;

readonly class CreateVirtualMeeting implements Command
{
    public function __construct(
        private VirtualMeetingDTO $virtualMeetingDto,
    ) {
    }

    public function getVirtualMeetingDto(): VirtualMeetingDTO
    {
        return $this->virtualMeetingDto;
    }
}
