<?php

declare(strict_types=1);

namespace App\V2\Application\Purchase;

use App\V2\Application\DTO\Purchase\PatchPurchasableItemInputDTO;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;

class PurchasableItemAssembler
{
    /**
     * @throws InvalidPurchasableItemException
     */
    public static function createUpdatedPurchasableItem(
        PurchasableItem $purchasableItem,
        PatchPurchasableItemInputDTO $input,
    ): PurchasableItem {
        return new PurchasableItem(
            id: $purchasableItem->getId(),
            name: $purchasableItem->getName(),
            description: $purchasableItem->getDescription(),
            price: $input->getPrice() ?? $purchasableItem->getPrice(),
            resource: $purchasableItem->getResource(),
            createdAt: $purchasableItem->getCreatedAt(),
            updatedAt: $purchasableItem->getUpdatedAt(),
            deletedAt: $purchasableItem->getDeletedAt(),
            isActive: $input->getIsActive() ?? $purchasableItem->getIsActive(),
        );
    }
}
