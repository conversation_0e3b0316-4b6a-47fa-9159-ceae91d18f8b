<?php

declare(strict_types=1);

namespace App\V2\Application\Purchase;

use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Financial\TaxRate;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class PurchaseFactory
{
    public function __construct(
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws CollectionException
     */
    public function createFromPurchasableItems(
        Id $userId,
        PurchasableItemCollection $purchasableItems,
        Currency $currency,
        TaxRate $taxRate,
    ): Purchase {
        $totalAmount = 0;
        foreach ($purchasableItems->all() as $purchasableItem) {
            if (!$purchasableItem->getPrice()->currency()->equals($currency)) {
                throw new \InvalidArgumentException('Currency mismatch');
            }

            $totalAmount += $purchasableItem->getPrice()->value();
        }

        $taxAmount = (int) round($totalAmount * $taxRate->value());

        $purchase = new Purchase(
            id: $this->uuidGenerator->generate(),
            userId: $userId,
            status: PurchaseStatus::Pending,
            amount: Money::create(
                amount: $totalAmount,
                currency: $currency
            ),
            taxRate: $taxRate,
            taxAmount: Money::create(
                amount: $taxAmount,
                currency: $currency
            ),
            createdAt: new \DateTimeImmutable(),
        );

        $items = new PurchaseItemCollection([]);
        foreach ($purchasableItems->all() as $purchasableItem) {
            $items->append(
                $this->createPurchaseItemFromPurchasableItem(
                    purchaseId: $purchase->getId(),
                    purchasableItem: $purchasableItem,
                )
            );
        }

        $purchase->setPurchaseItems($items);

        return $purchase;
    }

    public function createPurchaseItemFromPurchasableItem(
        Uuid $purchaseId,
        PurchasableItem $purchasableItem,
    ): PurchaseItem {
        return new PurchaseItem(
            id: $this->uuidGenerator->generate(),
            purchaseId: $purchaseId,
            purchasableItemId: $purchasableItem->getId(),
            price: $purchasableItem->getPrice(),
        );
    }
}
