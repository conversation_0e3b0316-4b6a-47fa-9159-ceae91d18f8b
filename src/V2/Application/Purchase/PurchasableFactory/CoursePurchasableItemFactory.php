<?php

declare(strict_types=1);

namespace App\V2\Application\Purchase\PurchasableFactory;

use App\Entity\Course;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemFactory;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class CoursePurchasableItemFactory implements PurchasableItemFactory
{
    public function __construct(private UuidGenerator $uuidGenerator)
    {
    }

    public function supports(ResourceType $type): bool
    {
        return ResourceType::Course === $type;
    }

    /**
     * @throws InvalidPurchasableItemException
     */
    public function create(mixed $source, Money $price): PurchasableItem
    {
        if (!$source instanceof Course) {
            throw new \InvalidArgumentException('Expected Course');
        }

        return new PurchasableItem(
            id: $this->uuidGenerator->generate(),
            name: $source->getName(),
            description: $source->getDescription() ?? '',
            price: $price,
            resource: new Resource(
                type: ResourceType::Course,
                id: new Id($source->getId())
            ),
            createdAt: new \DateTimeImmutable(),
            isActive: false,
        );
    }
}
