<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Purchase;

use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseItemHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;

class PurchaseItemHydratorCollection extends HydratorCollection
{
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof PurchaseItemCollection || !$criteria instanceof PurchaseItemHydrationCriteria) {
            throw new HydratorException();
        }

        parent::hydrate($collection, $criteria);
    }
}
