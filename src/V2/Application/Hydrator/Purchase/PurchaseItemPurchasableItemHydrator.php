<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Purchase;

use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseItemHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Uuid\UuidCollection;

class PurchaseItemPurchasableItemHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly PurchasableItemRepository $purchasableItemRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof PurchaseItemHydrationCriteria && $criteria->needsPurchasableItem();
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws PurchasableItemRepositoryException
     */
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof PurchaseItemCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $ids = $collection->reduce(
            callback: function (array $carry, PurchaseItem $purchaseItem) {
                $carry[] = $purchaseItem->getPurchasableItemId();

                return $carry;
            },
            initial: []
        );

        $ids = array_unique($ids);

        $purchasableItems = $this->purchasableItemRepository->findBy(
            PurchasableItemCriteria::createEmpty()->filterByIds(new UuidCollection($ids))
        );

        if ($purchasableItems->isEmpty()) {
            return;
        }

        $purchasableItemsById = $purchasableItems->allIndexedById();

        foreach ($collection->all() as $purchaseItem) {
            $purchaseItem->setPurchasableItem($purchasableItemsById[$purchaseItem->getPurchasableItemId()->value()]);
        }
    }
}
