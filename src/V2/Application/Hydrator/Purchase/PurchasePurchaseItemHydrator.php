<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Purchase;

use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\Purchase\Exception\PurchaseHydratorException;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseItemCriteria;
use App\V2\Domain\Purchase\PurchaseItemHydrationCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Uuid\UuidCollection;

class PurchasePurchaseItemHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly PurchaseRepository $purchaseRepository,
        private readonly PurchaseItemHydratorCollection $purchaseItemHydratorCollection,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof PurchaseHydrationCriteria && $criteria->needsPurchaseItem();
    }

    /**
     * @throws PurchaseHydratorException
     * @throws InfrastructureException
     */
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof PurchaseCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $ids = $collection->reduce(
            callback: function (array $carry, Purchase $purchase) {
                $carry[] = $purchase->getId();

                return $carry;
            },
            initial: []
        );

        $ids = array_unique($ids);

        $purchaseItems = $this->purchaseRepository->findPurchaseItemsBy(
            PurchaseItemCriteria::createEmpty()
                ->filterByPurchaseIds(new UuidCollection($ids))
        );

        if ($purchaseItems->isEmpty()) {
            return;
        }

        foreach ($collection->all() as $purchase) {
            $items = $purchaseItems->filter(
                fn (PurchaseItem $purchaseItem) => $purchaseItem->getPurchaseId()->equals($purchase->getId())
            );

            if ($items->isEmpty()) {
                continue;
            }

            $purchase->setPurchaseItems($items);
        }

        /* @var PurchaseHydrationCriteria $criteria */
        if ($criteria->needsPurchasableItem()) {
            $purchaseItemHydrationCriteria = new PurchaseItemHydrationCriteria();
            $purchaseItemHydrationCriteria->withPurchasableItem();

            try {
                $this->purchaseItemHydratorCollection->hydrate($purchaseItems, $purchaseItemHydrationCriteria);
            } catch (HydratorException $e) {
                throw PurchaseHydratorException::fromPrevious($e);
            }
        }
    }
}
