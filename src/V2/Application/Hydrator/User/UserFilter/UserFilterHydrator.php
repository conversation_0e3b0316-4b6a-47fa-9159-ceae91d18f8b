<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\User\UserFilter;

use App\Entity\Filter as LegacyFilter;
use App\Repository\FilterRepository as LegacyFilterRepository;
use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Filter;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterHydrationCriteria;
use Doctrine\DBAL\ArrayParameterType;

class UserFilterHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly LegacyFilterRepository $legacyFilterRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof UserFilterHydrationCriteria && $criteria->needsFilters();
    }

    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof UserFilterCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $filterIds = $collection->reduce(
            callback: function (array $carry, UserFilter $userFilter) {
                $carry[] = $userFilter->getFilterId();

                return $carry;
            },
            initial: []
        );

        $filterIds = array_unique($filterIds);

        $legacyFilters = $this->legacyFilterRepository->createQueryBuilder('f')
            ->andWhere('f.id IN (:filterIds)')
            ->setParameter('filterIds', $filterIds, ArrayParameterType::STRING)
            ->getQuery()
            ->getResult()
        ;

        $legacyFiltersIndexedById = [];
        foreach ($legacyFilters as $legacyFilter) {
            $legacyFiltersIndexedById[$legacyFilter->getId()] = $legacyFilter;
        }

        foreach ($collection->all() as $userFilter) {
            $legacyFilter = $legacyFiltersIndexedById[$userFilter->getFilterId()->value()] ?? null;
            if (!$legacyFilter) {
                continue;
            }

            $userFilter->setFilter(
                $this->getFilterProjection($legacyFilter)
            );
        }
    }

    private function getFilterProjection(LegacyFilter $filter): Filter
    {
        return new Filter(
            id: new Id($filter->getId()),
            name: $filter->getName(),
            categoryId: new Id($filter->getFilterCategory()->getId()),
        );
    }
}
