<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\User\UserFilter;

use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterHydrationCriteria;

class UserFilterHydratorCollection extends HydratorCollection
{
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (
            !$collection instanceof UserFilterCollection
            || !$criteria instanceof UserFilterHydrationCriteria
        ) {
            throw new HydratorException();
        }

        parent::hydrate($collection, $criteria);
    }
}
