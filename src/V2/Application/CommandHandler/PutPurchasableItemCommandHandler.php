<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\PutPurchasableItemCommand;
use App\V2\Application\Purchase\PurchasableItemAdapter;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\Shared\Resource\ResourceProvider;

readonly class PutPurchasableItemCommandHandler
{
    public function __construct(
        private PurchasableItemRepository $purchasableItemRepository,
        private PurchasableItemAdapter $purchasableItemAdapter,
        private ResourceProvider $resourceProvider,
    ) {
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws PurchasableItemRepositoryException
     * @throws ResourceNotFoundException
     */
    public function handle(PutPurchasableItemCommand $command): void
    {
        $entity = $this->resourceProvider->getEntity(
            $command->getResource()
        );

        $purchasableItem = $this->purchasableItemAdapter->from(
            $entity,
            $command->getPrice()
        );

        $this->purchasableItemRepository->put($purchasableItem);
    }
}
