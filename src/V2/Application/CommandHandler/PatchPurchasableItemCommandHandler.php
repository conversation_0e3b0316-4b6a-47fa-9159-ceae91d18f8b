<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\PatchPurchasableItemCommand;
use App\V2\Application\Purchase\PurchasableItemAssembler;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;

readonly class PatchPurchasableItemCommandHandler
{
    public function __construct(
        private PurchasableItemRepository $purchasableItemRepository,
    ) {
    }

    /**
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws CriteriaException
     */
    public function handle(PatchPurchasableItemCommand $command): void
    {
        $purchasableItem = $this->purchasableItemRepository->findOneBy(
            PurchasableItemCriteria::createById($command->getPurchasableItemId())
        );

        $updatedPurchasableItem = PurchasableItemAssembler::createUpdatedPurchasableItem(
            $purchasableItem,
            $command->getInput(),
        );

        $this->purchasableItemRepository->put($updatedPurchasableItem);
    }
}
