<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\DeletePurchasableItemCommand;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;

readonly class DeletePurchasableItemCommandHandler
{
    public function __construct(
        private PurchasableItemRepository $purchasableItemRepository,
    ) {
    }

    /**
     * @throws CriteriaException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function handle(DeletePurchasableItemCommand $command): void
    {
        $purchasableItem = $this->purchasableItemRepository->findOneBy(
            PurchasableItemCriteria::createById($command->getPurchasableItemId())
        );

        $this->purchasableItemRepository->delete($purchasableItem);
    }
}
