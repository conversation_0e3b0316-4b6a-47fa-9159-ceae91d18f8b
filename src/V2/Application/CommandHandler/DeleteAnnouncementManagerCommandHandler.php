<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Entity\Announcement;
use App\Entity\User;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Service\SettingsService;
use App\V2\Application\Command\DeleteAnnouncementManagerCommand;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\DeleteAnnouncementManagerException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class DeleteAnnouncementManagerCommandHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private LegacyAnnouncementRepository $legacyAnnouncementRepository,
        private AnnouncementManagerRepository $announcementManagerRepository,
        private SettingsService $settingsService,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws AnnouncementNotFoundException
     * @throws ManagerNotAuthorizedException
     * @throws AnnouncementManagerNotFoundException
     * @throws DeleteAnnouncementManagerException
     */
    public function handle(DeleteAnnouncementManagerCommand $command): void
    {
        // Check if the feature is enabled
        if (!$this->settingsService->get('app.announcement.managers.sharing')) {
            throw ManagerNotAuthorizedException::announcementManagerSharingIsDisabled();
        }

        $announcement = $this->legacyAnnouncementRepository->findOneBy(['id' => $command->getAnnouncementId()->value()]);
        if (null === $announcement) {
            throw new AnnouncementNotFoundException();
        }

        $this->checkPermissions(user: $command->getRequestUser(), announcement: $announcement);

        try {
            $user = $this->userRepository->findOneBy(
                UserCriteria::createById($command->getUserId())
            );
        } catch (UserNotFoundException $e) {
            throw DeleteAnnouncementManagerException::fromPrevious($e);
        }

        $announcementManager = $this->announcementManagerRepository->findOneBy(
            AnnouncementManagerCriteria::createEmpty()
                 ->filterByUserId($user->getId())
                 ->filterByAnnouncementId($announcement->getId())
        );

        $this->announcementManagerRepository->delete($announcementManager);
    }

    /**
     * @throws ManagerNotAuthorizedException
     */
    private function checkPermissions(User $user, Announcement $announcement): void
    {
        if (
            !$user->isAdmin()
            && $user->isManager()
            && ($announcement->getCreatedBy() && $announcement->getCreatedBy()->getId() !== $user->getId())
        ) {
            throw ManagerNotAuthorizedException::userNotAuthorized(announcement: $announcement, user: $user->__toString());
        }
    }
}
