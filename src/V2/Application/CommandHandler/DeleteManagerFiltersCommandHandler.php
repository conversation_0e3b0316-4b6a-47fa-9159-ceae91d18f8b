<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Service\SettingsService;
use App\V2\Application\Command\DeleteManagerFiltersCommand;
use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\DeleteManagerFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class DeleteManagerFiltersCommandHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private FilterRepository $filterRepository,
        private ManagerFilterRepository $managerFilterRepository,
        private SettingsService $settingsService,
    ) {
    }

    /**
     * @throws UserForbiddenAction
     * @throws InfrastructureException
     * @throws DeleteManagerFiltersCommandHandlerException
     * @throws CriteriaException
     * @throws CollectionException
     * @throws UserNotFoundException
     */
    public function handle(DeleteManagerFiltersCommand $command): void
    {
        if (!$this->settingsService->get('app.user.useFilters')) {
            throw UserForbiddenAction::filtersNotEnabled();
        }

        $this->userRepository->findOneBy(
            UserCriteria::createById($command->getUserId()),
        );

        $filtersIds = array_unique($command->getFilterIds()->all());
        $filters = $this->filterRepository->findBy(
            FilterCriteria::createByIds(new IdCollection($filtersIds)),
        );
        if ($filters->count() !== \count($filtersIds)) {
            throw DeleteManagerFiltersCommandHandlerException::filterDoesNotExist();
        }

        $assignedFilters = $this->managerFilterRepository->findBy(
            ManagerFilterCriteria::createEmpty()
                ->filterByFilterIds(new IdCollection($filtersIds))
                ->filterByUserId($command->getUserId()),
        );

        if ($assignedFilters->isEmpty() || $assignedFilters->count() !== \count($filtersIds)) {
            throw DeleteManagerFiltersCommandHandlerException::userFilterNotAssignedToUser();
        }

        foreach ($assignedFilters->all() as $managerFilter) {
            $this->managerFilterRepository->delete($managerFilter);
        }
    }
}
