<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\PostCreatePurchaseCommand;
use App\V2\Application\CommandHandler\Exception\PostCreatePurchaseException;
use App\V2\Application\Purchase\PurchaseFactory;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;

readonly class PostCreatePurchaseCommandHandler
{
    public function __construct(
        private PurchaseRepository $purchaseRepository,
        private PurchasableItemRepository $purchasableItemRepository,
        private PurchaseFactory $purchaseFactory,
    ) {
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws CriteriaException
     * @throws PostCreatePurchaseException
     */
    public function handle(PostCreatePurchaseCommand $command): Purchase
    {
        $purchasableItems = $this->purchasableItemRepository->findBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByIds($command->getPurchasableItemIds())
                ->filterByIsActive(true)
        );

        if (
            $purchasableItems->count() !== $command->getPurchasableItemIds()->count()
        ) {
            throw PostCreatePurchaseException::noPurchasableItemsFound();
        }

        $purchase = $this->purchaseFactory->createFromPurchasableItems(
            userId: $command->getUserId(),
            purchasableItems: $purchasableItems,
            currency: $command->getCurrency(),
            taxRate: $command->getTaxRate(),
        );

        $this->purchaseRepository->put($purchase);

        return $purchase;
    }
}
