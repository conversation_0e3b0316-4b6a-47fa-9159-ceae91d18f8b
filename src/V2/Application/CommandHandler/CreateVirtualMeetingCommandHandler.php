<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\CreateVirtualMeeting;
use App\V2\Application\VirtualMeeting\VirtualMeetingFactory;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;

readonly class CreateVirtualMeetingCommandHandler
{
    public function __construct(
        private VirtualMeetingRepository $virtualMeetingRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws InvalidVirtualMeetingException
     * @throws InfrastructureException
     */
    public function handle(CreateVirtualMeeting $command): Uuid
    {
        $virtualMeeting = VirtualMeetingFactory::createFromDTO(
            id: $this->uuidGenerator->generate(),
            dto: $command->getVirtualMeetingDto(),
        );

        $this->virtualMeetingRepository->put($virtualMeeting);

        return $virtualMeeting->getId();
    }
}
