<?php

declare(strict_types=1);

namespace App\V2\Application\Query\Admin;

use App\V2\Domain\Bus\Query;
use App\V2\Domain\Shared\Id\Id;

readonly class GetUserFiltersQuery implements Query
{
    public function __construct(
        private Id $userId,
        private bool $withFilters = false,
    ) {
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function needsFilters(): bool
    {
        return $this->withFilters;
    }
}
