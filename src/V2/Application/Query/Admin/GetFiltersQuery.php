<?php

declare(strict_types=1);

namespace App\V2\Application\Query\Admin;

use App\Entity\User;
use App\V2\Domain\Bus\Query;
use App\V2\Domain\Filter\FilterCriteria;

readonly class GetFiltersQuery implements Query
{
    public function __construct(
        private FilterCriteria $criteria,
        private User $requestedBy,
    ) {
    }

    public function getCriteria(): FilterCriteria
    {
        return $this->criteria;
    }

    public function getRequestedBy(): User
    {
        return $this->requestedBy;
    }
}
