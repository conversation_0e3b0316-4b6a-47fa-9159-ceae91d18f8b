<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\V2\Application\Hydrator\Purchase\PurchaseHydratorCollection;
use App\V2\Application\Query\GetPurchase;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;

readonly class GetPurchaseQueryHandler
{
    public function __construct(
        private PurchaseRepository $purchaseRepository,
        private PurchaseHydratorCollection $purchaseHydratorCollection,
    ) {
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws PurchaseNotFoundException
     * @throws PurchaseRepositoryException
     * @throws InfrastructureException
     * @throws HydratorException
     */
    public function handle(GetPurchase $query): Purchase
    {
        $criteria = PurchaseCriteria::createById($query->getPurchaseId());

        if ($query->getPurchaseOwner()) {
            $criteria->filterByUserId(new Id($query->getPurchaseOwner()->getId()));
        }

        $purchase = $this->purchaseRepository->findOneBy($criteria);

        if ($query->withItems()) {
            $hydrationCriteria = PurchaseHydrationCriteria::createEmpty()->withPurchaseItem(withPurchasableItem: true);

            $this->purchaseHydratorCollection->hydrate(
                new PurchaseCollection([$purchase]),
                $hydrationCriteria
            );
        }

        return $purchase;
    }
}
