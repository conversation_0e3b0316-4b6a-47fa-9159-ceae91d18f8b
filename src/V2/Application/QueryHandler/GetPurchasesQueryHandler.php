<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\V2\Application\Query\GetPurchases;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PaginatedPurchaseCollection;
use App\V2\Domain\Purchase\PurchaseRepository;

readonly class GetPurchasesQueryHandler
{
    public function __construct(
        private PurchaseRepository $purchaseRepository,
    ) {
    }

    /**
     * @throws PurchaseRepositoryException
     */
    public function handle(GetPurchases $query): PaginatedPurchaseCollection
    {
        $collection = $this->purchaseRepository->findBy($query->getCriteria());
        $totalItems = $this->purchaseRepository->countBy($query->getCriteria());

        return new PaginatedPurchaseCollection(
            collection: $collection,
            totalItems: $totalItems
        );
    }
}
