<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\V2\Application\Query\Admin\GetFilterCategoriesQuery;
use App\V2\Domain\Filter\Exception\FilterCategoryNotFoundException;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCollection;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;

readonly class GetFilterCategoriesQueryHandler
{
    public function __construct(
        private FilterCategoryRepository $filterCategoryRepository,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws FilterCategoryNotFoundException
     */
    public function handle(GetFilterCategoriesQuery $query): FilterCategoryCollection
    {
        return $this->filterCategoryRepository->findBy(
            $query->getCriteria()
        );
    }
}
