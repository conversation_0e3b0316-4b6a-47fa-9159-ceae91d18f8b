<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\V2\Application\Hydrator\User\ManagerFilter\ManagerFilterHydratorCollection;
use App\V2\Application\Query\Admin\GetManagerFiltersQuery;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\User\Exception\GetManagerFiltersQueryHandlerException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterHydrationCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class GetManagerFiltersQueryHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private ManagerFilterRepository $managerFilterRepository,
        private ManagerFilterHydratorCollection $hydratorCollection,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws UserNotFoundException
     * @throws GetManagerFiltersQueryHandlerException
     */
    public function handle(GetManagerFiltersQuery $query): ManagerFilterCollection
    {
        $this->userRepository->findOneBy(
            UserCriteria::createById($query->getUserId())
        );

        $managerFilters = $this->managerFilterRepository->findBy(
            ManagerFilterCriteria::createEmpty()->filterByUserId($query->getUserId())
        );

        if ($query->needsFilters()) {
            try {
                $this->hydratorCollection->hydrate(
                    $managerFilters,
                    ManagerFilterHydrationCriteria::createEmpty()->withFilters()
                );
            } catch (HydratorException $e) {
                throw GetManagerFiltersQueryHandlerException::fromPrevious($e);
            }
        }

        return $managerFilters;
    }
}
