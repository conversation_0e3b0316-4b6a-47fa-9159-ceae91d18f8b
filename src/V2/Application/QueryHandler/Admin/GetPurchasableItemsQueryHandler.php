<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\V2\Application\Query\Admin\GetPurchasableItemsQuery;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PaginatedPurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemRepository;

readonly class GetPurchasableItemsQueryHandler
{
    public function __construct(
        private PurchasableItemRepository $purchasableItemRepository,
    ) {
    }

    /**
     * @throws PurchasableItemRepositoryException
     */
    public function handle(GetPurchasableItemsQuery $query): PaginatedPurchasableItemCollection
    {
        $purchasableItemsCollection = $this->purchasableItemRepository->findBy($query->getCriteria());
        $totalItems = $this->purchasableItemRepository->countBy($query->getCriteria());

        return new PaginatedPurchasableItemCollection($purchasableItemsCollection, $totalItems);
    }
}
