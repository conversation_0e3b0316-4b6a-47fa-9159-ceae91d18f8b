<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\V2\Application\Query\Admin\GetFiltersQuery;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;

readonly class GetFiltersQueryHandler
{
    public function __construct(
        private FilterRepository $filterRepository,
        private ManagerFilterRepository $managerFilterRepository,
    ) {
    }

    /**
     * Rule: If the user is manager, it will return only its assigned filter.
     * If no filters are assigned, return empty collection.
     *
     * @throws InfrastructureException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function handle(GetFiltersQuery $query): FilterCollection
    {
        $criteria = $query->getCriteria();
        if (!$query->getRequestedBy()->isAdmin() && $query->getRequestedBy()->isManager()) {
            $managed = $this->managerFilterRepository->findBy(
                ManagerFilterCriteria::createEmpty()
                    ->filterByUserId(new Id($query->getRequestedBy()->getId()))
            );

            if ($managed->isEmpty()) {
                return new FilterCollection([]);
            }

            $filtersIds = $managed->reduce(
                callback: function (array $carry, ManagerFilter $managerFilter) {
                    $carry[] = $managerFilter->getFilterId();

                    return $carry;
                },
                initial: []
            );

            $criteria->filterByIds(new IdCollection($filtersIds));
        }

        return $this->filterRepository->findBy($criteria);
    }
}
