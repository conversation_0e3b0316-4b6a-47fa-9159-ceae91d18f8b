<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\V2\Application\Query\Admin\GetPurchasableItem;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;

readonly class GetPurchasableItemQueryHandler
{
    public function __construct(
        private PurchasableItemRepository $purchasableItemRepository,
    ) {
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws PurchasableItemNotFoundException
     * @throws CriteriaException
     */
    public function handle(GetPurchasableItem $query): PurchasableItem
    {
        return $this->purchasableItemRepository->findOneBy(
            PurchasableItemCriteria::createById($query->getPurchasableItemId())
        );
    }
}
