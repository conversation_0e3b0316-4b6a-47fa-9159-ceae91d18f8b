<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\V2\Application\Hydrator\User\UserFilter\UserFilterHydratorCollection;
use App\V2\Application\Query\Admin\GetUserFiltersQuery;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\User\Exception\GetUserFiltersQueryHandlerException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterCriteria;
use App\V2\Domain\User\UserFilter\UserFilterHydrationCriteria;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use App\V2\Domain\User\UserRepository;

readonly class GetUserFiltersQueryHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private UserFilterRepository $userFilterRepository,
        private UserFilterHydratorCollection $hydratorCollection,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws UserNotFoundException
     * @throws GetUserFiltersQueryHandlerException
     */
    public function handle(GetUserFiltersQuery $query): UserFilterCollection
    {
        $this->userRepository->findOneBy(
            UserCriteria::createById($query->getUserId())
        );

        $userFilters = $this->userFilterRepository->findBy(
            UserFilterCriteria::createEmpty()->filterByUserId($query->getUserId())
        );

        if ($query->needsFilters()) {
            try {
                $this->hydratorCollection->hydrate(
                    $userFilters,
                    UserFilterHydrationCriteria::createEmpty()
                        ->withFilters()
                );
            } catch (HydratorException $e) {
                throw GetUserFiltersQueryHandlerException::fromPrevious($e);
            }
        }

        return $userFilters;
    }
}
