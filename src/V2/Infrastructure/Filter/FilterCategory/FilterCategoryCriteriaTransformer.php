<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Filter\FilterCategory;

use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Shared\Id\Id;

class FilterCategoryCriteriaTransformer
{
    public static function fromArray(array $params): FilterCategoryCriteria
    {
        $criteria = FilterCategoryCriteria::createEmpty();

        if (isset($params['name'])) {
            $criteria->filterByName($params['name']);
        }

        if (isset($params['parent_id'])) {
            $criteria->filterByParentId(new Id((int) $params['parent_id']));
        }

        return $criteria;
    }
}
