<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Filter\FilterCategory;

use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCollection;

class FilterCategoryTransformer
{
    public static function toArray(FilterCategory $category): array
    {
        return [
            'id' => $category->getId()->value(),
            'name' => $category->getName(),
        ];
    }

    public static function fromCollectionToArray(FilterCategoryCollection $collection): array
    {
        return array_map(
            fn (FilterCategory $category) => self::toArray($category),
            $collection->all(),
        );
    }
}
