<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Filter;

use App\V2\Domain\Filter\Exception\FilterNotFoundException;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryFilterRepository implements FilterRepository
{
    private FilterCollection $collection;

    public function __construct()
    {
        $this->collection = new FilterCollection([]);
    }

    public function add(Filter $filter): void
    {
        $this->collection->append($filter);
    }

    #[\Override]
    public function findOneBy(FilterCriteria $criteria): Filter
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new FilterNotFoundException();
        }

        return $result->first();
    }

    #[\Override]
    public function findBy(FilterCriteria $criteria): FilterCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(FilterCriteria $criteria): FilterCollection
    {
        $collection = $this->collection->filter(
            fn (Filter $filter) => (
                null === $criteria->getCategoryId()
                || $criteria->getCategoryId()->equals($filter->getFilterCategoryId())
            ) && (
                null === $criteria->getCategoriesIds()
                || \in_array("{$filter->getFilterCategoryId()}", $criteria->getCategoriesIds()->all())
            ) && (
                null === $criteria->getParentId()
                || (null !== $filter->getParentId() && $filter->getParentId()->equals($criteria->getParentId()))
            ) && (
                null === $criteria->getSearch()
                || str_contains($filter->getName(), $criteria->getSearch())
                || str_contains($filter->getCode(), $criteria->getSearch())
            )
        );

        $criteria->sortBy(
            new SortCollection([
                new Sort(
                    field: new SortableField('sort'),
                    direction: SortDirection::ASC,
                ),
            ])
        );

        /** @var FilterCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $collection);

        return $collection->map(
            fn (Filter $filter) => new Filter(
                id: $filter->getId(),
                filterCategoryId: $filter->getFilterCategoryId(),
                name: $filter->getName(),
                code: $filter->getCode(),
                sort: $filter->getSort(),
                parentId: $filter->getParentId(),
            )
        );
    }
}
