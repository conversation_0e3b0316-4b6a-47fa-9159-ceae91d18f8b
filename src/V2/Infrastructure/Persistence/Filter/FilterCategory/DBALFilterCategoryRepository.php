<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Filter\FilterCategory;

use App\V2\Domain\Filter\Exception\FilterCategoryNotFoundException;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCollection;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALFilterCategoryRepository implements FilterCategoryRepository
{
    public function __construct(
        private Connection $connection,
        private string $filterCategoryTableName,
    ) {
    }

    public function findOneBy(FilterCategoryCriteria $criteria): FilterCategory
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new FilterCategoryNotFoundException();
            }

            return $this->fromArrayToCategory($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    public function findBy(FilterCategoryCriteria $criteria): FilterCategoryCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new FilterCategoryCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToCategory($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    private function fromArrayToCategory(array $values): FilterCategory
    {
        return new FilterCategory(
            id: new Id($values['id']),
            parentId: $values['parent_id'] ? new Id($values['parent_id']) : null,
            name: $values['name'],
            sort: $values['sort'],
        );
    }

    /**
     * @throws CollectionException
     */
    private function getQueryBuilderByCriteria(FilterCategoryCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->filterCategoryTableName, 't');

        if (null !== $criteria->getName()) {
            $queryBuilder->andWhere('t.name LIKE :name')
                ->setParameter('name', '%' . $criteria->getName() . '%');
        }

        if (null !== $criteria->getParentId()) {
            $queryBuilder->andWhere('t.parent_id = :parent_id')
                ->setParameter('parent_id', $criteria->getParentId()->value());
        }

        $criteria->sortBy(
            new SortCollection([
                new Sort(
                    field: new SortableField('sort'),
                    direction: SortDirection::ASC,
                ),
            ])
        );

        DBALCommonCriteriaBuilder::filterByCommonCriteria(
            criteria: $criteria,
            queryBuilder: $queryBuilder,
        );

        return $queryBuilder;
    }
}
