<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Filter\FilterCategory;

use App\V2\Domain\Filter\Exception\FilterCategoryNotFoundException;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCollection;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryFilterCategoryRepository implements FilterCategoryRepository
{
    private FilterCategoryCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new FilterCategoryCollection([]);
    }

    public function add(FilterCategory $filterCategory): void
    {
        $this->collection->append(clone $filterCategory);
    }

    public function findOneBy(FilterCategoryCriteria $criteria): FilterCategory
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new FilterCategoryNotFoundException();
        }

        return $result->first();
    }

    public function findBy(FilterCategoryCriteria $criteria): FilterCategoryCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(FilterCategoryCriteria $criteria): FilterCategoryCollection
    {
        $collection = $this->collection->filter(
            fn (FilterCategory $category) => (
                null === $criteria->getName()
                    || false !== str_contains($category->getName(), $criteria->getName())
            ) && (
                null === $criteria->getParentId()
                    || (null !== $category->getParentId() && $category->getParentId()->equals($criteria->getParentId()))
            )
        );

        $criteria->sortBy(
            new SortCollection([
                new Sort(
                    field: new SortableField('sort'),
                    direction: SortDirection::ASC,
                ),
            ])
        );

        /** @var FilterCategoryCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $collection);

        return $collection->map(
            fn (FilterCategory $category) => new FilterCategory(
                id: $category->getId(),
                parentId: $category->getParentId(),
                name: $category->getName(),
                sort: $category->getSort(),
            ),
        );
    }
}
