<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\User\UserFilter;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserFilterNotFoundException;
use App\V2\Domain\User\Exception\UserFilterRepositoryException;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterCriteria;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALUserFilterRepository implements UserFilterRepository
{
    public function __construct(
        private Connection $connection,
        private string $userFilterTableName,
    ) {
    }

    public function insert(UserFilter $userFilter): void
    {
        $this->checkDuplicateFilter($userFilter);

        try {
            $this->connection
                ->insert(
                    table: $this->userFilterTableName,
                    data: $this->fromUserFilterToArray($userFilter),
                );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     * @throws UserFilterRepositoryException
     */
    private function checkDuplicateFilter(UserFilter $userFilter): void
    {
        try {
            $this->findOneBy(
                UserFilterCriteria::createEmpty()
                    ->filterByUserId($userFilter->getUserId())
                    ->filterByFilterId($userFilter->getFilterId()),
            );

            throw UserFilterRepositoryException::duplicateFilter(
                userId: $userFilter->getUserId(),
                filterId: $userFilter->getFilterId(),
            );
        } catch (UserFilterNotFoundException) {
        }
    }

    private function fromUserFilterToArray(UserFilter $userFilter): array
    {
        return [
            'user_id' => $userFilter->getUserId()->value(),
            'filter_id' => $userFilter->getFilterId()->value(),
        ];
    }

    private function fromArrayToUserFilter(array $values): UserFilter
    {
        return new UserFilter(
            userId: new Id($values['user_id']),
            filterId: new Id($values['filter_id']),
        );
    }

    public function findOneBy(UserFilterCriteria $criteria): UserFilter
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new UserFilterNotFoundException();
            }

            return $this->fromArrayToUserFilter($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    public function findBy(UserFilterCriteria $criteria): UserFilterCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new UserFilterCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToUserFilter($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    public function delete(UserFilter $userFilter): void
    {
        try {
            $this->connection->delete(
                table: $this->userFilterTableName,
                criteria: [
                    'user_id' => $userFilter->getUserId()->value(),
                    'filter_id' => $userFilter->getFilterId()->value(),
                ]
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    private function getQueryBuilderByCriteria(UserFilterCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->userFilterTableName, 't');

        if (null !== $criteria->getUserId()) {
            $qb->andWhere('t.user_id = :user_id')
            ->setParameter('user_id', $criteria->getUserId()->value());
        }

        if (null !== $criteria->getFilterId()) {
            $qb->andWhere('t.filter_id = :filter_id')
                ->setParameter('filter_id', $criteria->getFilterId()->value());
        }

        if (null !== $criteria->getFilterIds()) {
            $qb->andWhere('t.filter_id IN (:filter_ids)')
                ->setParameter('filter_ids', $criteria->getFilterIds()->all(), ArrayParameterType::STRING);
        }

        return $qb;
    }
}
