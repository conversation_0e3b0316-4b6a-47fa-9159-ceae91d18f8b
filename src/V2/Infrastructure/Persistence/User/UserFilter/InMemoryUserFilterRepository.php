<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\User\UserFilter;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserFilterNotFoundException;
use App\V2\Domain\User\Exception\UserFilterRepositoryException;
use App\V2\Domain\User\UserFilter\UserFilter;
use App\V2\Domain\User\UserFilter\UserFilterCollection;
use App\V2\Domain\User\UserFilter\UserFilterCriteria;
use App\V2\Domain\User\UserFilter\UserFilterRepository;

class InMemoryUserFilterRepository implements UserFilterRepository
{
    private UserFilterCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new UserFilterCollection([]);
    }

    #[\Override]
    public function insert(UserFilter $userFilter): void
    {
        try {
            $this->findOneBy(
                UserFilterCriteria::createEmpty()
                    ->filterByUserId($userFilter->getUserId())
                    ->filterByFilterId($userFilter->getFilterId()),
            );

            throw UserFilterRepositoryException::duplicateFilter(
                userId: $userFilter->getUserId(),
                filterId: $userFilter->getFilterId(),
            );
        } catch (UserFilterNotFoundException) {
            $this->collection->append(clone $userFilter);
        }
    }

    #[\Override]
    public function findOneBy(UserFilterCriteria $criteria): UserFilter
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new UserFilterNotFoundException();
        }

        return $result->first();
    }

    #[\Override]
    public function findBy(UserFilterCriteria $criteria): UserFilterCollection
    {
        return $this->filterByCriteria($criteria);
    }

    #[\Override]
    public function delete(UserFilter $userFilter): void
    {
        $this->collection = $this->collection->filter(
            fn (UserFilter $pUserFilter) => !$userFilter->getUserId()->equals($pUserFilter->getUserId())
                || !$userFilter->getFilterId()->equals($pUserFilter->getFilterId()),
        );
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(UserFilterCriteria $criteria): UserFilterCollection
    {
        $collection = $this->collection->filter(
            fn (UserFilter $userFilter) => (
                null === $criteria->getUserId()
                || $criteria->getUserId()->equals($userFilter->getUserId())
            )
            && (
                null === $criteria->getFilterId()
                || $criteria->getFilterId()->equals($userFilter->getFilterId())
            ) && (
                null === $criteria->getFilterIds()
                || \count($criteria->getFilterIds()->filter(
                    fn (Id $id) => $id->equals($userFilter->getFilterId())
                )) > 0
            )
        );

        return $collection->map(
            fn (UserFilter $userFilter) => new UserFilter(
                userId: $userFilter->getUserId(),
                filterId: $userFilter->getFilterId(),
            )
        );
    }
}
