<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\User\ManagerFilter;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\ManagerFilterNotFoundException;
use App\V2\Domain\User\Exception\ManagerFilterRepositoryException;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;

class InMemoryManagerFilterRepository implements ManagerFilterRepository
{
    private ManagerFilterCollection $collection;

    public function __construct()
    {
        $this->collection = new ManagerFilterCollection();
    }

    #[\Override]
    public function insert(ManagerFilter $managerFilter): void
    {
        try {
            $this->findOneBy(
                ManagerFilterCriteria::createEmpty()
                ->filterByUserId($managerFilter->getUserId())
                ->filterByFilterId($managerFilter->getFilterId())
            );

            throw ManagerFilterRepositoryException::duplicateFilter(
                userId: $managerFilter->getUserId(),
                filterId: $managerFilter->getFilterId(),
            );
        } catch (ManagerFilterNotFoundException) {
            $this->collection->append(clone $managerFilter);
        }
    }

    #[\Override]
    public function findOneBy(ManagerFilterCriteria $criteria): ManagerFilter
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new ManagerFilterNotFoundException();
        }

        return $result->first();
    }

    #[\Override]
    public function findBy(ManagerFilterCriteria $criteria): ManagerFilterCollection
    {
        return $this->filterByCriteria($criteria);
    }

    #[\Override]
    public function delete(ManagerFilter $managerFilter): void
    {
        $this->collection = $this->collection->filter(
            fn (ManagerFilter $pManagerFilter) => !$managerFilter->getUserId()->equals($pManagerFilter->getUserId())
                || !$managerFilter->getFilterId()->equals($pManagerFilter->getFilterId()),
        );
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(ManagerFilterCriteria $criteria): ManagerFilterCollection
    {
        $collection = $this->collection->filter(
            fn (ManagerFilter $managerFilter) => (
                null === $criteria->getUserId()
                || $criteria->getUserId()->equals($managerFilter->getUserId())
            ) && (
                null === $criteria->getFilterId()
                    || $criteria->getFilterId()->equals($managerFilter->getFilterId())
            ) && (
                null === $criteria->getFilterIds()
                    || \count($criteria->getFilterIds()->filter(
                        fn (Id $id) => $id->equals($managerFilter->getFilterId())
                    )) > 0
            )
        );

        return $collection->map(
            fn (ManagerFilter $managerFilter) => new ManagerFilter(
                userId: $managerFilter->getUserId(),
                filterId: $managerFilter->getFilterId(),
            )
        );
    }
}
