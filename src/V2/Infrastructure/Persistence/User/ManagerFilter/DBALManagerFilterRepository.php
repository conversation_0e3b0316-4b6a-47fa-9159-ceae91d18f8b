<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\User\ManagerFilter;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\ManagerFilterNotFoundException;
use App\V2\Domain\User\Exception\ManagerFilterRepositoryException;
use App\V2\Domain\User\ManagerFilter\ManagerFilter;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCollection;
use App\V2\Domain\User\ManagerFilter\ManagerFilterCriteria;
use App\V2\Domain\User\ManagerFilter\ManagerFilterRepository;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALManagerFilterRepository implements ManagerFilterRepository
{
    public function __construct(
        private Connection $connection,
        private string $managerFilterTableName,
    ) {
    }

    #[\Override]
    public function insert(ManagerFilter $managerFilter): void
    {
        $this->checkDuplicateFilter($managerFilter);

        try {
            $this->connection
                ->insert(
                    table: $this->managerFilterTableName,
                    data: $this->fromManagerFilterToArray($managerFilter),
                );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function checkDuplicateFilter(ManagerFilter $managerFilter): void
    {
        try {
            $this->findOneBy(
                ManagerFilterCriteria::createEmpty()
                    ->filterByUserId($managerFilter->getUserId())
                    ->filterByFilterId($managerFilter->getFilterId()),
            );

            throw ManagerFilterRepositoryException::duplicateFilter(
                userId: $managerFilter->getUserId(),
                filterId: $managerFilter->getFilterId(),
            );
        } catch (ManagerFilterNotFoundException) {
        }
    }

    private function fromManagerFilterToArray(ManagerFilter $managerFilter): array
    {
        return [
            'user_id' => $managerFilter->getUserId()->value(),
            'filter_id' => $managerFilter->getFilterId()->value(),
        ];
    }

    private function fromArrayToManagerFilter(array $values): ManagerFilter
    {
        return new ManagerFilter(
            userId: new Id($values['user_id']),
            filterId: new Id($values['filter_id']),
        );
    }

    #[\Override]
    public function findOneBy(ManagerFilterCriteria $criteria): ManagerFilter
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new ManagerFilterNotFoundException();
            }

            return $this->fromArrayToManagerFilter($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(ManagerFilterCriteria $criteria): ManagerFilterCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new ManagerFilterCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToManagerFilter($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(ManagerFilter $managerFilter): void
    {
        try {
            $this->connection->delete(
                table: $this->managerFilterTableName,
                criteria: [
                    'user_id' => $managerFilter->getUserId()->value(),
                    'filter_id' => $managerFilter->getFilterId()->value(),
                ]
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    private function getQueryBuilderByCriteria(ManagerFilterCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->managerFilterTableName, 't');

        if (null !== $criteria->getUserId()) {
            $qb->andWhere('t.user_id = :user_id')
                ->setParameter('user_id', $criteria->getUserId()->value());
        }

        if (null !== $criteria->getFilterId()) {
            $qb->andWhere('t.filter_id = :filter_id')
                ->setParameter('filter_id', $criteria->getFilterId()->value());
        }

        if (null !== $criteria->getFilterIds()) {
            $qb->andWhere('t.filter_id IN (:filter_ids)')
                ->setParameter('filter_ids', $criteria->getFilterIds()->all(), ArrayParameterType::STRING);
        }

        return $qb;
    }
}
