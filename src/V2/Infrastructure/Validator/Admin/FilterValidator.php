<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class FilterValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateGetFilters(array $payload): void
    {
        $constraints = new Collection([
            'search' => [
                new Optional([
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                ]),
            ],
            'parent_id' => [
                new Optional([
                    new Constraints\NotBlank(),
                    new Constraints\Type('digit'),
                    new Constraints\Callback(callback: function ($parentId, ExecutionContextInterface $context) {
                        if ($parentId < 1) {
                            $context->buildViolation('parent_id must be greater than 0.')->addViolation();
                        }
                    }),
                ]),
            ],
            'category_id' => [
                new Optional([
                    new Constraints\NotBlank(),
                    new Constraints\Type('digit'),
                    new Constraints\Callback(callback: function ($parentId, ExecutionContextInterface $context) {
                        if ($parentId < 1) {
                            $context->buildViolation('category_id must be greater than 0.')->addViolation();
                        }
                    }),
                ]),
            ],
        ]);

        parent::validate($payload, $constraints);
    }
}
