<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;

class UserFilterValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validatePostUserFilters(array $data): void
    {
        $constraints = [
            new Constraints\NotBlank(message: 'Body cannot be empty'),
            new Constraints\Type('array'),
            new Constraints\All([
                new Constraints\Type('integer'),
                new Constraints\NotBlank(),
                new Constraints\GreaterThanOrEqual(1),
            ]),
        ];

        parent::validate($data, $constraints);
    }

    public static function validateDeleteUserFilters(array $data): void
    {
        $constraints = [
            new Constraints\NotBlank(message: 'Body cannot be empty'),
            new Constraints\Type('array'),
            new Constraints\All([
                new Constraints\Type('integer'),
                new Constraints\NotBlank(),
                new Constraints\GreaterThanOrEqual(1),
            ]),
        ];

        parent::validate($data, $constraints);
    }
}
