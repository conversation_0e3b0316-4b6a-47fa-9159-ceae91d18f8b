<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\Constraints\Type;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class PatchPurchasableItemValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validatePatchPurchasableItemRequest(array $data): void
    {
        $constraints = [
            new Constraints\NotBlank(message: 'Body cannot be empty'),
            new Constraints\Collection([
                'price_amount' => new Optional([
                    new Constraints\NotBlank(),
                    new Type('integer'),
                    new Constraints\GreaterThanOrEqual(0),
                ]),
                'price_currency' => new Optional([
                    new Constraints\NotBlank(),
                    new Type('string'),
                    new Constraints\Choice(['EUR', 'USD']),
                    new Constraints\Callback(callback: function ($value, ExecutionContextInterface $context) {
                        // if price_currency is provided, price_amount must also be provided
                        if (
                            !isset($context->getRoot()['price_amount'])
                            || null === $context->getRoot()['price_amount']
                        ) {
                            $context->buildViolation('price_amount is required when price_currency is provided.')
                                ->addViolation();
                        }
                    }),
                ]),
                'is_active' => new Optional([
                    new Type('bool'),
                ]),
            ]),
        ];

        parent::validate($data, $constraints);
    }
}
