<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class PutPurchasableItemValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validatePutPurchasableItemRequest(array $data): void
    {
        $constraints = [
            new Constraints\NotBlank(message: 'Body cannot be empty'),
            new Constraints\Collection([
                'type' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                    new Constraints\Choice(['course', 'subscription']),
                ],
                'resource_id' => [
                    new Constraints\NotBlank(),
                    new Callback(function ($value, ExecutionContextInterface $context) {
                        if (
                            !\is_int($value) && !(\is_string($value) && preg_match(
                                '/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i',
                                $value
                            ))
                        ) {
                            $context->buildViolation('This value should be an integer or a UUID v4.')
                                ->addViolation();
                        }
                    }),
                    new Callback(function ($value, ExecutionContextInterface $context) {
                        if (\is_int($value) && $value < 1) {
                            $context->buildViolation('This value should be greater than 0.')
                                ->addViolation();
                        }
                    }),
                ],
                'price_amount' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('integer'),
                    new Constraints\GreaterThanOrEqual(0),
                ],
                'price_currency' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                    new Constraints\Choice(['EUR', 'USD']),
                ],
            ]),
        ];

        parent::validate($data, $constraints);
    }
}
