<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Campus;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\DateTime;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\Constraints\Type;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class GetPurchasesValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateGetPurchasesRequest(array $data): void
    {
        $constraints = new Collection([
            'page' => [
                new Optional([
                    new NotBlank(),
                    new Type('digit'),
                    new Callback(callback: function ($page, ExecutionContextInterface $context) {
                        if ($page < 1) {
                            $context->buildViolation('Page must be greater than 0.')->addViolation();
                        }

                        if (!isset($context->getRoot()['page_size'])) {
                            $context->buildViolation('Page size is required when page is provided.')->addViolation();
                        }
                    }),
                ]),
            ],
            'page_size' => [
                new Optional([
                    new NotBlank(),
                    new Type('digit'),
                    new Callback(callback: function ($pageSize, ExecutionContextInterface $context) {
                        if ($pageSize < 1) {
                            $context->buildViolation('Page size must be greater than 0.')->addViolation();
                        }

                        if (!isset($context->getRoot()['page'])) {
                            $context->buildViolation('Page is required when page size is provided.')->addViolation();
                        }
                    }),
                ]),
            ],
            'amount_min' => [
                new Optional([
                    new NotBlank(),
                    new Type('digit'),
                ]),
            ],
            'amount_max' => [
                new Optional([
                    new NotBlank(),
                    new Type('digit'),
                ]),
            ],
            'status' => [
                new Optional([
                    new NotBlank(),
                    new Choice([
                        'pending',
                        'completed',
                        'cancelled',
                    ]),
                ]),
            ],
            'start_date' => [
                new Optional([
                    new NotBlank(),
                    new Type('string'),
                    new DateTime('Y-m-d H:i:s'),
                ]),
            ],
            'end_date' => [
                new Optional([
                    new NotBlank(),
                    new Type('string'),
                    new DateTime('Y-m-d H:i:s'),
                ]),
            ],
            'sort_by' => [
                new Optional([
                    new NotBlank(),
                    new Type('string'),
                    new Callback(callback: function ($page, ExecutionContextInterface $context) {
                        if (!isset($context->getRoot()['sort_dir'])) {
                            $context->buildViolation(
                                'Sort direction is required when sort by is provided.'
                            )->addViolation();
                        }
                    }),
                ]),
            ],
            'sort_dir' => [
                new Optional([
                    new NotBlank(),
                    new Type('string'),
                    new Choice([
                        'asc',
                        'desc',
                    ]),
                    new Callback(callback: function ($page, ExecutionContextInterface $context) {
                        if (!isset($context->getRoot()['sort_by'])) {
                            $context->buildViolation(
                                'Sort by is required when sort direction is provided.'
                            )->addViolation();
                        }
                    }),
                ]),
            ],
        ]);

        parent::validate($data, $constraints);
    }
}
