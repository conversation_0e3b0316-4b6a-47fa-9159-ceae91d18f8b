<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase;

use App\V2\Application\DTO\Purchase\PatchPurchasableItemInputDTO;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Infrastructure\Shared\Financial\MoneyTransformer;

class PatchPurchasableItemInputTransformer
{
    /**
     * @throws InvalidCurrencyCodeException
     */
    public static function fromPayload(array $payload): PatchPurchasableItemInputDTO
    {
        $price = null;
        $isActive = null;

        // Handle price transformation only if both price_amount and price_currency are provided
        $hasPriceAmount = isset($payload['price_amount']);
        $hasPriceCurrency = isset($payload['price_currency']);

        if ($hasPriceAmount && $hasPriceCurrency) {
            // Both fields present - transform normally
            $price = MoneyTransformer::fromPayload($payload);
        }

        // Handle is_active field if provided
        if (isset($payload['is_active'])) {
            $isActive = $payload['is_active'];
        }

        return new PatchPurchasableItemInputDTO(
            price: $price,
            isActive: $isActive,
        );
    }
}
