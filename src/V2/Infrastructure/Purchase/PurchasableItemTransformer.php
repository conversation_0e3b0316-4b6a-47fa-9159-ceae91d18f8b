<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase;

use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;

class PurchasableItemTransformer
{
    public static function fromCollectionToArray(PurchasableItemCollection $collection): array
    {
        return array_map(
            fn (PurchasableItem $item) => self::fromPurchasableItemToArray($item),
            $collection->all()
        );
    }

    public static function fromPurchasableItemToArray(PurchasableItem $item): array
    {
        return [
            'id' => $item->getId()->value(),
            'name' => $item->getName(),
            'description' => $item->getDescription(),
            'price_amount' => $item->getPrice()->value(),
            'price_currency' => CurrencyCodeTransformer::toString($item->getPrice()->currency()->code()),
            'resource_type' => ResourceTypeTransformer::toString($item->getResource()->getType()),
            'resource_id' => $item->getResource()->getId()->value(),
            'is_active' => $item->getIsActive(),
        ];
    }
}
