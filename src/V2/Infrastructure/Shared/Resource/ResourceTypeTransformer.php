<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\Resource;

use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class ResourceTypeTransformer
{
    public static function toString(ResourceType $resourceType): string
    {
        return match ($resourceType) {
            ResourceType::Course => 'course',
            ResourceType::Subscription => 'subscription',
        };
    }

    public static function fromString(string $resourceType): ResourceType
    {
        return match ($resourceType) {
            'course' => ResourceType::Course,
            'subscription' => ResourceType::Subscription,
        };
    }

    /**
     * @throws InvalidUuidException
     */
    public static function getIdentifier(ResourceType $resourceType, mixed $id): Identifier
    {
        return match ($resourceType) {
            ResourceType::Course => new Id((int) $id),
            ResourceType::Subscription => new Uuid($id),
        };
    }
}
