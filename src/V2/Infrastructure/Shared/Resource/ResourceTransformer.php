<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\Resource;

use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

class ResourceTransformer
{
    /**
     * @throws InvalidUuidException
     */
    public static function fromPayload(array $payload): Resource
    {
        $resourceType = ResourceTypeTransformer::fromString($payload['type']);

        return new Resource(
            type: $resourceType,
            id: ResourceTypeTransformer::getIdentifier($resourceType, $payload['resource_id']),
        );
    }
}
