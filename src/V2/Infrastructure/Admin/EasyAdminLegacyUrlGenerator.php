<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Admin;

use App\V2\Application\Admin\LegacyAdminUrlGeneratorInterface;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;

final class EasyAdminLegacyUrlGenerator implements LegacyAdminUrlGeneratorInterface
{
    private AdminUrlGenerator $adminUrlGenerator;

    public function __construct(AdminUrlGenerator $adminUrlGenerator)
    {
        $this->adminUrlGenerator = $adminUrlGenerator;
    }

    public function setDashboard(string $dashboardControllerFqcn): self
    {
        $this->adminUrlGenerator->setDashboard($dashboardControllerFqcn);
        return $this;
    }

    public function setController(string $crudControllerFqcn): self
    {
        $this->adminUrlGenerator->setController($crudControllerFqcn);
        return $this;
    }

    public function setAction(string $action): self
    {
        $this->adminUrlGenerator->setAction($action);
        return $this;
    }

    public function setRoute(string $routeName, array $routeParameters = []): self
    {
        $this->adminUrlGenerator->setRoute($routeName, $routeParameters);
        return $this;
    }

    public function setEntityId($entityId): self
    {
        $this->adminUrlGenerator->setEntityId($entityId);
        return $this;
    }

    public function get(string $paramName)
    {
        return $this->adminUrlGenerator->get($paramName);
    }

    public function set(string $paramName, $paramValue): self
    {
        $this->adminUrlGenerator->set($paramName, $paramValue);
        return $this;
    }

    public function setAll(array $routeParameters): self
    {
        $this->adminUrlGenerator->setAll($routeParameters);
        return $this;
    }

    public function unset(string $paramName): self
    {
        $this->adminUrlGenerator->unset($paramName);
        return $this;
    }

    public function unsetAll(): self
    {
        $this->adminUrlGenerator->unsetAll();
        return $this;
    }

    public function unsetAllExcept(string ...$namesOfParamsToKeep): self
    {
        $this->adminUrlGenerator->unsetAllExcept(...$namesOfParamsToKeep);
        return $this;
    }

    public function includeReferrer(): self
    {
        $this->adminUrlGenerator->includeReferrer();
        return $this;
    }

    public function removeReferrer(): self
    {
        $this->adminUrlGenerator->removeReferrer();
        return $this;
    }

    public function addSignature(bool $addSignature = true): self
    {
        $this->adminUrlGenerator->addSignature($addSignature);
        return $this;
    }

    public function getSignature(): string
    {
        return $this->adminUrlGenerator->getSignature();
    }

    public function generateUrl(): string
    {
        return $this->adminUrlGenerator->generateUrl();
    }

    public function __toString(): string
    {
        return $this->adminUrlGenerator->__toString();
    }
}
