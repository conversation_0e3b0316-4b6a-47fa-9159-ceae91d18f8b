<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Campus;

use App\V2\Application\Query\Campus\GetLocalesQuery;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetLocalesController extends QueryBusAccessor
{
    public function __invoke(Request $request): Response
    {
        $locales = $this->ask(new GetLocalesQuery());

        return new JsonResponse(
            [
                'data' => $locales,
            ],
            Response::HTTP_OK
        );
    }
}
