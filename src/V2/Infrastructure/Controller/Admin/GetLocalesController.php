<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetLocalesQuery;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetLocalesController extends QueryBusAccessor
{
    public function __invoke(Request $request): Response
    {
        $locales = $this->ask(new GetLocalesQuery());

        return new JsonResponse(
            [
                'data' => $locales,
            ],
            Response::HTTP_OK
        );
    }
}
