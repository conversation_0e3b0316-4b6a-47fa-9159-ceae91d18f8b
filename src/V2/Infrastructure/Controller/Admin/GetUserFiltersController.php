<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetUserFiltersQuery;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\User\UserFilterTransformer;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetUserFiltersController extends QueryBusAccessor
{
    public function __invoke(Request $request, int $userId): Response
    {
        IdValidator::validateId($userId);

        $collection = $this->ask(
            new GetUserFiltersQuery(
                userId: new Id($userId),
                withFilters: true,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                UserFilterTransformer::fromCollectionToArray($collection)
            )->toArray()
        );
    }
}
