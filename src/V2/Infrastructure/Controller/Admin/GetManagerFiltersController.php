<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetManagerFiltersQuery;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\User\ManagerFilterTransformer;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetManagerFiltersController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     */
    public function __invoke(Request $request, int $userId): Response
    {
        IdValidator::validateId($userId);

        $collection = $this->ask(
            new GetManagerFiltersQuery(
                userId: new Id($userId),
                withFilters: true
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                ManagerFilterTransformer::fromCollectionToArray($collection)
            )->toArray(),
        );
    }
}
