<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetPurchasableItem;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Purchase\PurchasableItemTransformer;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetPurchasableItemController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws InvalidUuidException
     */
    public function __invoke(string $purchasableItemId): Response
    {
        UuidValidator::validateUuid($purchasableItemId);

        $purchasableItem = $this->ask(
            new GetPurchasableItem(
                purchasableItemId: new Uuid($purchasableItemId),
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                PurchasableItemTransformer::fromPurchasableItemToArray($purchasableItem)
            )->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
