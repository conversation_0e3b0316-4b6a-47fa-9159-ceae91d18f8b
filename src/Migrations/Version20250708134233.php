<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250708134233 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME NOT NULL, ADD updated_at DATETIME NOT NULL, ADD deleted_at DATETIME DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word ADD CONSTRAINT FK_EDB2A53CB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word ADD CONSTRAINT FK_EDB2A53C896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word ADD CONSTRAINT FK_EDB2A53CC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EDB2A53CB03A8386 ON roulette_word (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EDB2A53C896DBBDE ON roulette_word (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_EDB2A53CC76F1F52 ON roulette_word (deleted_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word DROP FOREIGN KEY FK_EDB2A53CB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word DROP FOREIGN KEY FK_EDB2A53C896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word DROP FOREIGN KEY FK_EDB2A53CC76F1F52
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_EDB2A53CB03A8386 ON roulette_word
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_EDB2A53C896DBBDE ON roulette_word
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_EDB2A53CC76F1F52 ON roulette_word
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE roulette_word DROP created_by_id, DROP updated_by_id, DROP deleted_by_id, DROP created_at, DROP updated_at, DROP deleted_at
        SQL);
    }
}
