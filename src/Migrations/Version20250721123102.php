<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250721123102 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
        UPDATE chapter SET is_active = 1 WHERE is_active IS NULL AND deleted_at IS NULL
    SQL);

        $this->addSql(<<<'SQL'
        ALTER TABLE chapter MODIFY is_active TINYINT(1) NOT NULL DEFAULT 1
    SQL);
    }
}
