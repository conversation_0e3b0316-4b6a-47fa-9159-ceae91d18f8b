<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250708131558 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Add soft delete support to ordenar_menormayor table
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME NOT NULL, ADD updated_at DATETIME NOT NULL, ADD deleted_at DATETIME DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor ADD CONSTRAINT FK_C414560BB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor ADD CONSTRAINT FK_C414560B896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor ADD CONSTRAINT FK_C414560BC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C414560BB03A8386 ON ordenar_menormayor (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C414560B896DBBDE ON ordenar_menormayor (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_C414560BC76F1F52 ON ordenar_menormayor (deleted_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove soft delete support from ordenar_menormayor table
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor DROP FOREIGN KEY FK_C414560BB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor DROP FOREIGN KEY FK_C414560B896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor DROP FOREIGN KEY FK_C414560BC76F1F52
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_C414560BB03A8386 ON ordenar_menormayor
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_C414560B896DBBDE ON ordenar_menormayor
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_C414560BC76F1F52 ON ordenar_menormayor
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE ordenar_menormayor DROP created_by_id, DROP updated_by_id, DROP deleted_by_id, DROP created_at, DROP updated_at, DROP deleted_at
        SQL);
    }
}
