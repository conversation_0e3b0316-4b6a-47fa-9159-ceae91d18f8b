<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250714101700 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
               CREATE TABLE purchase (
                    id CHAR(36) NOT NULL,
                    user_id int NOT NULL,
                    payment_id CHAR(36) NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    amount INT NOT NULL,
                    currency_code VARCHAR(3) NOT NULL,
                    tax_rate FLOAT NOT NULL,
                    tax_amount INT NOT NULL,
                    created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)',
                    updated_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
                    deleted_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
                    PRIMARY KEY(id),
                    FOREIGN KEY (user_id) REFERENCES `user` (id) ON DELETE CASCADE                                     
                ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
            SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE purchase');
    }
}
