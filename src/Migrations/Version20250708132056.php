<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250708132056 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Add soft delete support to true_or_false table

        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME NOT NULL, ADD updated_at DATETIME NOT NULL, ADD deleted_at DATETIME DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false ADD CONSTRAINT FK_CA13A6C3B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false ADD CONSTRAINT FK_CA13A6C3896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false ADD CONSTRAINT FK_CA13A6C3C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_CA13A6C3B03A8386 ON true_or_false (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_CA13A6C3896DBBDE ON true_or_false (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_CA13A6C3C76F1F52 ON true_or_false (deleted_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Remove soft delete support from true_or_false table

        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false DROP FOREIGN KEY FK_CA13A6C3B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false DROP FOREIGN KEY FK_CA13A6C3896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false DROP FOREIGN KEY FK_CA13A6C3C76F1F52
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_CA13A6C3B03A8386 ON true_or_false
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_CA13A6C3896DBBDE ON true_or_false
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_CA13A6C3C76F1F52 ON true_or_false
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE true_or_false DROP created_by_id, DROP updated_by_id, DROP deleted_by_id, DROP created_at, DROP updated_at, DROP deleted_at
        SQL);
    }
}
