<?php

declare(strict_types=1);

namespace App\Campus\Controller\CourseSection;

use App\Campus\Controller\Base\BaseController;
use App\Campus\Service\CourseSection\SectionDetailService;
use App\Campus\Service\CourseSection\SectionsService;
use App\Entity\CourseSection;
use App\Serializer\Normalizer\CourseSectionNormalizer;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/api")
 */
class SectionController extends BaseController
{
    private SectionDetailService $sectionDetailService;
    private SectionsService $sectionsService;

    /**
     * ApiController constructor.
     */
    public function __construct(
        SettingsService $settings,
        EntityManagerInterface $em,
        TranslatorInterface $translator,
        SectionDetailService $sectionDetailService,
        SectionsService $sectionsService
    ) {
        parent::__construct($settings, $em, $translator);

        $this->sectionDetailService = $sectionDetailService;
        $this->sectionsService = $sectionsService;
    }

    /**
     * @Rest\Get("/sections", name="api_course_sections_list-user")
     */
    public function courseSectionsList(CourseSectionNormalizer $courseSectionNormalizer, Request $request): Response
    {
        if (!$this->settings->get('app.openCourse') && !$this->settings->get('app.user.useFilters')) {
            return new Response(
                json_encode([
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => [
                        'sections' => [],
                        'training' => [],
                    ],
                ])
            );
        }

        $sections = $this->sectionsService->getCoursesSectionForCampus();

        return new Response(
            json_encode([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'sections' => $sections,
                    'training' => [],
                ],
            ])
        );
    }

    /**
     * @Route("/sections/{id}", name="api_course_sections_detail-new")
     * @Route("/sections/{slug}", name="api_course_sections_detail_slug-new")
     */
    public function courseSectionDetail(CourseSection $courseSection): Response
    {
        return $this->executeSafe(function () use ($courseSection) {
            return $this->sectionDetailService->getCourseSectionDetail($courseSection);
        }, [], ['list']);
    }
}
