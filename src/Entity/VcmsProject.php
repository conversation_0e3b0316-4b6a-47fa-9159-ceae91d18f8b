<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\VcmsProjectRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;

/**
 * @ORM\Entity(repositoryClass=VcmsProjectRepository::class)
 *
 * @ORM\Table(name="vcms_project")
 */
class VcmsProject
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"list", "detail"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list", "detail"})
     */
    private $title;

    /**
     * @ORM\Column(type="json")
     *
     * @Groups({"detail"})
     */
    private $slides = [];

    /**
     * @ORM\Column(type="string", length=40, nullable=true)
     *
     * @Groups({"detail"})
     */
    private $view;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     *
     * @Groups({"detail"})
     */
    private $progressive = false;

    public function __construct()
    {
        $this->addSlide();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getSlides(): ?array
    {
        return $this->slides;
    }

    public function setSlides(array $slides): self
    {
        $this->slides = $slides;

        return $this;
    }

    public function getView(): ?string
    {
        return $this->view;
    }

    public function setView(?string $view): self
    {
        $this->view = $view;

        return $this;
    }

    /**
     * @Groups({"list"})
     *
     * @SerializedName("slides")
     */
    public function getSlidesCount(): int
    {
        return \count($this->slides);
    }

    public function addSlide($config = []): array
    {
        $id = $this->getNewSlideId();
        $order = $this->getSlidesCount() + 1;

        $default = [
            'id' => $id,
            'template' => [
                'value' => '"A"',
                'gap' => 0,
            ],
            'properties' => null,
            'elements' => [],
            'order' => $order,
        ];

        $config = array_filter($config);
        $slide = array_merge($default, $config);

        $this->slides[] = $slide;

        return $slide;
    }

    public function saveSlideElement($id, $data): ?array
    {
        if (($key = array_search($id, array_column($this->slides, 'id'))) !== false) {
            $id = $this->getNewElementId();
            $newElement = array_merge($data, ['id' => $id]);
            $this->slides[$key]['elements'] = [...$this->slides[$key]['elements'], $newElement];

            return $newElement;
        }

        return null;
    }

    public function removeSlide($id)
    {
        if (($key = array_search($id, array_column($this->slides, 'id'))) !== false) {
            unset($this->slides[$key]);
        }

        $this->slides = array_values($this->slides);
    }

    private function getNewSlideId()
    {
        $id = $this->getMaxFrom($this->slides, 'id');

        return $id + 1;
    }

    private function getNewElementId()
    {
        $maxId = 0;
        foreach ($this->slides as $slide) {
            $newMaxId = $this->getMaxFrom($slide['elements'], 'id');
            if ($newMaxId > $maxId) {
                $maxId = $newMaxId;
            }
        }

        return $maxId + 1;
    }

    private function getMaxFrom($arr, $propertyName)
    {
        try {
            return max(array_column($arr, $propertyName));
        } catch (\Throwable $th) {
            return 0;
        }
    }

    public function isProgressive(): bool
    {
        return $this->progressive;
    }

    public function setProgressive(bool $progressive): self
    {
        $this->progressive = $progressive;

        return $this;
    }

    public function hasSlidesWithContent(): bool
    {
        if (empty($this->slides)) {
            return false;
        }

        return array_reduce($this->slides, function ($carry, $slide) {
            return $carry || (isset($slide['elements']) && \count($slide['elements']) > 0);
        }, false);
    }
}
