<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Repository\RouletteWordRepository;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=RouletteWordRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class RouletteWord
{
    use Blamable;
    use Timestampable;
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"letter","roulette"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="rouletteWords")
     */
    private $chapter;

    /**
     * @ORM\Column(type="string", length=2)
     *
     * @Groups({"letter","roulette","results"})
     */
    private $letter;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"letter","results"})
     */
    private $word;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"letter","roulette","results"})
     */
    private $question;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     *
     * @Groups({"letter","roulette","results"})
     */
    private $type;

    public function __toString()
    {
        return $this->letter;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getLetter(): ?string
    {
        return $this->letter;
    }

    public function setLetter(string $letter): self
    {
        $this->letter = $letter;

        return $this;
    }

    public function getWord(): ?string
    {
        return $this->word;
    }

    public function setWord(?string $word): self
    {
        $this->word = $word;

        return $this;
    }

    public function getQuestion(): ?string
    {
        return $this->question;
    }

    public function setQuestion(?string $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getType(): ?bool
    {
        return $this->type;
    }

    public function setType(?bool $type): self
    {
        $this->type = $type;

        return $this;
    }
}
