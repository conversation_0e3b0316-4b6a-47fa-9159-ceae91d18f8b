<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Repository\VideopreguntasRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=VideopreguntasRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @Vich\Uploadable()
 */
class Videopreguntas
{
    use Blamable;
    use Timestampable;
    use \App\Behavior\Imageable;
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"roulette","videoquiz"})
     */
    private $id;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"roulette","videoquiz"})
     */
    private $currenttime;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"roulette","videoquiz"})
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="video_game_videoquiz", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"roulette","videoquiz"})
     */
    private $texto;

    /**
     * @ORM\Column(type="text")
     *
     * @Groups({"roulette","videoquiz"})
     */
    private $respuestas;

    /**
     * @ORM\ManyToOne(targetEntity=Videoquiz::class, inversedBy="videopreguntas")
     */
    private $videoquiz;

    /**
     * @ORM\OneToMany(targetEntity=AnswersVideoQuiz::class, mappedBy="question", cascade={"persist"})
     *
     * @Groups({"videoquiz"})
     */
    private $answersVideoQuizzes;

    public function __construct()
    {
        $this->answersVideoQuizzes = new ArrayCollection();
    }

    public function __clone()
    {
        $this->id = null;

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }

        $answersVideoQuizzes = $this->getAnswersVideoQuizzes();
        $this->answersVideoQuizzes = new ArrayCollection();
        foreach ($answersVideoQuizzes as $answersVideoQuiz) {
            $answersVideoQuizClone = clone $answersVideoQuiz;
            $answersVideoQuizClone->setQuestion($this);
            $this->addAnswersVideoQuiz($answersVideoQuizClone);
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCurrenttime(): ?int
    {
        return $this->currenttime;
    }

    public function setCurrenttime(int $currenttime): self
    {
        $this->currenttime = $currenttime;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(?string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getTexto(): ?string
    {
        return $this->texto;
    }

    public function setTexto(?string $texto): self
    {
        $this->texto = $texto;

        return $this;
    }

    public function getRespuestas(): ?string
    {
        return $this->respuestas;
    }

    public function setRespuestas(string $respuestas): self
    {
        $this->respuestas = $respuestas;

        return $this;
    }

    public function getVideoquiz(): ?Videoquiz
    {
        return $this->videoquiz;
    }

    public function setVideoquiz(?Videoquiz $videoquiz): self
    {
        $this->videoquiz = $videoquiz;

        return $this;
    }

    /**
     * @return mixed
     */
    public function setImageFile($imageFile): self
    {
        $this->imageFile = $imageFile;

        return $this;
    }

    public function getImageFile()
    {
        return $this->imageFile;
    }

    /**
     * @return Collection<int, AnswersVideoQuiz>
     */
    public function getAnswersVideoQuizzes(): Collection
    {
        return $this->answersVideoQuizzes;
    }

    public function addAnswersVideoQuiz(AnswersVideoQuiz $answersVideoQuiz): self
    {
        if (!$this->answersVideoQuizzes->contains($answersVideoQuiz)) {
            $this->answersVideoQuizzes[] = $answersVideoQuiz;
            $answersVideoQuiz->setQuestion($this);
        }

        return $this;
    }

    public function removeAnswersVideoQuiz(AnswersVideoQuiz $answersVideoQuiz): self
    {
        if ($this->answersVideoQuizzes->removeElement($answersVideoQuiz)) {
            // set the owning side to null (unless already changed)
            if ($answersVideoQuiz->getQuestion() === $this) {
                $answersVideoQuiz->setQuestion(null);
            }
        }

        return $this;
    }
}
