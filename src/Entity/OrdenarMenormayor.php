<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Repository\OrdenarMenormayorRepository;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=OrdenarMenormayorRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class OrdenarMenormayor
{
    use Blamable;
    use Timestampable;
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"roulette","ordenarMenorMayor"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"roulette","ordenarMenorMayor"})
     */
    private $words_array;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"roulette","ordenarMenorMayor"})
     */
    private $title;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"roulette","ordenarMenorMayor"})
     */
    private $time;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="ordenarMenormayors")
     */
    private $chapter;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"roulette","ordenarMenorMayor"})
     */
    private $gametype;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getWordsArray(): ?string
    {
        return $this->words_array;
    }

    public function setWordsArray(string $words_array): self
    {
        $this->words_array = $words_array;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getTime(): ?int
    {
        return $this->time;
    }

    public function setTime(int $time): self
    {
        $this->time = $time;

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getGametype(): ?string
    {
        return $this->gametype;
    }

    public function setGametype(?string $gametype): self
    {
        $this->gametype = $gametype;

        return $this;
    }
}
