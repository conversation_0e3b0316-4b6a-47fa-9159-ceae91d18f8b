<?php

declare(strict_types=1);

namespace App\Entity;

use App\Admin\Traits\LanguagesTrait;
use App\Repository\CourseRepository;
use App\V2\Domain\Course\Diploma\DiplomaConfig;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\OrderBy;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @ORM\Entity(repositoryClass=CourseRepository::class)
 *
 * @Vich\Uploadable()
 */
class Course
{
    use AtAndBy;
    use Imageable;
    use LanguagesTrait;

    private const float GAME_POINT_PERCENTAGE = 0.5;
    private const float CONTENT_POINT_PERCENTAGE = 0.5;
    private const int COURSE_ONLINE = 1;
    private const int COURSE_PRESS = 2;

    public const array THUMBNAILS = ['small', 'medium'];
    public const int MAX_POINTS = 500;

    /**
     * @ORM\Id()
     *
     * @ORM\GeneratedValue()
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"list", "detail", "passport", "user_area","forumChanelList", "update-course"})
     */
    private ?int $id = null;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list", "detail", "passport", "update-course"})
     */
    private $code;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list", "detail", "passport","forumChanelList", "update-course"})
     */
    private $name;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"list", "detail", "update-course"})
     */
    private $description;

    /**
     * @ORM\OneToMany(targetEntity=Announcement::class, mappedBy="course", orphanRemoval=true)
     */
    private $announcements;

    /**
     * @ORM\OneToMany(targetEntity=Chapter::class, mappedBy="course", orphanRemoval=true, cascade={"persist"})
     *
     * @ORM\OrderBy({"season" = "ASC", "position" = "ASC"})
     */
    private $chapters;

    /**
     * @ORM\ManyToMany(targetEntity=ProfessionalCategory::class, inversedBy="courses")
     *
     * @Groups({"detail"})
     */
    private $categories;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"list", "detail", "passport", "update-course"})
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="course_image", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @ORM\Column(type="boolean")
     *
     * @Groups({"update-course"})
     */
    private $open;

    /**
     * @ORM\Column(type="string", length=10)
     *
     * @Groups({"update-course"})
     */
    private $locale;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="translations")
     */
    private $translation;

    /**
     * @ORM\OneToMany(targetEntity=Course::class, mappedBy="translation", orphanRemoval=true)
     */
    private $translations;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"update-course"})
     */
    private ?int $points = 0;

    /**
     * @ORM\ManyToOne(targetEntity=CourseCategory::class, inversedBy="courses")
     *
     * @ORM\JoinColumn(nullable=true, onDelete="SET NULL")
     *
     * @Groups({"detail", "update-course"})
     */
    private $category;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"detail", "update-course"})
     */
    private $generalInformation;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     *
     * @Groups({"list", "update-course"})
     */
    private $active;

    /**
     * @ORM\OneToMany(targetEntity=Season::class, mappedBy="course", orphanRemoval=true, cascade={"persist"})
     *
     * @ORM\OrderBy({"sort" = "ASC"})
     *
     * @Groups({"detail"})
     */
    private $seasons;

    /**
     * @ORM\OneToMany(targetEntity=ForumPost::class, mappedBy="course")
     *
     * @OrderBy({"createdAt" = "DESC"})
     *
     * @Groups({"forum"})
     */
    private $forumPosts;

    /**
     * @ORM\ManyToMany(targetEntity=NpsQuestion::class, mappedBy="course")
     */
    private $npsQuestions;

    /**
     * @ORM\ManyToMany(targetEntity=User::class, inversedBy="managedCourses")
     *
     * @ORM\JoinTable(name="course_manager",
     *      joinColumns={@ORM\JoinColumn(name="course_id", referencedColumnName="id")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id")}
     *      )
     */
    private $managers;

    /**
     * @ORM\ManyToOne(targetEntity=CourseLevel::class, inversedBy="courses")
     *
     * @Groups({"list", "update-course"})
     */
    private $level;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"detail", "update-course"})
     */
    private $documentation;

    /**
     * @ORM\ManyToMany(targetEntity=Tag::class, inversedBy="courses",cascade={"persist"})
     *
     * @Groups({"list", "update-course"})
     */
    private $tags;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     *
     * @Groups({"update-course"})
     */
    private $open_visible;

    /**
     * @ORM\ManyToMany(targetEntity=Filter::class, inversedBy="courses")
     *
     * @ORM\JoinTable(name="course_filter",
     *      joinColumns={@ORM\JoinColumn(name="course_id", referencedColumnName="id")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="filter_id", referencedColumnName="id")}
     *      )
     */
    private $filters;

    /**
     * @ORM\ManyToMany(targetEntity=CourseSegment::class, mappedBy="Segment",cascade={"persist"})
     *
     * @Groups({"list", "update-course"})
     */
    private $courseSegments;

    /**
     * @ORM\OneToMany(targetEntity=ItineraryCourse::class, mappedBy="course",cascade={"remove"})
     */
    private $itineraryCourses;

    /**
     * @ORM\Column(type="boolean")
     *
     * @Groups({"list", "update-course"})
     */
    private $isNew;

    /**
     * @ORM\OneToMany(targetEntity=MaterialCourse::class, mappedBy="course")
     */
    private $materialCourses;

    /**
     * @ORM\OneToMany(targetEntity=MaterialDownloadHistory::class, mappedBy="course")
     */
    private $materialDownloadHistories;

    /**
     * @ORM\OneToMany(targetEntity=HistorySeenMaterial::class, mappedBy="Course")
     */
    private $historySeenMaterials;

    /**
     * @ORM\OneToMany(targetEntity=TaskCourse::class, mappedBy="course")
     */
    private $taskCourses;

    /**
     * @ORM\ManyToOne(targetEntity=TypeCourse::class)
     *
     * @ORM\JoinColumn(nullable=true)
     *
     * @Groups({"update-course"})
     */
    private $typeCourse;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private $newAt;

    /**
     * @ORM\OneToOne(targetEntity=MainCourseEvaluation::class, mappedBy="course", cascade={"persist", "remove"})
     */
    private $mainCourseEvaluation;

    /**
     * @ORM\OneToMany(targetEntity=SurveyCourse::class, mappedBy="course", cascade={"persist"}, orphanRemoval=true)
     */
    private $surveyCourses;

    /**
     * @ORM\OneToMany(targetEntity=UserTime::class, mappedBy="course")
     */
    private $userTimes;

    /**
     * @ORM\OneToMany(targetEntity=UserHistoryDownloadDiploma::class, mappedBy="course")
     */
    private $userHistoryDownloadDiplomas;

    /**
     * @ORM\OneToMany(targetEntity=CourseStat::class, mappedBy="course")
     */
    private $courseStat;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $sort = 0;

    /**
     * @ORM\ManyToOne(targetEntity=TypeDiploma::class, inversedBy="courses")
     *
     * @ORM\JoinColumn(nullable=true)
     *
     * @Groups({"update-course"})
     */
    private $typeDiploma;

    /**
     * @ORM\Column(type="boolean")
     *
     * @Groups({"update-course"})
     */
    private $is_content_diploma;

    /**
     * @ORM\Column(type="string", length=8, nullable=true)
     *
     * @Groups({"update-course"})
     */
    private $type_index_diploma;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"update-course"})
     */
    private $description_content_diploma;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"update-course"})
     */
    private ?int $duration = null;

    /**
     * @ORM\Column(type="json", nullable=false)
     *
     * @Groups({"update-course"})
     *
     * @SerializedName("diploma_config")
     */
    private array $diplomaConfig = [];

    public function __construct()
    {
        $this->announcements = new ArrayCollection();
        $this->chapters = new ArrayCollection();
        $this->categories = new ArrayCollection();
        $this->translations = new ArrayCollection();
        $this->seasons = new ArrayCollection();
        $this->forumPosts = new ArrayCollection();

        $this->npsQuestions = new ArrayCollection();
        $this->managers = new ArrayCollection();
        $this->tags = new ArrayCollection();
        $this->filters = new ArrayCollection();
        $this->courseSegments = new ArrayCollection();
        $this->itineraryCourses = new ArrayCollection();
        $this->materialCourses = new ArrayCollection();
        $this->materialDownloadHistories = new ArrayCollection();
        $this->historySeenMaterials = new ArrayCollection();
        $this->taskCourses = new ArrayCollection();
        $this->surveyCourses = new ArrayCollection();
        $this->userTimes = new ArrayCollection();
        $this->userHistoryDownloadDiplomas = new ArrayCollection();
        $this->courseStat = new ArrayCollection();
        $this->is_content_diploma = false;
        $this->open = false;
        $this->isNew = false;
        $this->open_visible = false;
    }

    public function __toString()
    {
        return $this->code . ' - ' . $this->name;
    }

    public function getCodeAndName(): string
    {
        return $this->getCode() . ' - ' . $this->getName();
    }

    public function __clone()
    {
        $this->id = null;

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }

        // clone chapters without seasson
        $chapters = $this->getChapters();
        $this->chapters = new ArrayCollection();
        if (\count($chapters)) {
            foreach ($chapters as $chapter) {
                if ($chapter->getSeason()) {
                    continue;
                }
                $clonedChapter = clone $chapter;
                $this->addChapter($clonedChapter);
            }
        }

        // clone seasons and chapters in eachseason
        $seasons = $this->getSeasons();
        $this->seasons = new ArrayCollection();
        $this->chapters = new ArrayCollection();
        if (\count($seasons)) {
            foreach ($seasons as $season) {
                $clonedSeason = clone $season;
                $this->addSeason($clonedSeason);

                $chapters = $season->getChapters();
                foreach ($chapters as $chapter) {
                    $clonedChapter = clone $chapter;
                    $clonedChapter->setSeason($clonedSeason);
                    $this->addChapter($clonedChapter);
                }
            }
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return Collection|Announcement[]
     */
    public function getAnnouncements(): Collection
    {
        return $this->announcements;
    }

    public function addAnnouncement(Announcement $announcement): self
    {
        if (!$this->announcements->contains($announcement)) {
            $this->announcements[] = $announcement;
            $announcement->setCourse($this);
        }

        return $this;
    }

    public function removeAnnouncement(Announcement $announcement): self
    {
        if ($this->announcements->contains($announcement)) {
            $this->announcements->removeElement($announcement);
            // set the owning side to null (unless already changed)
            if ($announcement->getCourse() === $this) {
                $announcement->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Chapter[]
     */
    public function getChapters(): Collection
    {
        return $this->chapters->filter(function (Chapter $chapter) {
            return $chapter->isIsActive() && null !== $chapter->getSeason();
        });
    }

    public function addChapter(Chapter $chapter): self
    {
        if (!$this->chapters->contains($chapter)) {
            $this->chapters[] = $chapter;
            $chapter->setCourse($this);
        }

        return $this;
    }

    public function removeChapter(Chapter $chapter): self
    {
        if ($this->chapters->contains($chapter)) {
            $this->chapters->removeElement($chapter);
            // set the owning side to null (unless already changed)
            if ($chapter->getCourse() === $this) {
                $chapter->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ProfessionalCategory[]
     */
    public function getCategories(): Collection
    {
        return $this->categories;
    }

    public function setCategories($categories): self
    {
        $oldCategories = $this->getCategories();
        foreach ($oldCategories as $o) {
            if (!\in_array($o, $categories)) {
                $this->removeCategory($o);
            }
        }

        foreach ($categories as $cat) {
            $this->addCategory($cat);
        }

        return $this;
    }

    public function addCategory(ProfessionalCategory $category): self
    {
        if (!$this->categories->contains($category)) {
            $this->categories[] = $category;
        }

        return $this;
    }

    public function removeCategory(ProfessionalCategory $category): self
    {
        if ($this->categories->contains($category)) {
            $this->categories->removeElement($category);
        }

        return $this;
    }

    public function getOpen(): ?bool
    {
        return $this->open;
    }

    public function setOpen(bool $open): self
    {
        $this->open = $open;

        return $this;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    public function getTranslation(): ?self
    {
        return $this->translation;
    }

    public function setTranslation(?self $translation): self
    {
        $this->translation = $translation;

        return $this;
    }

    /**
     * @return Collection|self[]
     */
    public function getTranslations(): Collection
    {
        return $this->translations;
    }

    public function addTranslation(self $translation): self
    {
        if (!$this->translations->contains($translation)) {
            $this->translations[] = $translation;
            $translation->setTranslation($this);
        }

        return $this;
    }

    public function removeTranslation(self $translation): self
    {
        if ($this->translations->removeElement($translation)) {
            // set the owning side to null (unless already changed)
            if ($translation->getTranslation() === $this) {
                $translation->setTranslation(null);
            }
        }

        return $this;
    }

    public function getTranslationByLocale(string $locale): ?Course
    {
        foreach ($this->getTranslations() as $translation) {
            if ($translation->getLocale() == $locale) {
                return $translation;
            }
        }

        return null;
    }

    public function getCategoryTranslated(self $translation): ?string
    {
        /** @var CourseCategory $courseCategory */
        $courseCategory = $this->getCategory();
        /** @var CourseCategoryTranslation $categoryTranslation */
        $categoryTranslation = $courseCategory->translate($translation->getLocale());

        return $categoryTranslation->getName();
    }

    public function getPoints(): ?int
    {
        return $this->points ?? self::MAX_POINTS;
    }

    public function setPoints(?int $points): self
    {
        $this->points = $points ?? 0;

        return $this;
    }

    public function gamesMaxPoints(): float|int
    {
        $numberOfGames = 0;
        foreach ($this->getChapters() as $chapter) {
            if ($chapter->getType()->isGame()) {
                ++$numberOfGames;
            }
        }

        if (0 == $this->getPoints() || 0 == $numberOfGames) {
            return 0;
        }

        if ($this->hasChapterContent()) {
            return round(($this->getPoints() * self::GAME_POINT_PERCENTAGE) / $numberOfGames);
        }

        return round($this->getPoints() / $numberOfGames);
    }

    public function contentChaptersMaxPoints(): float|int
    {
        if ($this->hasChapterGame()) {
            return round($this->getPoints() * self::CONTENT_POINT_PERCENTAGE);
        }

        return $this->getPoints();
    }

    public function getFinishingPoints(): float
    {
        return $this->points * (1 - self::GAME_POINT_PERCENTAGE);
    }

    public function getCategory(): ?CourseCategory
    {
        return $this->category;
    }

    public function getIdCategory()
    {
        return $this->category;
    }

    public function setCategory(?CourseCategory $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getGeneralInformation(): ?string
    {
        return $this->generalInformation;
    }

    public function setGeneralInformation(?string $generalInformation): self
    {
        $this->generalInformation = $generalInformation;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @return Collection|Season[]
     */
    public function getSeasons(): Collection
    {
        return $this->seasons;
    }

    public function addSeason(Season $season): self
    {
        if (!$this->seasons->contains($season)) {
            $this->seasons[] = $season;
            $season->setCourse($this);
        }

        return $this;
    }

    public function removeSeason(Season $season): self
    {
        if ($this->seasons->removeElement($season)) {
            // set the owning side to null (unless already changed)
            if ($season->getCourse() === $this) {
                $season->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ForumPost[]
     */
    public function getForumPosts(): Collection
    {
        return $this->forumPosts;
    }

    public function addForumPost(ForumPost $forumPost): self
    {
        if (!$this->forumPosts->contains($forumPost)) {
            $this->forumPosts[] = $forumPost;
            $forumPost->setCourse($this);
        }

        return $this;
    }

    public function removeForumPost(ForumPost $forumPost): self
    {
        if ($this->forumPosts->contains($forumPost)) {
            $this->forumPosts->removeElement($forumPost);
            // set the owning side to null (unless already changed)
            if ($forumPost->getCourse() === $this) {
                $forumPost->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|NpsQuestion[]
     */
    public function getNpsQuestions(): Collection
    {
        return $this->npsQuestions;
    }

    public function addNpsQuestion(NpsQuestion $npsQuestion): self
    {
        if (!$this->npsQuestions->contains($npsQuestion)) {
            $this->npsQuestions[] = $npsQuestion;
            $npsQuestion->addCourse($this);
        }

        return $this;
    }

    public function removeNpsQuestion(NpsQuestion $npsQuestion): self
    {
        if ($this->npsQuestions->removeElement($npsQuestion)) {
            $npsQuestion->removeCourse($this);
        }

        return $this;
    }

    public function getLanguage($language)
    {
        $locale = '';
        if ('es' == $language) {
            $locale = 'Spanish';
        } elseif ('en' == $language) {
            $locale = 'English';
        } elseif ('pt' == $language) {
            $locale = 'Portuguese';
        } elseif ('fr' == $language) {
            $locale = 'Francés';
        } elseif ('uk' == $language) {
            $locale = 'Ucraniano';
        } elseif ('ro' == $language) {
            $locale = 'Rumano';
        } elseif ('pl' == $language) {
            $locale = 'Polaco';
        } elseif ('de' == $language) {
            $locale = 'Alemán';
        } elseif ('fi' == $language) {
            $locale = 'Finés';
        } elseif ('ca' == $language) {
            $locale = 'Catalán';
        } elseif ('eu' == $language) {
            $locale = 'Euskera';
        } elseif ('it' == $language) {
            $locale = 'Italiano';
        }

        return $locale;
    }

    /**
     * @return Collection|User[]
     */
    public function getManagers(): Collection
    {
        return $this->managers;
    }

    public function setManagers($managers): self
    {
        $this->managers = $managers;

        return $this;
    }

    public function addManager(User $manager): self
    {
        if (!$this->managers->contains($manager)) {
            $this->managers[] = $manager;
        }

        return $this;
    }

    public function removeManager(User $manager): self
    {
        $this->managers->removeElement($manager);

        return $this;
    }

    public function getDocumentation(): ?string
    {
        return $this->documentation;
    }

    public function setDocumentation(?string $documentation): self
    {
        $this->documentation = $documentation;

        return $this;
    }

    public function getLevel(): ?CourseLevel
    {
        return $this->level;
    }

    public function setLevel(?CourseLevel $level): self
    {
        $this->level = $level;

        return $this;
    }

    public function isManager(UserInterface $user)
    {
        foreach ($this->getManagers() as $manager) {
            if ($manager->getId() == $user->getId()) {
                return true;
            }
        }

        return false;
    }

    public function setTags($tags): self
    {
        $this->tags = $tags;

        return $this;
    }

    /**
     * @return Collection|Tag[]
     */
    public function getTags(): Collection
    {
        return $this->tags;
    }

    public function addTag(Tag $tag): self
    {
        if (!$this->tags->contains($tag)) {
            $this->tags[] = $tag;
        }

        return $this;
    }

    public function removeTag(Tag $tag): self
    {
        $this->tags->removeElement($tag);

        return $this;
    }

    public function getOpenVisible(): ?bool
    {
        return $this->open_visible;
    }

    public function setOpenVisible(?bool $open_visible): self
    {
        $this->open_visible = $open_visible;

        return $this;
    }

    public function setFilters($filters): self
    {
        $this->filters = $filters;

        return $this;
    }

    /**
     * @return Collection|Filter[]
     */
    public function getFilters(): Collection
    {
        return $this->filters;
    }

    public function addFilter(Filter $filter): self
    {
        if (!$this->filters->contains($filter)) {
            $this->filters[] = $filter;
        }

        return $this;
    }

    public function removeFilter(Filter $filter, $idFilterCategory = null): self
    {
        if ($idFilterCategory) {
            if ($filter->getFilterCategory()->getId() === $idFilterCategory) {
                $this->filters->removeElement($filter);
            }
        } else {
            $this->filters->removeElement($filter);
        }

        return $this;
    }

    public function checkUserFilterAccess(User $user)
    {
        $filters = $this->getFilters();

        $differentCategories = [];

        foreach ($filters as $filter) {
            if (!\in_array($filter->getFilterCategory(), $differentCategories)) {
                array_push($differentCategories, $filter->getFilterCategory());
            }
        }

        $userFilters = $user->getFilter();

        $countFilters = 0;
        foreach ($userFilters as $filter) {
            if ($filters->contains($filter)) {
                ++$countFilters;
            }
        }

        return \count($differentCategories) <= $countFilters;
    }

    /**
     * @return Collection|CourseSegment[]
     */
    public function getCourseSegments(): Collection
    {
        return $this->courseSegments;
    }

    public function addCourseSegment(CourseSegment $courseSegment): self
    {
        if (!$this->courseSegments->contains($courseSegment)) {
            $this->courseSegments[] = $courseSegment;
            $courseSegment->addSegment($this);
        }

        return $this;
    }

    public function removeCourseSegment(CourseSegment $courseSegment): self
    {
        if ($this->courseSegments->removeElement($courseSegment)) {
            $courseSegment->removeSegment($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, ItineraryCourse>
     */
    public function getItineraryCourses(): Collection
    {
        return $this->itineraryCourses;
    }

    public function addItineraryCourse(ItineraryCourse $itineraryCourse): self
    {
        if (!$this->itineraryCourses->contains($itineraryCourse)) {
            $this->itineraryCourses[] = $itineraryCourse;
            $itineraryCourse->setCourse($this);
        }

        return $this;
    }

    public function removeItineraryCourse(ItineraryCourse $itineraryCourse): self
    {
        if ($this->itineraryCourses->removeElement($itineraryCourse)) {
            // set the owning side to null (unless already changed)
            if ($itineraryCourse->getCourse() === $this) {
                $itineraryCourse->setCourse(null);
            }
        }

        return $this;
    }

    public function isNew(): ?bool
    {
        return $this->isNew;
    }

    /**
     * @return Collection<int, MaterialCourse>
     */
    public function getMaterialCourses(): Collection
    {
        return $this->materialCourses;
    }

    public function addMaterialCourse(MaterialCourse $materialCourse): self
    {
        if (!$this->materialCourses->contains($materialCourse)) {
            $this->materialCourses[] = $materialCourse;
            $materialCourse->setCourse($this);
        }

        return $this;
    }

    public function removeMaterialCourse(MaterialCourse $materialCourse): self
    {
        if ($this->materialCourses->removeElement($materialCourse)) {
            // set the owning side to null (unless already changed)
            if ($materialCourse->getCourse() === $this) {
                $materialCourse->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, MaterialDownloadHistory>
     */
    public function getMaterialDownloadHistories(): Collection
    {
        return $this->materialDownloadHistories;
    }

    public function addMaterialDownloadHistory(MaterialDownloadHistory $materialDownloadHistory): self
    {
        if (!$this->materialDownloadHistories->contains($materialDownloadHistory)) {
            $this->materialDownloadHistories[] = $materialDownloadHistory;
            $materialDownloadHistory->setCourse($this);
        }

        return $this;
    }

    public function removeMaterialDownloadHistory(MaterialDownloadHistory $materialDownloadHistory): self
    {
        if ($this->materialDownloadHistories->removeElement($materialDownloadHistory)) {
            // set the owning side to null (unless already changed)
            if ($materialDownloadHistory->getCourse() === $this) {
                $materialDownloadHistory->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, HistorySeenMaterial>
     */
    public function getHistorySeenMaterials(): Collection
    {
        return $this->historySeenMaterials;
    }

    public function addHistorySeenMaterial(HistorySeenMaterial $historySeenMaterial): self
    {
        if (!$this->historySeenMaterials->contains($historySeenMaterial)) {
            $this->historySeenMaterials[] = $historySeenMaterial;
            $historySeenMaterial->setCourse($this);
        }

        return $this;
    }

    public function removeHistorySeenMaterial(HistorySeenMaterial $historySeenMaterial): self
    {
        if ($this->historySeenMaterials->removeElement($historySeenMaterial)) {
            // set the owning side to null (unless already changed)
            if ($historySeenMaterial->getCourse() === $this) {
                $historySeenMaterial->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, TaskCourse>
     */
    public function getTaskCourses(): Collection
    {
        return $this->taskCourses;
    }

    public function addTaskCourse(TaskCourse $taskCourse): self
    {
        if (!$this->taskCourses->contains($taskCourse)) {
            $this->taskCourses[] = $taskCourse;
            $taskCourse->setCourse($this);
        }

        return $this;
    }

    public function removeTaskCourse(TaskCourse $taskCourse): self
    {
        if ($this->taskCourses->removeElement($taskCourse)) {
            // set the owning side to null (unless already changed)
            if ($taskCourse->getCourse() === $this) {
                $taskCourse->setCourse(null);
            }
        }

        return $this;
    }

    public function allChapterPendient()
    {
        $chapterPendient = [];
        foreach ($this->getChapters() as $chapter) {
            if (false == $chapter->hasContentCompleted()) {
                array_push($chapterPendient, $chapter->hasContentCompleted());
            }
        }

        return \count($chapterPendient);
    }

    public function isCompletedContentChapter()
    {
        $isCompleted = false;
        if (\count($this->getChapters()) > 0 && 0 == $this->allChapterPendient()) {
            $isCompleted = true;
        }

        return $isCompleted;
    }

    public function hasACompleteChapter()
    {
        $hasACompleteChapter = false;
        foreach ($this->getChapters() as $chapter) {
            if ($chapter->hasContentCompleted()) {
                $hasACompleteChapter = true;
                break;
            }
        }

        return $hasACompleteChapter;
    }

    public function getTotalChapter()
    {
        $totalChapter = 0;
        foreach ($this->getChapters() as $chapter) {
            if ($chapter->isIsActive()) {
                ++$totalChapter;
            }
        }

        return $totalChapter;
    }

    public function hasTranslation($locale): bool
    {
        if ($this->getLocale() == $locale) {
            return true;
        }

        return $this->translations->exists(function ($key, Course $element) use ($locale) {
            return $element->getLocale() === $locale;
        });
    }

    public function getTypeCourse(): ?TypeCourse
    {
        return $this->typeCourse;
    }

    public function setTypeCourse(?TypeCourse $typeCourse): self
    {
        $this->typeCourse = $typeCourse;

        return $this;
    }

    public function getNewAt(): ?\DateTimeImmutable
    {
        return $this->newAt;
    }

    public function setNewAt(?\DateTimeImmutable $newAt): self
    {
        $this->newAt = $newAt;

        return $this;
    }

    public function getMainCourseEvaluation(): ?MainCourseEvaluation
    {
        return $this->mainCourseEvaluation;
    }

    public function setMainCourseEvaluation(?MainCourseEvaluation $mainCourseEvaluation): self
    {
        // unset the owning side of the relation if necessary
        if (null === $mainCourseEvaluation && null !== $this->mainCourseEvaluation) {
            $this->mainCourseEvaluation->setCourse(null);
        }

        // set the owning side of the relation if necessary
        if (null !== $mainCourseEvaluation && $mainCourseEvaluation->getCourse() !== $this) {
            $mainCourseEvaluation->setCourse($this);
        }

        $this->mainCourseEvaluation = $mainCourseEvaluation;

        return $this;
    }

    /**
     * @Groups({"detail"})
     *
     * @return bool
     */
    public function gethasOwnQuestions()
    {
        return $this->getMainCourseEvaluation() && $this->getMainCourseEvaluation()->isIsMainNps() ? true : false;
    }

    /**
     * @return Collection<int, SurveyCourse>
     */
    public function getSurveyCourses(): Collection
    {
        return $this->surveyCourses;
    }

    public function addSurveyCourse(SurveyCourse $surveyCourse): self
    {
        if (!$this->surveyCourses->contains($surveyCourse)) {
            $this->surveyCourses[] = $surveyCourse;
            $surveyCourse->setCourse($this);
        }

        return $this;
    }

    public function removeSurveyCourse(SurveyCourse $surveyCourse): self
    {
        if ($this->surveyCourses->removeElement($surveyCourse)) {
            // set the owning side to null (unless already changed)
            if ($surveyCourse->getCourse() === $this) {
                $surveyCourse->setCourse(null);
            }
        }

        return $this;
    }

    public function isCompleted(): bool
    {
        $chapters = $this->getChapters();
        foreach ($chapters as $chapter) {
            $chapterType = $chapter->getType();
            if ($chapterType->isActive()) {
                $result = $chapter->hasContentCompleted();
                if (!$result) {
                    return false;
                }
            }
        }

        if (0 == \count($chapters) && !$this->isPresencialOrAulaVirtual()) {
            return false;
        }

        return true;
    }

    private function isPresencialOrAulaVirtual()
    {
        if (!$this->typeCourse) {
            return false;
        }

        return (
            TypeCourse::TYPE_PRESENCIAL == $this->typeCourse->getId())
            || (
                TypeCourse::TYPE_AULA_VIRTUAL == $this->typeCourse->getId()
            );
    }

    public function hasChapterGame()
    {
        $chapters = $this->getChapters();
        foreach ($chapters as $chapter) {
            $chapterType = $chapter->getType();
            if ($chapterType->isGame()) {
                return true;
            }
        }

        return false;
    }

    public function hasChapterContent(): bool
    {
        $chapters = $this->getChapters();
        foreach ($chapters as $chapter) {
            $chapterType = $chapter->getType();
            if ($chapterType->isContent()) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return Collection<int, UserTime>
     */
    public function getUserTimes(): Collection
    {
        return $this->userTimes;
    }

    public function addUserTime(UserTime $userTime): self
    {
        if (!$this->userTimes->contains($userTime)) {
            $this->userTimes[] = $userTime;
            $userTime->setCourse($this);
        }

        return $this;
    }

    public function removeUserTime(UserTime $userTime): self
    {
        if ($this->userTimes->removeElement($userTime)) {
            // set the owning side to null (unless already changed)
            if ($userTime->getCourse() === $this) {
                $userTime->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, UserHistoryDownloadDiploma>
     */
    public function getUserHistoryDownloadDiplomas(): Collection
    {
        return $this->userHistoryDownloadDiplomas;
    }

    public function addUserHistoryDownloadDiploma(UserHistoryDownloadDiploma $userHistoryDownloadDiploma): self
    {
        if (!$this->userHistoryDownloadDiplomas->contains($userHistoryDownloadDiploma)) {
            $this->userHistoryDownloadDiplomas[] = $userHistoryDownloadDiploma;
            $userHistoryDownloadDiploma->setCourse($this);
        }

        return $this;
    }

    public function removeUserHistoryDownloadDiploma(UserHistoryDownloadDiploma $userHistoryDownloadDiploma): self
    {
        if ($this->userHistoryDownloadDiplomas->removeElement($userHistoryDownloadDiploma)) {
            // set the owning side to null (unless already changed)
            if ($userHistoryDownloadDiploma->getCourse() === $this) {
                $userHistoryDownloadDiploma->setCourse(null);
            }
        }

        return $this;
    }

    /**
     * Get the value of isNew.
     */
    public function getIsNew()
    {
        return $this->isNew;
    }

    /**
     * Set the value of isNew.
     *
     * @return self
     */
    public function setIsNew($isNew)
    {
        $this->isNew = $isNew;

        return $this;
    }

    public function getThumbnailUrl()
    {
        return $this->thumbnailUrl ?
            $this->thumbnailUrl
            : '/assets_announcement/types_courses_image/' . $this->getTypeCourse()->getImageByType() . '.png';
    }

    public function getImage()
    {
        return $this->image ? $this->image : $this->getTypeCourse()->getImageByType() . '.png';
    }

    /**
     * @return Collection<int, CourseStat>
     */
    public function getCourseStat(): Collection
    {
        return $this->courseStat;
    }

    public function addCourseStat(CourseStat $courseStat): self
    {
        if (!$this->courseStat->contains($courseStat)) {
            $this->courseStat[] = $courseStat;
            $courseStat->setCourse($this);
        }

        return $this;
    }

    public function removeCourseStat(CourseStat $courseStat): self
    {
        if ($this->courseStat->removeElement($courseStat)) {
            // set the owning side to null (unless already changed)
            if ($courseStat->getCourse() === $this) {
                $courseStat->setCourse(null);
            }
        }

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }

    public function getTypeDiploma(): ?TypeDiploma
    {
        return $this->typeDiploma;
    }

    public function setTypeDiploma(?TypeDiploma $typeDiploma): self
    {
        $this->typeDiploma = $typeDiploma;

        return $this;
    }

    public function isIsContentDiploma(): ?bool
    {
        return $this->is_content_diploma;
    }

    public function setIsContentDiploma(bool $is_content_diploma): self
    {
        $this->is_content_diploma = $is_content_diploma;

        return $this;
    }

    public function getTypeIndexDiploma(): ?string
    {
        return $this->type_index_diploma;
    }

    public function setTypeIndexDiploma(?string $type_index_diploma): self
    {
        $this->type_index_diploma = $type_index_diploma;

        return $this;
    }

    public function getDescriptionContentDiploma(): ?string
    {
        return $this->description_content_diploma;
    }

    public function setDescriptionContentDiploma(?string $description_content_diploma): self
    {
        $this->description_content_diploma = $description_content_diploma;

        return $this;
    }

    public function getDuration(): ?int
    {
        return $this->duration;
    }

    public function setDuration(?int $duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    public function getDiplomaConfig(): DiplomaConfig
    {
        return DiplomaConfig::fromArray($this->diplomaConfig);
    }

    public function setDiplomaConfig(DiplomaConfig $diplomaConfig): self
    {
        $this->diplomaConfig = $diplomaConfig->toArray();

        return $this;
    }
}
