<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\ExportRepository;
use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Ged<PERSON>;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @ORM\Entity(repositoryClass=ExportRepository::class)
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class Export
{
    use AtAndBy;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $finished_at;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $available_until;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $filename;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $meta = [];

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $task_id;

    /**
     * @ORM\OneToOne(targetEntity=Task::class, cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=true)
     */
    private $task;

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $type;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getFinishedAt(): ?\DateTime
    {
        return $this->finished_at;
    }

    public function setFinishedAt($finished_at): self
    {
        $this->finished_at = $finished_at;

        return $this;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setFilename(?string $filename): self
    {
        if (null === $filename) {
            return $this;
        }

        $this->filename = str_replace(['/', '\\', ':', '*', '?', '«', '<', '>', '|'], '-', $filename);

        return $this;
    }

    public function getMeta(): array
    {
        return $this->meta;
    }

    public function setMeta(?array $meta): self
    {
        $this->meta = $meta;

        return $this;
    }

    public function getTaskId(): ?int
    {
        return $this->task_id;
    }

    public function setTaskId(int $task_id): self
    {
        $this->task_id = $task_id;

        return $this;
    }

    public function getAvailableUntil(): ?\DateTime
    {
        return $this->available_until;
    }

    public function setAvailableUntil(\DateTime $available_until): self
    {
        $this->available_until = $available_until;

        return $this;
    }

    public function setCreate(?User $user)
    {
        $this->setCreatedAt(new \DateTime());
        $this->setCreatedBy($user);
    }

    public function setUpdate(?User $user)
    {
        $this->setUpdatedAt(new \DateTime());
        $this->setUpdatedBy($user);
    }

    public function setDelete(?User $user)
    {
        $this->setDeletedAt(new \DateTime());
        $this->setDeletedBy($user);
    }

    public function getTask(): ?Task
    {
        return $this->task;
    }

    public function setTask(Task $task): self
    {
        $this->task = $task;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }
}
