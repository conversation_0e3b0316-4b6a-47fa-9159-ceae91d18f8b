<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report;

use App\Entity\Announcement;
use App\Entity\User;
use App\Service\Diploma\DiplomaService;
use App\Utils\SpreadsheetUtil;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

/**
 * Abstract class that defines the general flow of the report
 * and common methods. Subclasses (ReportOnlineService, ReportOnSiteService)
 * will implement or override the abstract methods to provide
 * specific headers and data.
 */
abstract class AbstractReport
{
    protected AnnouncementContainer $announcementContainer;
    protected EntityManagerInterface $em;
    protected DiplomaService $diplomaUserService;
    protected AnnouncementReportDataService $announcementReportDataService;
    protected AnnouncementReportConstants $announcementReportConstants;
    protected LoggerInterface $logger;

    /**
     * Configurations.
     */
    protected bool $confGroupInfo = false;
    protected bool $confForum = false;
    protected bool $confChat = false;
    protected bool $confVideoConference = false;
    protected bool $confConnections = false;
    protected bool $confActivities = false;
    protected bool $confNotifications = false;
    protected bool $confMaterials = false;
    protected bool $confTasks = false;
    protected bool $confAssistance = false;
    protected bool $confSurvey = false;
    protected bool $confResumeIndividual = false;
    protected int $userId = 0;

    private string $baseDir;
    /** Directory */
    protected string $announcementDir;

    public function __construct(
        EntityManagerInterface $em,
        KernelInterface $kernel,
        AnnouncementReportDataService $announcementReportDataService,
        DiplomaService $diplomaUserService,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger
    ) {
        $this->em = $em;
        $this->announcementReportDataService = $announcementReportDataService;
        $this->baseDir = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'announcements';
        if (!file_exists($this->baseDir)) {
            mkdir($this->baseDir, 0777, true);
        }
        $this->diplomaUserService = $diplomaUserService;
        $this->announcementReportConstants = $announcementReportConstants;
        $this->logger = $logger;
    }

    /**
     * Initializes the base data.
     */
    protected function initData($announcement, User $user, bool $all = false, array $groups = [])
    {
        if (!($announcement instanceof Announcement)) {
            $announcement = $this->em->getRepository(Announcement::class)->find($announcement);
        }

        $this->announcementContainer = new AnnouncementContainer(
            $this->em,
            $announcement,
            $user,
            $all,
            $groups
        );

        $this->announcementDir = $this->baseDir . DIRECTORY_SEPARATOR . 'announcement_' . $announcement->getId();
        if (!file_exists($this->announcementDir)) {
            mkdir($this->announcementDir);
        }
    }

    public function initConfiguration(array $configurations = [])
    {
        $this->confGroupInfo = $configurations['groupInfo'] ?? false;
        $this->confChat = $configurations['chat'] ?? false;
        $this->confForum = $configurations['forum'] ?? false;
        $this->confVideoConference = $configurations['videoConference'] ?? false;
        $this->confConnections = $configurations['connections'] ?? false;
        $this->confActivities = $configurations['activities'] ?? false;
        $this->confNotifications = $configurations['notifications'] ?? false;
        $this->confMaterials = $configurations['materials'] ?? false;
        $this->confTasks = $configurations['tasks'] ?? false;
        $this->confAssistance = $configurations['assistance'] ?? false;
        $this->confSurvey = $configurations['survey'] ?? false;
        $this->confResumeIndividual = $configurations['resumeIndividual'] ?? false;
        $this->userId = $configurations['userId'] ?? 0;
    }

    /**
     * Main entry point:
     *
     * @param int|Announcement $announcement
     */
    public function init($announcement, User $user, bool $all = false, array $groups = [], array $configurations = [])
    {
        $this->initData($announcement, $user, $all, $groups);
        $this->initConfiguration($configurations);
    }

    protected function initSheet(
        SpreadsheetUtil $report,
        string $sheetTitle,
        array $headers,
        array $colorSequence = AnnouncementReportConstants::HEADER_COLOR_SEQUENCE,
        bool $autosize = true
    ) {
        $report->addSheet($sheetTitle);

        $fill = [];
        $column = 'A';
        foreach ($headers as $index => $header) {
            $color = $colorSequence[$index % \count($colorSequence)];
            $fill[$column] = $color;
            $column = SpreadsheetUtil::getNextColumn($column);
        }

        $report->setHeaders(
            $this->announcementReportConstants->getTranslations($headers),
            $autosize,
            true, // freeze = true
            false, // autoFilter = false
            1, // row = 1
            [
                'fontSize' => 10,
                'bold' => true,
                'color' => '000000',
                'fill' => $fill, // Asignar el arreglo de colores
            ]
        );
    }
}
