<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report;

class AnnouncementReportConstants
{
    public const HEADER_COLOR_SEQUENCE = [
        'FAE4CC',
        'D8E9D2',
        'E9D0DB',
        'F4B16B',
        'B5D6A7',
        'C8D9F6',
        'F4CCCC',
        'A3C1F2',
    ];

    private const NAME_RELATIONS = [
        'GROUP_ID' => 'ID grupo',
        'GROUP_CODE' => 'Código de grupo',
        'ANNOUNCEMENT_NAME' => 'Nombre de convocatoria',
        'COURSE_NAME' => 'Nombre de curso',
        'TOTAL_USERS' => 'Personas totales',
        'TOTAL_ASSISTANCE' => 'Asistencia totales',
        'TOTAL_CONFIRMED' => 'Confirmación totales',
        'CONFIRMED_ASSISTANCE' => 'Asistencia confirmada',
        'N_SESSIONS' => 'N° sesiones',
        'TUTOR_NAME' => 'Nombre tutor/a',
        'TUTOR_EMAIL' => 'Email tutor/a',

        'USER_ID' => 'ID usuario',
        'USER_CODE' => 'Código usuario',
        'USER_NAME' => 'Nombre',
        'USER_LASTNAME' => 'Apellidos',
        'USER_EMAIL' => 'Correo electrónico',
        'ACCOUNT_VALIDATED' => 'Cuenta validada',

        'SESSION_ID' => 'Sesión ID',
        'SESSION_NUMBER' => 'Número de sesión',
        'SESSION_NAME' => 'Nombre sesión',
        'START_AT' => 'Fecha de inicio',
        'ENTRY_MARGIN' => 'Margen de entrada',
        'FINISH_AT' => 'Fecha de fin',
        'EXIT_MARGIN' => 'Margen de salida',
        'STATUS' => 'Estado',
        'PLACE' => 'Lugar',
        'ASSISTANCE_PERCENT' => 'Asistencia',
        'BOOL_QR_CODE' => 'Códio QR',
        'BOOL_SIGNATURE' => 'Parte de firmas',

        'BOOL_ASSISTANCE_USER' => 'Control usuario',
        'PERCENT_ASSISTANCE_TUTOR' => 'Control tutor',
        'BOOL_DIGITAL_SIGNATURE' => 'Firma digital',
        'SIGNATURE_IMAGE' => 'Imagen',

        'TUTOR_ID' => 'ID tutor/a',
        'N_CONNECTIONS' => 'N° de conexiones',

        'CONNECTION_ID' => 'ID Conexión',
        'CONNECTION_DATE' => 'Fecha de la conexión',
        'IP' => 'IP',
        'CONNECTION_TIME' => 'Tiempo de conexión',
        'CONNECTION_START_AT' => 'Inicio de conexión',
        'CONNECTION_FINISH_AT' => 'Fin de conexión',

        'QUESTION_ID' => 'ID pregunta',
        'QUESTION_QUESTION' => 'Enunciado',
        'QUESTION_TYPE' => 'Tipo',
        'QUESTION_ANSWER' => 'Respuesta',
        'QUESTION_ANSWER_DATE' => 'Fecha de apertura',

        'SURVEY_ID' => 'ID encuesta',
        'SURVEY_NAME' => 'Nombre de encuesta',
        'N_QUESTIONS' => 'Número de preguntas',
        'CREATED_BY' => 'Creado por',
        'ACTIVE' => 'Activada',
        'PARTICIPANT_PERCENT' => '% de participación',

        'MESSAGE_ID' => 'ID mensaje',
        'MESSAGE_DATE' => 'Registro del mensaje',
        'MESSAGE' => 'Mensaje',
        'REPLY_TO' => 'Responde a',
        'CHAT_GROUP_ID' => 'ID chat grupal',
        'CHAT_GROUP_CODE' => 'Código del chat grupal',
        'N_CHATS' => 'Chats creados',
        'N_USERS' => 'Personas totales',
        'N_COMMENTS' => 'Comentarios totales',

        'FORUM_ID' => 'ID foro',
        'FORUM_CODE' => 'Código del foro',
        'N_THREADS' => 'Hilos creados',
        'N_TUTOR_MESSAGES' => 'Intervenciones tutor/a',

        'ACTIVITY_ID' => 'ID Actividad',
        'ACTIVITY_NAME' => 'Nombre de actividad',
        'ACTIVITY_TYPE' => 'Tipo de actividad',
        'TIME_SPENT' => 'Tiempo empleado',

        'ACTIVITY_CLASS' => 'Clase de actividad',
        'OPENING_TIME' => 'Fecha de apertura',
        'CLOSING_TIME' => 'Fecha de cierre',
        'COMPLETION_PERCENTAGE' => '% de finalización',

        'MATERIAL_ID' => 'ID Material',
        'MATERIAL_NAME' => 'Nombre material',
        'MATERIAL_TYPE' => 'Tipo de material',
        'CREATED_AT' => 'Fecha de creación',
        'PUBLISHED' => 'Publicado',
        'DOWNLOADABLE' => 'Descargable',

        // '', '', ''
        'NOTIFICATION_ID' => 'ID notificación',
        'PUBLISH_DATE' => 'Fecha de publicación',
        'MESSAGE_BODY' => 'Cuerpo del mensaje',

        // '', '', '', '', '', ''
        'TASK_ID' => 'ID tarea',
        'TASK_NAME' => 'Nombre tarea',
        'DEADLINE' => 'Fecha límite',
        'ATTACHED_FILES' => 'Archivos adjuntos',

        'AA' => 'AAA',
    ];

    public const ON_SITE_GROUP_INFO_HEADERS = [
        'info' => ['GROUP_ID', 'GROUP_CODE', 'ANNOUNCEMENT_NAME', 'COURSE_NAME', 'TOTAL_USERS', 'TUTOR_NAME', 'TUTOR_EMAIL', 'CONFIRMED_ASSISTANCE', 'N_SESSIONS'],
        'users' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'CONFIRMED_ASSISTANCE', 'ACCOUNT_VALIDATED'],
        'sessions' => ['SESSION_ID', 'SESSION_NUMBER', 'START_AT', 'FINISH_AT', 'ENTRY_MARGIN', 'EXIT_MARGIN', 'PLACE', 'STATUS', 'TOTAL_USERS', 'TOTAL_ASSISTANCE', 'ASSISTANCE_PERCENT'],
    ];

    public const ON_SITE_ASSISTANCE_INFO_HEADERS = [
        'info' => ['GROUP_ID', 'GROUP_CODE', 'ANNOUNCEMENT_NAME', 'TOTAL_USERS', 'N_SESSIONS', 'TUTOR_NAME', 'TUTOR_EMAIL'],
        'users' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL'],
        'sessions' => ['SESSION_ID', 'SESSION_NAME', 'START_AT', 'ENTRY_MARGIN', 'FINISH_AT', 'EXIT_MARGIN', 'STATUS', 'ASSISTANCE_PERCENT', 'BOOL_QR_CODE', 'BOOL_SIGNATURE'],
        'session_n' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'BOOL_ASSISTANCE_USER', 'PERCENT_ASSISTANCE_TUTOR', 'BOOL_DIGITAL_SIGNATURE'],
    ];

    public const CONNECTIONS_HEADERS = [
        'info' => ['TUTOR_ID', 'TUTOR_NAME', 'TUTOR_EMAIL', 'N_CONNECTIONS'],
        'connections' => ['CONNECTION_ID', 'CONNECTION_DATE', 'IP', 'CONNECTION_TIME', 'CONNECTION_START_AT', 'CONNECTION_FINISH_AT'],
    ];

    public const ONLINE_CONNECTIONS_HEADERS = [
        'info' => ['TUTOR_ID', 'TUTOR_NAME', 'TUTOR_EMAIL', 'N_CONNECTIONS'],
        'connections' => ['CONNECTION_ID', 'CONNECTION_DATE', 'IP', 'CONNECTION_TIME', 'CONNECTION_START_AT', 'CONNECTION_FINISH_AT'],
        'participants' => ['CONNECTION_ID', 'CONNECTION_DATE', 'IP', 'CONNECTION_TIME', 'CONNECTION_START_AT', 'CONNECTION_FINISH_AT', 'USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL'],
    ];

    public const ON_SITE_SURVEY_HEADERS = [
        'info' => ['SURVEY_ID', 'SURVEY_NAME', 'N_QUESTIONS', 'CREATED_BY', 'ACTIVE', 'PARTICIPANT_PERCENT'],
        'activity' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'QUESTION_ID', 'QUESTION_QUESTION', 'QUESTION_TYPE', 'QUESTION_ANSWER', 'QUESTION_ANSWER_DATE'],
    ];

    public const ONLINE_SURVEY_HEADERS = [
        'info' => ['SURVEY_ID', 'SURVEY_NAME', 'N_QUESTIONS', 'CREATED_BY', 'ACTIVE', 'PARTICIPANT_PERCENT'],
        'activity' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'QUESTION_ID', 'QUESTION_QUESTION', 'QUESTION_TYPE', 'QUESTION_ANSWER', 'QUESTION_ANSWER_DATE'],
    ];

    public const ONLINE_CHAT_HEADERS = [
        'info' => ['CHAT_GROUP_ID', 'CHAT_GROUP_CODE', 'N_CHATS', 'N_USERS', 'N_COMMENTS', 'N_TUTOR_MESSAGES'],
        'messages_page' => ['MESSAGE_ID', 'USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'MESSAGE_DATE', 'MESSAGE', 'REPLY_TO'],
    ];

    public const ONLINE_FORUM_HEADERS = [
        'info' => ['FORUM_ID', 'FORUM_CODE', 'N_THREADS', 'N_USERS', 'N_COMMENTS', 'N_TUTOR_MESSAGES'],
        'messages_page' => ['MESSAGE_ID', 'USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'MESSAGE_DATE', 'MESSAGE'],
    ];

    public const ONLINE_GROUP_INFO_HEADERS = [
        'info' => ['GROUP_ID', 'GROUP_CODE', 'ANNOUNCEMENT_NAME', 'COURSE_NAME', 'TOTAL_USERS', 'TUTOR_NAME', 'TUTOR_EMAIL'],
        'users' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL'],
    ];

    public const ONLINE_ACTIVITIES_HEADERS = [
        'info' => ['ACTIVITY_ID', 'ACTIVITY_NAME', 'ACTIVITY_TYPE', 'ACTIVITY_CLASS', 'OPENING_TIME', 'CLOSING_TIME', 'COMPLETION_PERCENTAGE', 'TIME_SPENT'],
        'activities' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'ACTIVITY_ID', 'ACTIVITY_NAME', 'ACTIVITY_TYPE', 'ACTIVITY_CLASS',  'START_AT', 'FINISH_AT', 'TIME_SPENT', 'STATUS'],
    ];

    public const ONLINE_MATERIALS_HEADERS = [
        'info' => ['MATERIAL_ID', 'MATERIAL_NAME', 'MATERIAL_TYPE', 'CREATED_AT', 'PUBLISHED', 'DOWNLOADABLE'],
    ];

    public const NOTIFICATIONS_HEADERS = [
        'info' => ['NOTIFICATION_ID', 'PUBLISH_DATE', 'MESSAGE_BODY'],
    ];

    public const TASKS_HEADERS = [
        'info' => ['TASK_ID', 'TASK_NAME', 'START_AT', 'DEADLINE', 'ATTACHED_FILES', 'PUBLISHED'],
        'activity' => ['USER_ID', 'USER_CODE', 'USER_NAME', 'USER_LASTNAME', 'USER_EMAIL', 'TASK_ID', 'TASK_NAME', 'START_AT', 'DEADLINE', 'STATUS'],
    ];

    public function __construct()
    {
    }

    public function getTranslations(array $headers = []): array
    {
        $translations = [];
        foreach ($headers as $header) {
            $translations[] = self::NAME_RELATIONS[$header] ?? $header;
        }

        return $translations;
    }
}
