<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report;

use App\Entity\Announcement;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\ZipFileTask;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\KernelInterface;

class AnnouncementReportService
{
    public const TYPE_GROUP = 't-announcement-g-r';
    public const TYPE_ANNOUNCEMENT = 't-announcement-r';

    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private string $announcementDir;

    private ReportOnSiteService $reportOnSiteService;
    private ReportOnlineService $reportOnlineService;

    public function __construct(
        EntityManagerInterface $em,
        KernelInterface $kernel,
        AnnouncementReportDataService $announcementReportDataService,
        LoggerInterface $logger,
        ReportOnSiteService $reportOnSiteService,
        ReportOnlineService $reportOnlineService
    ) {
        $this->reportOnSiteService = $reportOnSiteService;
        $this->reportOnlineService = $reportOnlineService;

        $this->em = $em;
        $this->announcementDir = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'announcements';
        if (!file_exists($this->announcementDir)) {
            mkdir($this->announcementDir, 0777, true);
        }
        $this->logger = $logger;
    }

    /**
     * @param int|Announcement $announcement
     * @param User             $user         The user requesting the report
     * @param bool             $allGroups    If true, check for user permissions and download only allowed groups
     *
     * @return string|null Returns the directory where all files where saved
     *
     * @throws \Exception
     */
    public function generateReport(
        $announcement,
        User $user,
        bool $allGroups = false,
        array $groups = [],
        array $configurations = []
    ): ?string {
        if (!($announcement instanceof Announcement)) {
            $announcement = $this->em->getRepository(Announcement::class)->find($announcement);
        }

        switch ($announcement->getCourse()->getTypeCourse()->getType()) {
            case TypeCourse::TYPE_NAME_ONLINE:
                $this->reportOnlineService->init(
                    $announcement,
                    $user,
                    $allGroups,
                    $groups,
                    $configurations['params'] ?? $configurations
                );

                return $this->reportOnlineService->generate();
            case TypeCourse::TYPE_NAME_ON_SITE:
                $this->reportOnSiteService->init(
                    $announcement,
                    $user,
                    $allGroups,
                    $groups,
                    $configurations['params'] ?? $configurations
                );

                return $this->reportOnSiteService->generate();
            default:
                throw new \RuntimeException('Option not supported');
        }
    }

    // public static function generateZipTaskFromRequest(Request $request, User $user): ZipFileTask
    // {
    //     $content = json_decode($request->getContent(), true);
    //     $announcementId = $content['announcementId'] ?? null;
    //     $groupId = $content['groupId'] ?? null;
    //     $content['userId'] = $user->getId();

    //     if (empty($announcementId) && empty($groupId)) {
    //         throw new \Exception('announcementId or groupId must be defined');
    //     }

    //     if (!empty($announcementId)) {
    //         unset($content['announcementId']);
    //     }
    //     if (!empty($groupId)) {
    //         unset($content['groupId']);
    //     }

    //     $task = new ZipFileTask();
    //     $task->setType(empty($groupId) ? AnnouncementReportService::TYPE_ANNOUNCEMENT : AnnouncementReportService::TYPE_GROUP)
    //         ->setEntityId(
    //             empty($groupId)
    //                 ? (string) $announcementId
    //                 : (string) $groupId
    //         )
    //         ->setParams($content)
    //         ->setTask('announcement-report')
    //         ->setStatus(ZipFileTask::STATUS_PENDING)
    //         ->setOriginalName('announcement-report' . (new \DateTimeImmutable())->format('dmYHis'))
    //     ;

    //     return $task;
    // }
}
