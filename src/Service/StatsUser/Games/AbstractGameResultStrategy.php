<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\Answer;
use App\Entity\Question;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

abstract class AbstractGameResultStrategy implements GamesStragegyResultInterface
{
    private EntityManagerInterface $em;
    private SettingsService $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultFromGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $questionRepository = $this->em->getRepository(Question::class);

        if (!$userCourseChapter->getData()) {
            return [];
        }

        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                if (empty($attempt)) {
                    continue;
                }
                $timeTotalAttempt = 0;  // Inicializamos el tiempo total del intento
                $questions = [];

                foreach ($attempt as $question) {
                    $questionFind = $questionRepository->findWithDeleted($question['questionId']);

                    if (!$questionFind) {
                        continue;
                    }

                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $questions[] = [
                        'id' => $questionFind?->getId(),
                        'question' => $questionFind?->getQuestion(),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $this->getAnswersQuestion($questionFind, $question),
                    ];

                    $timeTotalAttempt += $timeInQuestion;
                }

                if (empty($questions)) {
                    continue;
                }

                $attempForCalculateScore = [
                    'answers' => $attempt,
                    'remainingTime' => $timeTotalAttempt ?? 0,
                ];

                $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateInAttempt($userCourseChapter, $attempForCalculateScore),
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswersQuestion(Question $question, $answerInTheGame): array
    {
        if (!$question || !$question->getAnswers() || !$answerInTheGame) {
            return [];
        }

        $answerUser = $this->getAnswerUser($answerInTheGame['id'] ?? null);
        if (!$answerUser) {
            return [];
        }

        $answers = [];

        foreach ($question->getAnswers() as $answer) {
            if ($answer->getId() == $answerInTheGame['id']) {
                $isUserAnswer = $answerUser['id'] == $answer->getId();
                $incorrect = $isUserAnswer && !($answerUser['correct'] && $answer->getCorrect());
                $solution = $this->em->getRepository(Answer::class)->getCorrectAnswer($question->getId());
                $answers[] = [
                    'answer' => !\is_null($solution) ? $solution : '',
                    'userAnswer' => $answer->getAnswer(),
                    'correct' => $answer->getCorrect(),
                    'incorrect' => $incorrect,
                ];
            }
        }

        return $answers;
    }

    public function getAnswerUser($idAnswer): array
    {
        if (!$idAnswer) {
            return [];
        }
        $answer = $this->em->getRepository(Answer::class)->findWithDeleted($idAnswer);

        if (!$answer) {
            return [];
        }

        return [
            'id' => $answer?->getId(),
            'answer' => $answer?->getAnswer(),
            'correct' => $answer?->getCorrect(),
        ];
    }

    private function getStateInAttempt(UserCourseChapter $userCourseChapter, $attempt): string
    {
        $score = $this->userCourseService->getPointByAttempInGame($userCourseChapter, $attempt);

        if ($score > 0) {
            return 'ok';
        }

        return 'ko';
    }
}
