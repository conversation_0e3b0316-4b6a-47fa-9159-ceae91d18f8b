<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\TrueOrFalse as EntityTrueOrFalse;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class TrueOrFalse implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;
    private const UPLOADS_PATH = 'app.gameTrueOrFalse_uploads_path';
    private $translator;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService,
        TranslatorInterface $translator
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
        $this->translator = $translator;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $trueOrFalseRepository = $this->em->getRepository(EntityTrueOrFalse::class);
        $path = $this->settings->get(self::UPLOADS_PATH) . '/';
        $lastDateInQuestions = null;
        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $questionFind = $trueOrFalseRepository->findWithDeleted($question['questionId']);
                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    if (!$questionFind) {
                        continue;
                    }

                    $questions[] = [
                        'id' => $questionFind?->getId(),
                        'question' => $questionFind?->getText(),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $this->getAnswers($questionFind, $question),
                    ];

                    $timeTotalAttempt += $timeInQuestion ?? 0;
                }

                if (!$questions) {
                    continue;
                }
                $attemptForCalculateScore = array_merge(['answers' => $attempt], $this->getQuestionAndTime($userCourseChapter));
                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),  // Agregamos el tiempo total del intento al array de resultados
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'date' => $lastDateInQuestions,
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswers(EntityTrueOrFalse $entityTrueOrFalse, $answers): array
    {
        $questionTime = $entityTrueOrFalse->getTime();
        $answerTime = $answers['time'] ?? 0;
        $exceedQuestionTime = $answerTime >= $questionTime;
        $result = $entityTrueOrFalse->getResult();
        $answerUser = $exceedQuestionTime ? null : $answers['correct'] == $result;

        $data = [
            'answer' => $this->translator->trans($result ? 'games.true' : 'games.false'),
            'userAnswer' => !\is_null($answerUser) ? $this->translator->trans($answerUser ? 'games.true' : 'games.false') : null,
        ];

        if ($exceedQuestionTime) {
            $correctIncorrectData = [
                'correct' => false,
                'incorrect' => false,
            ];
        } else {
            $correctIncorrectData = [
                'correct' => $answers['correct'] ?? null,
                'incorrect' => !$answers['correct'] ?? null,
            ];
        }
        $data = array_merge($data, $correctIncorrectData);

        return [$data];
    }

    private function getQuestionAndTime(UserCourseChapter $userCourseChapter): array
    {
        $questions = $this->em->getRepository(EntityTrueOrFalse::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
        $totalQuestions = \count($questions) ?? 0;
        $timeTotal = 0;
        foreach ($questions as $question) {
            $timeTotal += $question->getTime();
        }

        return [
            'totalQuestions' => $totalQuestions,
            'timeTotal' => $timeTotal,
        ];
    }
}
