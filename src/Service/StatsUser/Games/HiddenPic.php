<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\AdivinaImagen;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class HiddenPic implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;
    private const UPLOADS_PATH = 'app.gameAdivinaImagen_uploads_path';

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $hiddenPicRepository = $this->em->getRepository(AdivinaImagen::class);

        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;
                $questionsAdivinaImagen = $this->em->getRepository(AdivinaImagen::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
                $totalQuestions = \count($questionsAdivinaImagen) ?? 0;
                $timeTotal = 0;
                $lastDateInQuestions = null;

                foreach ($questionsAdivinaImagen as $questionAdivina) {
                    $timeTotal += $questionAdivina->getTime();
                }

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $questionFind = $hiddenPicRepository->findWithDeleted($question['questionId']);
                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    $questions[] = [
                        'id' => $questionFind?->getId(),
                        'question' => $questionFind?->getTitle(),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $this->getAnswers($questionFind, $question),
                    ];

                    $timeTotalAttempt += $timeInQuestion ?? 0;
                    $attemptForCalculateScore = [
                        'answers' => $attempt,
                        'totalQuestions' => $totalQuestions,
                        'timeTotal' => $timeTotal,
                    ];
                }

                if (!$questions) {
                    continue;
                }

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attemptForCalculateScore),
                    'attempts' => $attemptForCalculateScore,
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswers(?AdivinaImagen $questionFind, $answer): array
    {
        if (!$answer) {
            return [];
        }
        if (!$questionFind?->getWords()) {
            return [];
        }

        return [[
            'userAnswer' => $answer['value'] ?? null,
            'answer' => $questionFind->getWords(),
            'correct' => $answer['correct'] ?? false,
            'incorrect' => !$answer['correct'] ?? false,
        ]];
    }
}
