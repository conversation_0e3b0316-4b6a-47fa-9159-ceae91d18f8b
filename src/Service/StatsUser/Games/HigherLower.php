<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\HigherLower as EntityHigherLower;
use App\Entity\HigherLowerWords;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class HigherLower implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $higherLowerRepository = $this->em->getRepository(EntityHigherLower::class);
        $lastDateInQuestions = null;

        if (!empty($userCourseChapter->getData)) {
            return $attempts;
        }

        if (!empty($userCourseChapter->getData()['attempts'])) {
            $highLower = $this->em->getRepository(EntityHigherLower::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

            $words = 0;
            $timeTotal = 0;
            foreach ($highLower as $high) {
                $timeTotal += $high->getTime();
                foreach ($high->getHigherLowerWords() as $word) {
                    ++$words;
                }
            }
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $question) {
                    $solution = $this->em->getRepository(HigherLowerWords::class)->getConcatenatedWords($question['questionId']);
                    $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                    $questionFind = $higherLowerRepository->findWithDeleted($question['questionId']);
                    $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                    $questions[] = [
                        'id' => $questionFind?->getId(),
                        'question' => $questionFind?->getTitle(),
                        'correct' => $question['correct'] ?? false,
                        'answers' => $this->getAnswersQuestion($question['attempt'], $solution),
                    ];

                    $timeTotalAttempt += $timeInQuestion ?? 0;
                    $attempForCalculateScore = [
                        'answers' => $attempt,
                        'timeTotal' => $timeTotal,
                        'words' => $words,
                    ];
                }

                if (!$questions) {
                    continue;
                }

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attempForCalculateScore),
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswersQuestion($attempts, $solution): array
    {
        if (empty($attempts)) {
            return [];
        }
        $answers = [];

        foreach ($attempts as $attemp) {
            $higherLowerWord = $this->em->getRepository(HigherLowerWords::class)->find($attemp['id']);
            $answers[] = [
                'answer' => $solution,
                'userAnswer' => $higherLowerWord?->getWord(),
                'correct' => $attemp['correct'],
                'incorrect' => !$attemp['correct'],
            ];
        }

        return $answers;
    }
}
