<?php

declare(strict_types=1);

namespace App\Service\Api;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\Chapter;
use App\Entity\Classroomvirtual;
use App\Entity\ClassroomvirtualUser;
use App\Entity\Course;
use App\Entity\Season;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserLogin;
use App\Enum\AnnouncementState;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementGroupAssistanceService;
use App\Service\Geolocation\GeolocationService;
use App\Service\SettingsService;
use App\Service\Traits\Announcement\AnnouncementTemporalizationTrait;
use App\Utils\DateCalculationUtils;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class ApiAnnouncementService extends AbstractBaseService
{
    use AnnouncementTemporalizationTrait;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        private readonly AnnouncementConfigurationsService $announcementConfigurationsService,
        private readonly AnnouncementGroupAssistanceService $announcementGroupAssistanceService,
        private readonly Security $security,
        private readonly GeolocationService $geolocationService,
        private readonly RequestStack $requestStack,
    ) {
        parent::__construct($em, $settings);
    }

    public function getDataExtraAnnouncement(Announcement $announcement, User $user, ?bool $isDiplomaReport = false): array
    {
        $timezoneActualUser = $this->getTimezoneConnectionUserLogin() ?? $this->settings->get('app.default_timezone');
        $timeZoneAnnouncement = $announcement->getTimezone() ?? $this->settings->get('app.default_timezone');
        $nextSessionAt = !$isDiplomaReport ? ($this->getNextSessionDateAnnouncement($announcement, $user) ?? null) : null;

        if ($announcement->getFinishAt() < new \DateTime()) {
            $nextSessionAt = null;
        }

        return
            [
                'idAnnouncement' => $announcement->getId(),
                'state' => $this->stateAnnouncement($announcement),
                'startAt' => $this->geolocationService->convertTimeZone($announcement->getStartAt(), $timeZoneAnnouncement, $timezoneActualUser),
                'finishAt' => $this->geolocationService->convertTimeZone($announcement->getFinishAt(), $timeZoneAnnouncement, $timezoneActualUser),
                'nextSessionAt' => $nextSessionAt ? $this->geolocationService->convertTimeZone($nextSessionAt, $timeZoneAnnouncement, $timezoneActualUser) : null,
            ];
    }

    private function stateAnnouncement(Announcement $announcement): string
    {
        $now = new \DateTime();
        $startAt = $announcement->getStartAt();
        $finishAt = $announcement->getFinishAt();

        if ($startAt > $now) {
            return AnnouncementState::STATE_NOT_STARTED;
        } elseif ($finishAt < $now) {
            return AnnouncementState::STATE_FINISHED;
        } else {
            return AnnouncementState::STATE_IN_PROGRESS;
        }
    }

    private function getNextDateSesions(Announcement $announcement, User $user)
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);

        if (!$announcementUser) {
            return null;
        }

        $announcementGroup = $announcementUser->getAnnouncementGroup() ? $announcementUser->getAnnouncementGroup()->getId() : null;

        $sessions = $this->announcementGroupAssistanceService->getSessions($announcement, $announcementGroup);

        if (!$sessions) {
            return null;
        }

        // // Changed $nextSession from $announcement->getFinishAt() to null
        // // to correctly display the finish date when the next session has expired.
        // $nexSesion = $announcement->getFinishAt();
        $nexSesion = null;

        foreach ($sessions as $session) {
            if ('IN_PROCESS' == $session['state']) {
                $nexSesion = $session['startAt'];
                break;
            }

            if ('NOT_STARTED' == $session['state']) {
                $nexSesion = $session['startAt'];
                break;
            }
        }

        return $nexSesion;
    }

    private function getNextDateTemporalization(Announcement $announcement)
    {
        $hasTemporalization = $this->announcementConfigurationsService->hasTemporalization($announcement);

        if (!$hasTemporalization) {
            return $announcement->getFinishAt();
        }

        $announcementTemporalization = $this->em->getRepository(AnnouncementTemporalization::class)->findBy(['announcement' => $announcement]);

        if (!$announcementTemporalization) {
            return $announcement->getFinishAt();
        }

        if ($announcement->getStartAt() > new \DateTime()) {
            return $announcement->getStartAt();
        }

        $nextTemporalization = $announcement->getFinishAt();
        $temporalizations = $this->getDataTemporalization($announcement);

        foreach ($temporalizations as $temporalization) {
            if ('IN_PROCESS' == $temporalization['state']) {
                $nextTemporalization = $temporalization['startAt'];
                break;
            }

            if ('NOT_STARTED' == $temporalization['state']) {
                $nextTemporalization = $temporalization['startAt'];
                break;
            }
        }

        return $nextTemporalization;
    }

    private function getNextSessionDateAnnouncement(Announcement $announcement, User $user)
    {
        $nextDateSession = $this->getNextDateSesions($announcement, $user);
        $nextDateTemporalization = $this->getNextDateTemporalization($announcement);

        if ($nextDateSession < $nextDateTemporalization) {
            return $nextDateSession;
        } else {
            return $nextDateTemporalization;
        }
    }

    private function getDataTemporalization(Announcement $announcement): ?array
    {
        $hasTemporalization = $this->announcementConfigurationsService->hasTemporalization($announcement);

        if (!$hasTemporalization) {
            return null;
        }

        $announcementTemporalization = $this->em->getRepository(AnnouncementTemporalization::class)->findBy(['announcement' => $announcement]);

        if (!$announcementTemporalization) {
            return null;
        }

        $temporalizations = [];

        foreach ($announcementTemporalization as $temporalization) {
            if ($temporalization->getStartedAt() >= $temporalization->getFinishedAt()) {
                continue;
            }

            $temporalizations[] = [
                'id' => $temporalization->getId(),
                'startAt' => $temporalization->getStartedAt(),
                'finishAt' => $temporalization->getFinishedAt(),
                'state' => DateCalculationUtils::getStateOfDateRange($temporalization->getStartedAt(), $temporalization->getFinishedAt()),
            ];
        }

        return $temporalizations;
    }

    /**
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    public function getTemporalizationAnnouncement(Announcement $announcement): array
    {
        $request = $this->requestStack->getCurrentRequest();

        $isNew = $request->get('new', false);
        $hasTemporalization = $this->announcementConfigurationsService->hasTemporalization($announcement);
        $baseUrl = $request->getSchemeAndHttpHost();

        $course = $announcement->getCourse();
        $chapters = $this->getSortedChapters($course);

        $timezone = $announcement->getTimezone() ?? $this->settings->get('app.default_timezone');
        $nowTimezone = new \DateTime('now', new \DateTimeZone($timezone));

        $userCourse = $this->em->getRepository(UserCourse::class)
            ->findOneBy([
                'user' => $this->security->getUser(),
                'course' => $course,
                'announcement' => $announcement,
            ]);

        if (!$chapters) {
            return [];
        }

        if (!$hasTemporalization) {
            return $chapters;
        }

        $dataChapters = [];

        foreach ($chapters as $chapter) {
            $isActiveChapterType = $chapter->getType()->isActive();
            $isActiveChapter = $chapter->isIsActive();
            $hasContentCompleted = $chapter->hasContentCompleted();

            if ($isActiveChapterType && $isActiveChapter && $hasContentCompleted) {
                $chapterTemporalization = $this->em->getRepository(AnnouncementTemporalization::class)
                    ->findOneBy([
                        'announcement' => $announcement,
                        'chapter' => $chapter,
                    ]);

                $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)
                    ->findOneBy([
                        'chapter' => $chapter,
                        'userCourse' => $userCourse,
                    ]);

                $status = $userCourseChapter && $userCourseChapter->getFinishedAt();

                $dataChapters[] = [
                    'id' => $chapter->getId(),
                    'title' => $chapter->getTitle(),
                    'description' => $chapter->getDescription(),
                    'type' => [
                        'id' => $chapter->getType()->getId(),
                        'name' => $chapter->getType()->getNormalizedName(),
                    ],
                    'playerUrl' => $chapter->getPlayerUrl(),
                    'status' => $status,
                    'image' => $chapter->getImage(),
                    'imageUrl' => $isNew && $chapter->getImageUrl() ? $baseUrl . '/' . $chapter->getImageUrl() : $chapter->getImageUrl(),
                    'thumbnail' => $isNew && $chapter->getImageUrl() ? $baseUrl . '/' . $chapter->getImageUrl() : $chapter->getImageUrl(),
                    'order' => $chapter->getPosition(),
                    'state' => false,
                    'startAt' => $this->getStartDateTemporalization($announcement, $chapterTemporalization),
                    'finishAt' => $this->getFinishDateTemporalization($announcement, $chapterTemporalization),
                    'extra' => [
                        'isMinimunTime' => $this->hasMinimunTimeChapter($chapterTemporalization, $hasTemporalization),
                        'minimunTime' => $chapterTemporalization ? $chapterTemporalization->getMinimumTime() : 0,
                        'hasTemporalization' => $hasTemporalization,
                        'timeInUserChapter' => $this->getTimeInUserCourseChapter($announcement, $chapter),
                    ],
                ];
            }
        }

        foreach ($dataChapters as $clave => $chapter) {
            $startAt = $chapter['startAt'];
            $finishAt = $chapter['finishAt'] ?? null;

            if ($announcement->getStartAt() < $nowTimezone && $announcement->getFinishAt() < $nowTimezone) {
                $dataChapters[$clave]['state'] = true;
            } elseif ($announcement->getFinishAt() > $nowTimezone) {
                if ($hasTemporalization) {
                    $dataChapters[$clave]['state'] = ($startAt < $nowTimezone && (!$finishAt || $finishAt > $nowTimezone));
                } else {
                    $dataChapters[$clave]['state'] = ($startAt < $nowTimezone);
                }
            }
        }

        usort($dataChapters, function ($a, $b) {
            if ($a['startAt'] === $b['startAt']) {
                return 0; // Keep the original order
            }

            return $a['startAt'] <=> $b['startAt'];
        });

        return $dataChapters;
    }

    private function getSortedChapters(?Course $course): array
    {
        if (!$course) {
            return [];
        }

        $chapters = $this->em->getRepository(Chapter::class)
            ->findBy(
                criteria: ['course' => $course],
                orderBy: ['position' => 'ASC']
            );

        if (empty($chapters)) {
            return [];
        }

        $seasonIds = [];
        foreach ($chapters as $chapter) {
            if ($chapter->getSeason()) {
                $seasonIds[] = $chapter->getSeason()->getId();
            }
        }

        $uniqueSeasonIds = array_unique($seasonIds);
        $this->em->getRepository(Season::class)->findBy(['id' => $uniqueSeasonIds]);

        // This works because the seasons are loaded in memory in the previous line
        usort($chapters, function ($a, $b) {
            // If either chapter doesn't have a season, sort by position only
            if (!$a->getSeason() || !$b->getSeason()) {
                // Chapters without seasons go to the end
                if (!$a->getSeason() && $b->getSeason()) {
                    return 1;
                }
                if ($a->getSeason() && !$b->getSeason()) {
                    return -1;
                }

                // If both don't have seasons, sort by position
                return $a->getPosition() <=> $b->getPosition();
            }

            // If both have seasons with the same sort value, sort by position
            if ($a->getSeason()->getSort() == $b->getSeason()->getSort()) {
                return $a->getPosition() <=> $b->getPosition();
            }

            // Otherwise sort by season sort value
            return $a->getSeason()->getSort() <=> $b->getSeason()->getSort();
        });

        return $chapters;
    }

    private function getTimeInUserCourseChapter(Announcement $announcement, Chapter $chapter)
    {
        $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy(['user' => $this->security->getUser(), 'course' => $announcement->getCourse(), 'announcement' => $announcement]);

        if (!$userCourse) {
            return 0;
        }

        $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)->findOneBy(['userCourse' => $userCourse, 'chapter' => $chapter]);

        if (!$userCourseChapter) {
            return 0;
        }

        return $userCourseChapter->getTimeSpent() ?? 0;
    }

    public function getSessionsAnnouncement(Announcement $announcement, User $user): array
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);

        if (!$announcementUser) {
            return [];
        }

        $announcementGroup = $announcementUser->getAnnouncementGroup() ? $announcementUser->getAnnouncementGroup()->getId() : null;
        $sessions = $this->announcementGroupAssistanceService->getSessions($announcement, $announcementGroup);

        if (!$sessions) {
            return [];
        }

        $sessionIds = array_column($sessions, 'id');
        $sessionAssistances = $this->em->getRepository(AnnouncementGroupSession::class)->findBy(['id' => $sessionIds]);

        foreach ($sessions as $clave => $session) {
            $sessionAssistance = $this->announcementGroupAssistanceService->getAsistanceUserBySession($sessionAssistances[$clave], $user);

            $announcementGroupSession = $this->em->getRepository(AnnouncementGroupSession::class)->findOneBy(['id' => $session['id']]);
            $digitalSignature = $announcementGroupSession ? $this->searchDigitalSignature($announcement, $announcementGroupSession) : null;
            // Remover elementos
            unset($sessions[$clave]['fileAssistance']);
            unset($sessions[$clave]['assistanceFiles']);

            $sessions[$clave]['assistance'] = 'NOT_STARTED' == $session['state'] ? false : $sessionAssistance;
            $sessions[$clave]['name'] = 'Sesión ' . ($clave + 1);
            $sessions[$clave]['stateSession'] = $session['state'];
            $sessions[$clave]['state'] = 'IN_PROCESS' == $session['state'];
            $sessions[$clave]['place'] = $session['place'] ?? '--'; // $announcementUser->getAnnouncementGroup() ? $announcementUser->getAnnouncementGroup()->getPlace() : '--';
            $sessions[$clave]['url'] = $this->getClassroomVirtualUserUrl($session['id'], $announcementUser);
            $sessions[$clave]['hasDigitalSignature'] = $this->hasDigitalSignatureTheSession($announcement, $sessions[$clave]);
            $sessions[$clave]['dateDigitalSignature'] = $digitalSignature ? $digitalSignature->getCreatedAt()->format('H:i') : null;

            if ('NOT_STARTED' == $session['state'] && null != $session['url']) {
                $sessions[$clave]['url'] = '--';
            }
        }

        if (TypeCourse::TYPE_AULA_VIRTUAL == $announcement->getCourse()->getTypeCourse()->getId()) {
            $sessions = array_filter($sessions, function ($session) {
                return null != $session['url'];
            });
        }

        return $sessions;
    }

    /**
     * @throws \DateMalformedStringException
     * @throws \DateInvalidTimeZoneException
     */
    public function getTemporalizationAndSessionsAnnouncement(Announcement $announcement, User $user): array
    {
        $course = $announcement->getCourse();
        $typeCourse = $course?->getTypeCourse();

        $temporalization = [];
        $sessions = [];

        if ($typeCourse) {
            $typeCourseId = $typeCourse->getId();

            if (TypeCourse::TYPE_PRESENCIAL !== $typeCourseId && TypeCourse::TYPE_AULA_VIRTUAL !== $typeCourseId) {
                $temporalization = $this->getTemporalizationAnnouncement($announcement);
            }
            $sessions = $this->getSessionsAnnouncement($announcement, $user);
        }

        $data = array_merge($temporalization, $sessions);

        if (!$data) {
            return [];
        }

        usort($data, function ($a, $b) {
            if (
                !is_array($a)
                || !is_array($b)
                || !isset($a['startAt'])
                || !isset($b['startAt'])
                || $a['startAt'] === $b['startAt']
            ) {
                return 0; // Keep the original order
            }

            return $a['startAt'] <=> $b['startAt'];
        });

        return $data;
    }

    private function getClassroomVirtualUserUrl($idSession, AnnouncementUser $announcementUser)
    {
        $classRoomVirtual = $this->em->getRepository(Classroomvirtual::class)->findOneBy(['groupSession' => $idSession]);
        $classRoomVirtualUser = $this->em->getRepository(ClassroomvirtualUser::class)->findOneBy(
            [
                'classroomvirtual' => $classRoomVirtual,
                'announcementuser' => $announcementUser,
            ]
        );

        return $classRoomVirtualUser ? $classRoomVirtualUser->getClassroomvirtualuserUrl() : null;
    }

    private function hasDigitalSignatureTheSession(Announcement $announcement, $session): bool
    {
        if (!$announcement->getNotifiedAt()) {
            return false;
        }

        $hasDigitalSignature = $this->announcementConfigurationsService->hasDigitalSignature($announcement);
        if (!$hasDigitalSignature) {
            return false;
        }

        if ('NOT_STARTED' == $session['stateSession']) {
            return false;
        }
        if ('FINISHED' == $session['stateSession']) {
            return false;
        }

        if ('IN_PROCESS' == $session['stateSession'] && $hasDigitalSignature) {
            return true;
        }

        return false;
    }

    private function getSessionInfo(Announcement $announcement): ?array
    {
        $user = $this->security->getUser();

        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);

        if (!$announcementUser || !$announcementUser->getAnnouncementGroup()) {
            return null;
        }

        $announcementGroup = $announcementUser->getAnnouncementGroup()->getId();
        $sessions = $this->announcementGroupAssistanceService->getSessions($announcement, $announcementGroup);

        if (!$sessions) {
            return null;
        }

        $sessionIds = array_column($sessions, 'id');
        $sessionAssistances = $this->em->getRepository(AnnouncementGroupSession::class)->findBy(['id' => $sessionIds]);

        return ['sessions' => $sessions, 'sessionAssistances' => $sessionAssistances];
    }

    private function validateGroupSessionAndDigitalSignature(Announcement $announcement)
    {
        $info = $this->getSessionInfo($announcement);

        if (!$info || !$this->announcementConfigurationsService->hasDigitalSignature($announcement)) {
            return null;
        }

        foreach ($info['sessions'] as $session) {
            //   $sessionAssistance = $this->announcementGroupAssistanceService->getAsistanceUserBySession($info['sessionAssistances'][$clave], $this->security->getUser());
            $announcementGroupSession = $this->em->getRepository(AnnouncementGroupSession::class)->findOneBy(['id' => $session['id']]);
            if ('IN_PROCESS' == $session['state'] /* && !$sessionAssistance */ && !$this->searchDigitalSignature($announcement, $announcementGroupSession)) {
                return $announcementGroupSession;
            }
        }

        return null;
    }

    public function theCourseHasSignatureDigitalPending(Announcement $announcement): bool
    {
        if (!$announcement->getNotifiedAt()) {
            return false;
        }

        return null !== $this->validateGroupSessionAndDigitalSignature($announcement);
    }

    public function getGroupSessionInProccessThanDigitalSignature(Announcement $announcement)
    {
        return $this->validateGroupSessionAndDigitalSignature($announcement);
    }

    public function searchDigitalSignature(Announcement $announcement, AnnouncementGroupSession $announcementGroupSession)
    {
        $user = $this->security->getUser();
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);

        if (!$announcementUser) {
            return null;
        }

        return $this->em->getRepository(AnnouncementUserDigitalSignature::class)->findOneBy([
            'announcementUser' => $announcementUser,
            'announcementGroupSession' => $announcementGroupSession,
        ]);
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getDateDigitalSignature(AnnouncementUser $announcementUser)
    {
        $digitalSignature = $this->em->getRepository(AnnouncementUserDigitalSignature::class)
            ->createQueryBuilder('udc')
            ->select('udc')
            ->where('udc.announcementUser = :announcementUser')
            ->andWhere('udc.createdAt >= :currentDate')
            ->setParameter('announcementUser', $announcementUser)
            ->setParameter('currentDate', (new \DateTime('now'))->format('Y-m-d'))
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        return $digitalSignature ? $digitalSignature->getCreatedAt()->format('H:i') : null;
    }

    private function getTimezoneConnectionUserLogin()
    {
        $userLogin = $this->em->getRepository(UserLogin::class)->findOneBy(['user' => $this->security->getUser()], ['createdAt' => 'DESC']);

        if (!$userLogin) {
            return null;
        }

        return $userLogin->getTimezone() ?? $this->settings->get('app.default_timezone');
    }
}
