{"a2lix/auto-form-bundle": {"version": "0.3.3"}, "a2lix/translation-form-bundle": {"version": "3.0.7"}, "ankitpokhrel/tus-php": {"version": "v2.1.2"}, "beberlei/doctrineextensions": {"version": "v1.3.0"}, "behat/transliterator": {"version": "v1.3.0"}, "brick/math": {"version": "0.9.2"}, "composer/package-versions-deprecated": {"version": "*********"}, "cron/cron": {"version": "1.5.0"}, "cron/cron-bundle": {"version": "2.6.0"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["./config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.10.0"}, "doctrine/collections": {"version": "1.6.4"}, "doctrine/common": {"version": "2.13.0"}, "doctrine/data-fixtures": {"version": "1.4.3"}, "doctrine/dbal": {"version": "2.10.2"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.0", "ref": "a9f2463b9f73efe74482f831f03a204a41328555"}, "files": ["./config/packages/doctrine.yaml", "./config/packages/prod/doctrine.yaml", "./src/Entity/.gitignore", "./src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.0", "ref": "fc52d86631a6dfd9fdf3381d0b7e3df2069e51b3"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "1.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.2", "ref": "c1431086fec31f17fbcfe6d6d7e92059458facc1"}, "files": ["./config/packages/doctrine_migrations.yaml", "./src/Migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.0"}, "doctrine/inflector": {"version": "1.4.1"}, "doctrine/instantiator": {"version": "1.3.0"}, "doctrine/lexer": {"version": "1.2.0"}, "doctrine/migrations": {"version": "2.2.1"}, "doctrine/orm": {"version": "v2.7.2"}, "doctrine/persistence": {"version": "1.3.7"}, "doctrine/sql-formatter": {"version": "1.0.1"}, "easycorp/easyadmin-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.0", "ref": "b131e6cbfe1b898a508987851963fff485986285"}}, "egulias/email-validator": {"version": "2.1.24"}, "exsyst/swagger": {"version": "v0.4.1"}, "ezyang/htmlpurifier": {"version": "v4.13.0"}, "friendsofphp/php-cs-fixer": {"version": "3.63", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.2"}, "friendsofsymfony/ckeditor-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.0", "ref": "8eb1cd0962ded6a6d6e1e5a9b6d3e888f9f94ff6"}, "files": ["config/packages/fos_ckeditor.yaml"]}, "friendsofsymfony/rest-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.2", "ref": "cad41ef93d6150067ae2bb3c7fd729492dff6f0a"}, "files": ["./config/packages/fos_rest.yaml"]}, "gedmo/doctrine-extensions": {"version": "v2.4.41"}, "gesdinet/jwt-refresh-token-bundle": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "13fb2b58ec6204211815eb6f4c69e4918e5af617"}, "files": ["./config/packages/gesdinet_jwt_refresh_token.yaml", "./config/routes/gesdinet_jwt_refresh_token.yaml", "./src/Entity/RefreshToken.php"]}, "guzzlehttp/guzzle": {"version": "7.2.0"}, "guzzlehttp/promises": {"version": "1.4.0"}, "guzzlehttp/psr7": {"version": "1.7.0"}, "imagine/imagine": {"version": "1.2.4"}, "jms/metadata": {"version": "2.3.0"}, "kms/froala-editor-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "666a78d430cecebe61a1990d7b8f638e45b2eeab"}, "files": ["./config/packages/kms_froala_editor.yaml", "./config/routes/kms_froala_editor.yaml"]}, "knplabs/doctrine-behaviors": {"version": "2.2.0"}, "knplabs/knp-components": {"version": "v3.1.0"}, "knplabs/knp-paginator-bundle": {"version": "v5.6.0"}, "knpuniversity/oauth2-client-bundle": {"version": "2.17", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.20", "ref": "1ff300d8c030f55c99219cc55050b97a695af3f6"}, "files": ["./config/packages/knpu_oauth2_client.yaml"]}, "laminas/laminas-code": {"version": "3.4.1"}, "lcobucci/clock": {"version": "2.0.0"}, "lcobucci/jwt": {"version": "3.3.2"}, "league/tactician-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "222c3d39d38378bc6a9790a0b5baf841ba6679b9"}}, "lexik/jwt-authentication-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "5b2157bcd5778166a5696e42f552ad36529a07a6"}, "files": ["./config/packages/lexik_jwt_authentication.yaml"]}, "liip/imagine-bundle": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.8", "ref": "5a5bdc2d0e2533ed6935d5ae562f2b318a8fc1ee"}, "files": ["config/packages/liip_imagine.yaml", "config/routes/liip_imagine.yaml"]}, "maennchen/zipstream-php": {"version": "2.1.0"}, "markbaker/complex": {"version": "2.0.3"}, "markbaker/matrix": {"version": "2.1.3"}, "mobiledetect/mobiledetectlib": {"version": "2.8.37"}, "monolog/monolog": {"version": "2.1.0"}, "mpdf/mpdf": {"version": "v8.0.10"}, "myclabs/deep-copy": {"version": "1.10.2"}, "namshi/jose": {"version": "7.2.3"}, "nelmio/api-doc-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["./config/packages/nelmio_api_doc.yaml", "./config/routes/nelmio_api_doc.yaml"]}, "nelmio/cors-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["./config/packages/nelmio_cors.yaml"]}, "nesbot/carbon": {"version": "2.45.1"}, "nette/utils": {"version": "v3.2.2"}, "nikic/php-parser": {"version": "v4.4.0"}, "nyholm/psr7": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "4a8c0345442dcca1d8a2c65633dcf0285dd5a5a2"}, "files": ["config/packages/nyholm_psr7.yaml"]}, "oat-sa/bundle-lti1p3": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "2ab374bc274fde3da5cff38d840f620178f56a6b"}, "files": ["config/packages/lti1p3.yaml", "config/routes/lti1p3.yaml"]}, "php-http/message-factory": {"version": "v1.0.2"}, "phpdocumentor/reflection-common": {"version": "2.1.0"}, "phpdocumentor/reflection-docblock": {"version": "5.1.0"}, "phpdocumentor/type-resolver": {"version": "1.1.0"}, "phpoffice/phpspreadsheet": {"version": "1.18.0"}, "phpstan/phpdoc-parser": {"version": "1.2.0"}, "phpstan/phpstan": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "predis/predis": {"version": "v1.1.6"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "1.1.3"}, "psr/simple-cache": {"version": "1.0.1"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "ramsey/collection": {"version": "1.1.3"}, "ramsey/uuid": {"version": "4.1.1"}, "scienta/doctrine-json-functions": {"version": "4.3.0"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["./config/packages/sensio_framework_extra.yaml"]}, "setasign/fpdi": {"version": "v2.3.6"}, "squizlabs/php_codesniffer": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.6", "ref": "1019e5c08d4821cb9b77f4891f8e9c31ff20ac6f"}, "files": ["phpcs.xml.dist"]}, "stof/doctrine-extensions-bundle": {"version": "1.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.2", "ref": "6c1ceb662f8997085f739cd089bfbef67f245983"}, "files": ["./config/packages/stof_doctrine_extensions.yaml"]}, "symfony/asset": {"version": "v5.0.8"}, "symfony/cache": {"version": "v5.0.8"}, "symfony/cache-contracts": {"version": "v2.0.1"}, "symfony/config": {"version": "v5.0.8"}, "symfony/console": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "ea8c0eda34fda57e7d5cd8cbd889e2a387e3472c"}, "files": ["./bin/console", "./config/bootstrap.php"]}, "symfony/css-selector": {"version": "v5.2.4"}, "symfony/debug-bundle": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "f8863cbad2f2e58c4b65fa1eac892ab189971bea"}, "files": ["./config/packages/dev/debug.yaml"]}, "symfony/dependency-injection": {"version": "v5.0.8"}, "symfony/deprecation-contracts": {"version": "v2.2.0"}, "symfony/doctrine-bridge": {"version": "v5.0.8"}, "symfony/dotenv": {"version": "v5.0.8"}, "symfony/error-handler": {"version": "v5.0.8"}, "symfony/event-dispatcher": {"version": "v5.0.8"}, "symfony/event-dispatcher-contracts": {"version": "v2.0.1"}, "symfony/expression-language": {"version": "v5.0.8"}, "symfony/filesystem": {"version": "v5.0.8"}, "symfony/finder": {"version": "v5.0.8"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": ["./.env"]}, "symfony/form": {"version": "v5.0.8"}, "symfony/framework-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "36d3075b2b8e0c4de0e82356a86e4c4a4eb6681b"}, "files": ["./config/bootstrap.php", "./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/packages/test/framework.yaml", "./config/routes/dev/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/http-client": {"version": "v5.0.9"}, "symfony/http-client-contracts": {"version": "v2.1.2"}, "symfony/http-foundation": {"version": "v5.0.8"}, "symfony/http-kernel": {"version": "v5.0.8"}, "symfony/intl": {"version": "v5.0.8"}, "symfony/lock": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["./config/packages/lock.yaml"]}, "symfony/loco-translation-provider": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c706da68af4ae956f45cffccf99a130e9484cb33"}}, "symfony/mailchimp-mailer": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "1be490e068472fc87e226cf54bc95fb6f2e49117"}}, "symfony/mailer": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "15658c2a0176cda2e7dba66276a2030b52bd81b2"}, "files": ["./config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mime": {"version": "v5.0.8"}, "symfony/monolog-bridge": {"version": "v5.0.8"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "a89f4cd8a232563707418eea6c2da36acd36a917"}, "files": ["./config/packages/dev/monolog.yaml", "./config/packages/prod/monolog.yaml", "./config/packages/test/monolog.yaml"]}, "symfony/options-resolver": {"version": "v5.0.8"}, "symfony/phpunit-bridge": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "97cb3dc7b0f39c7cfc4b7553504c9d7b7795de96"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.17.0"}, "symfony/polyfill-intl-icu": {"version": "v1.17.0"}, "symfony/polyfill-intl-idn": {"version": "v1.17.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.17.0"}, "symfony/polyfill-mbstring": {"version": "v1.17.0"}, "symfony/polyfill-php73": {"version": "v1.17.0"}, "symfony/polyfill-php80": {"version": "v1.17.0"}, "symfony/polyfill-php81": {"version": "v1.23.0"}, "symfony/polyfill-uuid": {"version": "v1.18.1"}, "symfony/process": {"version": "v5.2.7"}, "symfony/property-access": {"version": "v5.0.8"}, "symfony/property-info": {"version": "v5.0.10"}, "symfony/proxy-manager-bridge": {"version": "v5.2.4"}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["./config/packages/prod/routing.yaml", "./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["./config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.0.8"}, "symfony/security-csrf": {"version": "v5.0.8"}, "symfony/security-guard": {"version": "v5.0.8"}, "symfony/security-http": {"version": "v5.0.8"}, "symfony/serializer": {"version": "v5.0.10"}, "symfony/service-contracts": {"version": "v2.0.1"}, "symfony/stopwatch": {"version": "v5.0.8"}, "symfony/string": {"version": "v5.0.9"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["./config/packages/translation.yaml", "./translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.0.1"}, "symfony/twig-bridge": {"version": "v5.0.8"}, "symfony/twig-bundle": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "fab9149bbaa4d5eca054ed93f9e1b66cc500895d"}, "files": ["./config/packages/test/twig.yaml", "./config/packages/twig.yaml", "./templates/base.html.twig"]}, "symfony/uid": {"version": "v5.1.5"}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["./config/packages/test/validator.yaml", "./config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.0.8"}, "symfony/var-exporter": {"version": "v5.0.8"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["./config/packages/dev/web_profiler.yaml", "./config/packages/test/web_profiler.yaml", "./config/routes/dev/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.6", "ref": "69e1d805ad95964088bd510c05995e87dc391564"}, "files": ["assets/css/app.css", "assets/js/app.js", "config/packages/assets.yaml", "config/packages/prod/webpack_encore.yaml", "config/packages/test/webpack_encore.yaml", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/yaml": {"version": "v5.0.8"}, "symfonycasts/reset-password-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "97c1627c0384534997ae1047b93be517ca16de43"}, "files": ["./config/packages/reset_password.yaml"]}, "tijsverkoyen/css-to-inline-styles": {"version": "2.2.3"}, "twig/cssinliner-extra": {"version": "v3.3.0"}, "twig/extra-bundle": {"version": "v3.0.3"}, "twig/string-extra": {"version": "v3.3.0"}, "twig/twig": {"version": "v3.0.3"}, "vich/uploader-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.5", "ref": "c4f5755b37fb65b9c6a3cbdae91205c15a137ed4"}, "files": ["config/packages/vich_uploader.yaml"]}, "vimeo/vimeo-api": {"version": "3.0.6"}, "webmozart/assert": {"version": "1.9.0"}, "willdurand/jsonp-callback-validator": {"version": "v1.1.0"}, "willdurand/negotiation": {"version": "v2.3.1"}, "zircote/swagger-php": {"version": "2.0.16"}}