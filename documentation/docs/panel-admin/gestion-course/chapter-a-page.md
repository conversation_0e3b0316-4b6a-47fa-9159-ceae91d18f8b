---
sidebar_position: 1
---

# Capítulos

En Easylearning, los capítulos son elementos clave que permiten la gestión de los contenidos en los cursos. Estos capítulos se dividen en varios tipos, cada uno con su propósito y características específicas.

## Tipos de Capítulos

A continuación, se presenta una lista de ejemplo de los tipos de capítulos disponibles:

```json
[
    {
        "id": 1,
        "name": "Scorm",
        "type": "content",
        "active": true
    },
    {
        "id": 2,
        "name": "Contents",
        "type": "content",
        "active": true
    },
    {
        "id": 3,
        "name": "Roulette",
        "type": "game",
        "active": true
    },
    {
        "id": 4,
        "name": "Double or Nothing",
        "type": "game",
        "active": true
    },
    {
        "id": 5,
        "name": "Quiz",
        "type": "game",
        "active": true
    },
    {
        "id": 6,
        "name": "Puzzle",
        "type": "game",
        "active": true
    },
    {
        "id": 7,
        "name": "Hidden Words",
        "type": "game",
        "active": true
    },
    {
        "id": 8,
        "name": "Pdf",
        "type": "content",
        "active": true
    },
    {
        "id": 10,
        "name": "Slider",
        "type": "content",
        "active": true
    }
]
```


## Entender los campos

- **name** Nombre del capítulo
- **type** Tipo del capítulo
  - **game** Son aquellos capítulos de tipo juego
  - **content** Son los capítulos de tipo contenido
- **active** Permite activar o desactivar segun las necesidades del cliente




