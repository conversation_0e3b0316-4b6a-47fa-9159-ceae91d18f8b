# Vimeo

*Easylearning* aloja los vídeos en Vimeo, para una mejor gestión de los recursos.

> La API de Vimeo es una API REST. REST, o transferencia de estado representacional, es un estilo arquitectónico o filosofía de diseño para interactuar con recursos en línea (como videos) utilizando métodos HTTP estándar, incluidos GET, POST y PATCH.

#### Api oficial de vimeo
https://developer.vimeo.com/api/guides/start

## Configuraciones por cliente

1. `app.clientIdVimeo`: 'clientidvimeo'
2. `app.clientSecretVimeo`: 'clientesecretVimeo'
3. `app.accessTokenVimeo`: 'accesstokenvimeo'
4. `app.userIdVimeo`: 'userIdVimeo'
5. `app.projectIdVimeo`: 'projectIdVimeo'
6. `app.projectIdResourceCourse`: ''
7. `app.projectIdTaskCourse`: ''
8. `app.projectIdVideoQuiz`: ''
9. `app.projectIdRoleplay`: ''

### Configuraciones que pueden mantenerse igual

Estas configuraciones se obtienen atraves de https://developer.vimeo.com/apps, en este espacio se crea una app para poder conectar a la api de vimeo, se puede usar una para todos los clientes, pero tambien se puede crear una para cada uno, pero no estan necesario.

1. `app.clientIdVimeo`: 'clientidvimeo'
2. `app.clientSecretVimeo`: 'clientesecretVimeo'
3. `app.accessTokenVimeo`: 'accesstokenvimeo'
4. `app.userIdVimeo`: 'userIdVimeo'

Dentro de easylearning contamos con un servicio que gestiona la subida de los recursos a vimeo

- **Path**: src/services/VimeoService.php

### Otras variables

1. `app.projectIdVimeo`: Es el identificador de la carpeta del cliente que tenemos configurado en vimeo.
2. `app.projectIdResourceCourse`: Es una carpeta donde van los video de tipo de materiales, que generalmente se crean en cursos en convocatorias.
3. `app.projectIdTaskCourse`: Es una carpeta donde van los video de las tareas, que generalmente se crean en cursos en convocatorias.
4. `app.projectIdVideoQuiz`: Se utiliza para guardar los recursos de los video quiz.
5. `app.projectIdRoleplay`: Se utiliza para guardar los recursos que se generan desde el editor de roleplay.


