# Detalle del curso

Esta API proporciona información detallada sobre un curso. Puede ser utilizado para obtener datos de cursos de teleformación, presenciales, mixtos y aula virtual.

## Endpoint

- **URL:** `/api/courses/{id}`
- **Método:** GET

### Parámetros

- `id`: ID del curso.

### Controlador

- `ApiCourseController`

## Cómo utilizar este archivo

Esta ruta trae la información necesario para los distintitos tipos de cursos, ya sea para teleformacion, presencial, mixta y aula virtual

## Descripción

La respuesta JSON contiene varios campos que brindan información sobre el curso y su estado para el usuario. A continuación, se describen los campos más importantes:

- `course`: Información del curso, como el título, la descripción, las temporadas, etc.
- `points`: Número de puntos que se pueden obtener al completar el curso.
- `userCourseId`: ID del curso del usuario, si el usuario ya se ha inscrito en el curso.
- `finished`: Fecha en la que el usuario completó el curso, si el usuario ya lo ha completado.
- `valued`: Indica si el usuario ha valorado el curso.
- `opinions`: Opiniones de otros usuarios sobre el curso.
- `averageTime`: Tiempo promedio que tardan los usuarios en completar el curso.
- `progress`: Progreso del usuario en el curso.
- `itineraries`: Itinerario o itinerarios del curso, si el curso forma parte de uno o varios itinerarios.
- `announcement`: ID del anuncio del curso, si el curso está asociado a un anuncio.
- `materials`: Materiales del curso, como los vídeos, los documentos, etc.
- `tasks`: Tareas del curso.
- `messages`: Mensajes del curso.
- `tutor`: Tutor del curso.
- `channels`: Canales del curso, como el canal de foro y el canal de chat.
- `infoAnnouncement`: Información del anuncio del curso, como la guía didáctica y las horas totales del curso.
- `configurations`: Estas configuraciones tienes efecto tanto en la formación que ve el usuario.
  - `SECTIONS:` Esto es para manejar el panel del detalle del usuario
  - `CRITERIA:` Son criterios que permiten controlar la actividad del usuario

### Detalle del curso para teleformación

Esta es la data que llega cuando pinchas dentro del detalle de un curso, independientemente si es
itinerario, teleformación ó convocatoria

### Info convocatoria

La siguiente data servirá para pintar la guia didactica y total de horas de la convocatoria

```json
{
  "infoAnnouncement": {
    "guideURL": "http://127.0.0.1:8000/assets_announcement/didactic_guide//64f8509c69a30831876741.pdf",
    "id": 127,
    "totalHours": 10
  }
}
```

### Data que llega en las temporadas de un curso mixto

Un curso mixto puede tener capitulos y temporadas, para diferenciar un capitulo de una sesión, un capitulo tiene un tipo.

```json
{
  "seasons": [
    {
      "id": 1,
      "name": "annoucement",
      "type": "announcement",
      "chapters": [
        {
          "id": 1769,
          "title": "pdf",
          "description": "<p>pdf</p>",
          "type": {
            "id": 8,
            "name": "Pdf"
          },
          "playerUrl": "/pdf/1769",
          "status": true,
          "image": null,
          "imageUrl": null,
          "order": 1,
          "state": true,
          "startAt": "2023-09-13 10:01:00",
          "finishAt": "2023-09-24 18:23:00"
        },

        {
          "id": 7,
          "startAt": "2023-09-20 13:26:00",
          "finishAt": "2023-09-20 14:26:00",
          "url": null,
          "state": false,
          "hourInit": "13:26:00",
          "hourEnd": "14:26:00",
          "assistance": false,
          "name": "Sesión 1",
          "stateSession": "NOT_STARTED",
          "place": "Erandio",
          "order": 4
        }
      ]
    }
  ]
}
```

### Informacion necesaria para pintar las sesiones en el front

```json
{
  "id": 9,
  "startAt": "2023-09-22 13:27:00",
  "finishAt": "2023-09-22 14:27:00",
  "url": null,
  "state": false,
  "hourInit": "13:27:00",
  "hourEnd": "14:27:00",
  "assistance": false,
  "name": "Sesión 3",
  "stateSession": "NOT_STARTED",
  "place": "Erandio",
  "hasDigitalSignature": true,
  "order": 6
}
```

#### Entender la data de la sesiones

- **stateSession:** Pueden haber tres tipos de estado
  - **NOT_STARTED:** La sesión no ha iniciado
  - **IN_PROCESS:** La sesión esta en proceso
  - **FINISHED:** La sesión ha finalizado
- **assistance:** Indica si el usuario asistiço ó no
- **url:** Esto nos vale para cuando la convocatoria es aula virtual
- **hasDigitalSignature:** Si esto esta en true, el usuario puede registar la firma digital

[Nota]: Esta la data del curso mixto, es valida para presencial y aula virtual

### Ejemplo de datos

A continuación, se muestra un ejemplo de los datos que se devuelven al realizar la llamada a este endpoint:

```json
{
    "status": 200,
    "error": false,
    "data": {
        "course": {
            "id": 324,
            "code": "002",
            "name": "Mixto",
            "description": "<p>Es un curso mixto</p>",
            "categories": [],
            "image": null,
            "category": {
                "id": 11,
                "name": "ATENCIÓN AL CLIENTE"
            },
            "generalInformation": "",
            "seasons": [
                {
                    "id": 1,
                    "name": "annoucement",
                    "type": "announcement",
                    "chapters": [
                        {
                            "id": 1769,
                            "title": "pdf",
                            "description": "<p>pdf</p>",
                            "type": {
                                "id": 8,
                                "name": "Pdf"
                            },
                            "playerUrl": "/pdf/1769",
                            "status": true,
                            "image": null,
                            "imageUrl": null,
                            "order": 1,
                            "state": true,
                            "startAt": "2023-09-13 10:01:00",
                            "finishAt": "2023-09-24 18:23:00"
                        },
                        {
                            "id": 1771,
                            "title": "tercer pdf",
                            "description": "<p>tercer pdf</p>",
                            "type": {
                                "id": 8,
                                "name": "Pdf"
                            },
                            "playerUrl": "/pdf/1771",
                            "status": false,
                            "image": null,
                            "imageUrl": null,
                            "order": 2,
                            "state": false,
                            "startAt": "2023-09-16 13:23:00",
                            "finishAt": "2023-09-16 16:23:00"
                        },
                        {
                            "id": 1770,
                            "title": "video",
                            "description": "<p>Video</p>",
                            "type": {
                                "id": 9,
                                "name": "Vídeo"
                            },
                            "playerUrl": "/video/1770",
                            "status": false,
                            "image": null,
                            "imageUrl": null,
                            "order": 3,
                            "state": false,
                            "startAt": "2023-09-18 13:23:00",
                            "finishAt": "2023-09-18 18:23:00"
                        },
                        {
                            "id": 7,
                            "startAt": "2023-09-20 13:26:00",
                            "finishAt": "2023-09-20 14:26:00",
                            "url": null,
                            "state": false,
                            "hourInit": "13:26:00",
                            "hourEnd": "14:26:00",
                            "assistance": false,
                            "name": "Sesión 1",
                            "stateSession": "NOT_STARTED",
                            "place": "Erandio",
                            "order": 4
                        },
                        {
                            "id": 8,
                            "startAt": "2023-09-21 13:26:00",
                            "finishAt": "2023-09-21 14:26:00",
                            "url": null,
                            "state": false,
                            "hourInit": "13:26:00",
                            "hourEnd": "14:26:00",
                            "assistance": false,
                            "name": "Sesión 2",
                            "stateSession": "NOT_STARTED",
                            "place": "Erandio",
                            "order": 5
                        },
                        {
                            "id": 9,
                            "startAt": "2023-09-22 13:27:00",
                            "finishAt": "2023-09-22 14:27:00",
                            "url": null,
                            "state": false,
                            "hourInit": "13:27:00",
                            "hourEnd": "14:27:00",
                            "assistance": false,
                            "name": "Sesión 3",
                            "stateSession": "NOT_STARTED",
                            "place": "Erandio",
                            "order": 6
                        }
                    ]
                }
            ],
            "documentation": null,
            "new": false,
            "imageUrl": null,
            "thumbnail": null,
            "hasOwnQuestions": false,
             "typeCourse": {
                "id": 3,
                "name": "Mixto"
            }
        },
        "points": 58004,
        "userCourseId": 361,
        "finished": null,
        "valued": false,
        "opinions": [],
        "averageTime": {
            "countUsers": null,
            "SUM( c.time )": null,
            "averageTime": null,
            "minutos": null
        },
        "progress": 33,
        "itineraries": [],
        "announcement": 128,
        "materials": [],
        "tasks": [],
        "messages": [],
        "tutor": {
            "id": 118,
            "firstName": "Raul",
            "lastName": "Zuñiga",
            "avatar": null
        },
        "seasons": [
            {
                "id": 1,
                "name": "annoucement",
                "type": "announcement",
                "chapters": [
                    {
                        "id": 1769,
                        "title": "pdf",
                        "description": "<p>pdf</p>",
                        "type": {
                            "id": 8,
                            "name": "Pdf"
                        },
                        "playerUrl": "/pdf/1769",
                        "status": true,
                        "image": null,
                        "imageUrl": null,
                        "order": 1,
                        "state": true,
                        "startAt": "2023-09-13 10:01:00",
                        "finishAt": "2023-09-24 18:23:00"
                    },
                    {
                        "id": 1771,
                        "title": "tercer pdf",
                        "description": "<p>tercer pdf</p>",
                        "type": {
                            "id": 8,
                            "name": "Pdf"
                        },
                        "playerUrl": "/pdf/1771",
                        "status": false,
                        "image": null,
                        "imageUrl": null,
                        "order": 2,
                        "state": false,
                        "startAt": "2023-09-16 13:23:00",
                        "finishAt": "2023-09-16 16:23:00"
                    },
                    {
                        "id": 1770,
                        "title": "video",
                        "description": "<p>Video</p>",
                        "type": {
                            "id": 9,
                            "name": "Vídeo"
                        },
                        "playerUrl": "/video/1770",
                        "status": false,
                        "image": null,
                        "imageUrl": null,
                        "order": 3,
                        "state": false,
                        "startAt": "2023-09-18 13:23:00",
                        "finishAt": "2023-09-18 18:23:00"
                    },
                    {
                        "id": 7,
                        "startAt": "2023-09-20 13:26:00",
                        "finishAt": "2023-09-20 14:26:00",
                        "url": null,
                        "state": false,
                        "hourInit": "13:26:00",
                        "hourEnd": "14:26:00",
                        "assistance": false,
                        "name": "Sesión 1",
                        "stateSession": "NOT_STARTED",
                        "place": "Erandio",
                        "order": 4
                    },
                    {
                        "id": 8,
                        "startAt": "2023-09-21 13:26:00",
                        "finishAt": "2023-09-21 14:26:00",
                        "url": null,
                        "state": false,
                        "hourInit": "13:26:00",
                        "hourEnd": "14:26:00",
                        "assistance": false,
                        "name": "Sesión 2",
                        "stateSession": "NOT_STARTED",
                        "place": "Erandio",
                        "order": 5
                    },
                    {
                        "id": 9,
                        "startAt": "2023-09-22 13:27:00",
                        "finishAt": "2023-09-22 14:27:00",
                        "url": null,
                        "state": false,
                        "hourInit": "13:27:00",
                        "hourEnd": "14:27:00",
                        "assistance": false,
                        "name": "Sesión 3",
                        "stateSession": "NOT_STARTED",
                        "place": "Erandio",
                        "order": 6
                    }
                ]
            }
        ],
        "channels": {
            "forumChannel": null,
            "chatChannel": null
        },
        "infoAnnouncement": {
            "id": 128,
            "guideURL": null,
            "totalHours": 30
        },
        "isReadDidacticGuide": true,
        "configurations": {
            "SECTIONS": {
                "INFORMATION": true,
                "CONTENT": true,
                "OPINIONS": true,
                "TASK": false,
                "CHAT": false,
                "MATERIALS": false,
                "FORUM": false
             }
            "CRITERIA": {
                "MAXIMUM_INACTIVITY_TIME": 5
            }
        }
    }
}
```

## Configuraciones del curso

Este proceso activa ciertos paneles en la sección de detalles del curso. Existe una configuración predeterminada para los cursos, la cual puede ser ajustada cuando el curso se asigna a una convocatoria específica. La configuración básica se encuentra en el archivo `fundae.yaml` en la parte BACK del sistema.

```json
{
  "SECTIONS": {
    "INFORMATION": true,
    "CONTENT": true,
    "OPINIONS": true,
    "TASK": false,
    "CHAT": false,
    "MATERIALS": false,
    "FORUM": false
  }
}
```

![Docusaurus logo](/img/fundae/detail-course.png)
