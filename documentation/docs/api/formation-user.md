---
sidebar_position: 2
---

# Formación del usuario

La formación del usuario es una función que proporciona a los usuarios acceso a los cursos obligatorios que deben completar.

## Endpoint

- **URL:** `api/formation-user`
- **Método:** GET

## Controlador

- `ApiFormationUserController`

## Tipos de formación

Hay cuatro tipos de formación disponibles:

* **Itinerario:** Un itinerario es un conjunto de cursos que se organizan en un orden específico. Los itinerarios se asignan a los usuarios por su rol o función.
* **Teleformación:** La teleformación es un tipo de formación que se realiza a distancia, a través de Internet.
* **Presencial:** La formación presencial se realiza en un lugar físico, como un aula o un centro de formación.
* **Mixto:** Los cursos mixto es una combinación de teleformación y presencial

### Ejemplo de datos

La siguiente es una muestra de la respuesta de la API para la formación del usuario:

```json
{
    "status": 200,
    "error": false,
    "data": {
        "mixed": {
            "id": 3,
            "name": "Mixto",
            "description": "Es una combinación entre Teleformación y Presencial",
            "featured": true,
            "courses": [
                {
                    "id": 324,
                    "code": "002",
                    "name": "Mixto",
                    "description": "<p>Es un curso mixto</p>",
                    "image": null,
                    "thumbnail": null,
                    "tags": [],
                    "segments": [],
                    "locale": "es",
                    "new": false,
                    "itinerary": false,
                    "active": true,
                    "progress": 0,
                    "isAnnouncement": true,
                    "extraAnnouncement": {
                        "idAnnouncement": 128,
                        "startAt": "2023-09-12 13:22:00",
                        "finishAt": "2023-09-23 13:22:00",
                        "nextSessionAt": "2023-09-15 13:23:00",
                        "state": "IN_PROGRESS"
                    },
                    "finished": false,
                    "valued": false,
                    "state": false,
                    "startAt": null,
                    "finishAt": null,
                    "courseCurrentlyViewed": false
                }
            ]
        }
    }
}

```
