---
sidebar_position: 1
---


# Secciones
Las secciones se configuran a través del panel de administración. Estas secciones incluyen una lista de categorías, de las cuales se extraen los cursos disponibles de dos tipos: cursos libres y cursos asignados a los usuarios según ciertos filtros.

## Endpoint `Listado de secciones`

- **`URL`:** api/sections
- **`Método`:** GET
- **`Controlador`:** ApiSectionController

> Si no se han configurado secciones en el panel de administración, se mostrará una sección predeterminada.

### ¿Cómo se muestran las secciones a los usuarios?
La sección de `Formación Adicional` solo se mostrará a los usuarios que tengan habilitada la opción de "Campus Abierto" en su perfil. Esta sección se crea automáticamente según las configuraciones del usuario.

La `Formación por Filtro` se asigna a los usuarios que tienen el nivel de acceso correspondiente a ciertos cursos. 

>Es importante destacar que la sección de `Formación por Filtro` desaparecerá si se crea una nueva sección en el panel de administración. Esta sección solo existe cuando no se ha configurado ninguna sección en el sistema.


## Detalle de una sección
## Endpoint 

- **`URL`:** /sections/{id}
    - **`id`** Es el objeto de la entidad `Section`
- **`Método`:** GET
- **`Controlador`:** ApiSectionController