# Noticias de Formación Obligatoria

En el contexto de la formación obligatoria, se generan noticias utilizando fórmulas específicas. Estas noticias tienen como objetivo informar a los estudiantes sobre los próximos cursos que deben comenzar y los que están a punto de finalizar en la convocatoria.

## Endpoint

- **URL:** `/api/formation-user`
- **M<PERSON>todo:** `GET`
- **Controlador:** `ApiFormationUserController`

## Lógica para la Generación de Noticias

La responsabilidad de generar estas noticias recae en un servicio especializado que se encuentra en la siguiente ubicación:

- **Ruta:** `services/Fundae/Api/AnnouncementNewsService.php`

Este servicio se encarga de recopilar y procesar la información necesaria para crear las noticias y presentarlas de manera clara y concisa a los estudiantes.

## <PERSON>je<PERSON><PERSON> de <PERSON>

```json
{
    "itineraries":[],
    "teleformation":[],
    "presential":[],
    "mixed":[],
    "virtualClass":[],
    "news": [
           {
                "title": "Curso de cocina",
                "text": "¡El curso Curso de cocina falta poco para que inicie!",
                "imageUrl": "http://127.0.0.1:8000/626af7c7cf5c2215856660.jpg",
                "datetime": "2023-09-21 09:09:00",
                "time": "3 day(s) 21 hour(s) 58 minute(s)",
                "type": "START"
            },
            {
                "title": "Curso convocatoria",
                "text": "¡El curso Curso convocatoria falta poco para que finalice!",
                "imageUrl": null,
                "datetime": "2023-09-22 12:12:00",
                "time": "1 day(s) 22 hour(s) 1 minute(s)",
                "type": "END"
            },
            {
                "title": "Introducción a los simuladores empresariales",
                "text": "¡El curso Introducción a los simuladores empresariales falta poco para que finalice!",
                "imageUrl": "http://127.0.0.1:8000/610170f136e8a816427074.png",
                "datetime": "2023-09-23 11:38:00",
                "time": "2 day(s) 21 hour(s) 27 minute(s)",
                "type": "END"
            },
            {
                "title": "New testing",
                "text": "¡El curso New testing falta poco para que finalice!",
                "imageUrl": "http://127.0.0.1:8000/4cb6d77b571b7df3ad3c1691661583.jpeg",
                "datetime": "2023-09-21 17:58:00",
                "time": "1 day(s) 3 hour(s) 47 minute(s)",
                "type": "END"
            }
    ]
}
```

Dentro del `news` llegan dos tipos:

- **start:** Llegan aquellos cursos que estan asignados a convocatorias que estan a punto de iniciar.
- **end:** Llegan aquellos cursos que estan asignados a convocatorias y estan a punto de finalizar.

```json
 {
    "title": "New testing",
    "text": "¡El curso New testing falta poco para que finalice!",
    "imageUrl": "http://127.0.0.1:8000/4cb6d77b571b7df3ad3c1691661583.jpeg",
    "datetime": "2023-09-21 17:58:00",
    "time": "1 day(s) 3 hour(s) 47 minute(s)",
    "type": "END"
} 
```

### Entender la data de noticias
- **`title`:** Es el nombre del curso
- **`text`:** Es una descripcion generica ya sea si inicia o finaliza el curso
- **`datetime`:** LLega la fecha que inica o finaliza la convocatoria
- **`time`:** Es tiempo que hace falta para iniciar o finalizar la convocatoria
- **`type`:** Llegan dos tipos
    - **`START:`** Son aquellos curso de convocatoria que todavia no ha iniciado
     - **`END:`** Son aquellos curso de convocatoria que estan cerca de finalizar