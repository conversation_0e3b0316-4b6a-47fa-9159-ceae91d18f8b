---
sidebar_position: 6
---

# Guía didáctica 

La Guía Didáctica es un recurso fundamental que se adjunta en las convocatorias para ayudar a los usuarios a comprender y seguir las pautas del curso. Además, se utiliza para recopilar datos estadísticos y realizar un seguimiento del progreso de los tutores. A través de un endpoint específico, podemos determinar si el usuario ha leído o descargado esta guía.

## Endpoint

- **URL:** `/api/guide-didactic/{id}`
    - **id:** Identificador de la convocatoria.
- **Parámetros:**
    - **isDownload:** Valor booleano que indica si el usuario ha descargado (true) o no (false) la guía didáctica.
- **Método:** POST
- **Controlador:** `ApiFormationUserController`

> **Nota:** El parámetro `isDownload` se utiliza para controlar si el usuario ha descargado o leído la guía didáctica.

## Ejemplo de Respuesta

Aquí se muestran ejemplos de respuestas posibles para este endpoint:

### Actualización Exitosa

En caso de una actualización exitosa del estado de la guía didáctica, la respuesta será la siguiente:

```json
{
    "status": 200,
    "error": false,
    "data": "Se ha actualizado el estado de la guía didáctica."
}
```


### Modal para que el Usuario Lea la Guía Didáctica
En el detalle del curso, se envía un campo isReadDidacticGuide con un valor booleano (true o false) que permite saber si el usuario ha leído la guía didáctica. La respuesta se vería de la siguiente manera:
```json
{
    "status": 200,
    "error": false,
    "data": {
        "course": {
            // Información detallada del curso
        },
        "isReadDidacticGuide": true
    }
}
```