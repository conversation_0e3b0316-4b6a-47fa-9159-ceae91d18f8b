---
sidebar_position: 1
---
# GenericToken
# [GenericToken](../../src/Entity/GenericToken.php)

Ejecutar acciones en dependencia de un token que puede o no tener fecha de vencimiento
dependiendo de la intención de donde se llame.

Cuenta con 2 campos obligatorios:
1. `type` Hacer referencia a una tabla/módulo, debe ser único a nivel del token. Por
    ejemplo: `type=survey` cuando se trata de encuestas o `type=announcement` cuando el 
    token tiene relación con una convocatoria. Recomendable declarar como constantes los tipos
    que se va a soportar:
    ```php
   class GenericToken {
        public const TYPE_UNIQUE_NAME = '<value>';
   }
   ```
2. `entityId` Referencia a la tabla/objeto con base a la lógica local de donde se mande a llamar
    `GenericToken`. No requerido que sea único, afectado al uso local.
3. `extra` Un array de parámetros adicionales. Entendibles de acuerdo a la lógica local.
4. `validFrom` y `validUntil` son el rango de fechas en la que el tóken es válido. Si ambos
    están vacíos, el token no tiene fechas de vencimiento, solo se puede negar con la propiedad 
    `revoked`
5. `revoked` Dar un token por inválido
6. `token` SHA-256 generado en el método `generateToken()` de [GenericToken](../../src/Entity/GenericToken.php)

## Servicio [GenericTokenService](../../src/Service/GenericTokenService.php)

Solo existe el método `handleGenericToken` que recibe como parámetro un token de tipo string. Agregar 
llamadas y condiciones de acuerdo a requerimientos.

## Api
Hacer una llamada post a `/api/generic-token` con body `token=<value>`
Retorna un json
```json
{
  "error": true|false,
  "data": {
    "type": "message|redirect",
    "value": "<value>"
  }
}
```
Si `error=true`, `data` es el mensaje de error.
