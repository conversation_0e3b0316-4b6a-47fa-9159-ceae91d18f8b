# Firma digital.

La firma digital se emplea en convocatorias presenciales con el propósito de verificar la asistencia del estudiante. No obstante, su aplicación requiere que se haya configurado adecuadamente en el perfil de administrador.

La información relacionada con la firma digital se encuentra almacenada en la tabla `configurationClientAnnouncement` y se identifica mediante el valor `DIGITAL_SIGNATURE`.

## Funcionamiento:

- **Primer nivel:** La configuración se aplica a nivel de cliente. En esta etapa, se establecen las preferencias y parámetros iniciales que afectarán el resto del proceso.

- **Segundo nivel:** Esta fase depende de si el cliente ha aplicado la configuración mencionada. Al crear o configurar la convocatoria, se toma la decisión sobre si los estudiantes deben aplicar su firma digital. Esta elección influirá en la dinámica de las sesiones.

- **Tercer nivel:** La ejecución depende del código QR generado. Si se ha implementado la firma digital, el sistema automáticamente detectará el estado de la sesión y determinará si es necesario que los estudiantes apliquen su firma. Este enfoque busca simplificar el proceso y optimizar la gestión de sesiones, asegurando que la firma sea requerida solo cuando sea pertinente.

## Módelo relacional

![Docusaurus logo](/img/fundae/digital_signature.png)

## Endpoint

- **URL:** `/api/digital-signature/announcement-user`
- **Parámetros:**
  - **idAnnouncement:** Es el id de la convocatoria
  - **digitalSignature:** Es una imagen que se recibe en base64
- **Método:** POST
- **Controlador:** `ApiDigitalSignatureController`

### Ejemplo data que se envia

```json
{
  "idAnnouncement": 474,
  "digitalSignature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII="
}
```

### Data curso
```json
{
    "status": 200,
    "error": false,
    "data": {
        "course": {
            "id": 355,
            "code": "presencial",
            "name": "Limpieza de camas",
            "description": "<p>probar presencial</p>",
            "categories": [],
            "image": "f62a15ec36f5a22ae0781700732146.jpeg",
            "category": {
                "id": 1,
                "name": "Tu itinerario formativo"
            },
            "generalInformation": "",
            "seasons": [
                {
                    "id": 1,
                    "name": "annoucement",
                    "type": "announcement",
                    "chapters": [
                        {
                            "id": 234,
                            "startAt": "2024-01-19 21:12:00",
                            "finishAt": "2024-01-19 23:00:00",
                            "url": null,
                            "state": true,
                            "hourInit": "21:12",
                            "hourEnd": "23:00",
                            "entryQrToken": "http://127.0.0.1:8000/campus?generic-token=c7ec2a9059f90efadef247153d24879d73ff8183278ec680cd81d5f7438c6f4a",
                            "exitQrToken": "http://127.0.0.1:8000/campus?generic-token=a2128c9578f627c3dfefd260110abe0c14a5a88462255e1b933d956495489fe3",
                            "entryMargin": 5,
                            "exitMargin": 5,
                            "entryMarginMaxTime": "21:17",
                            "exitMarginMinTime": "22:55",
                            "sessionTimeInMinutes": 108,
                            "timezone": "Europe/Madrid",
                            "timezoneOffset": "",
                            "place": "Erandio",
                            "assistance": false,
                            "name": "Sesión 1",
                            "stateSession": "IN_PROCESS",
                            "hasDigitalSignature": true,
                            "order": 1
                        },
                        {
                            "id": 235,
                            "startAt": "2024-01-20 21:01:00",
                            "finishAt": "2024-01-20 22:01:00",
                            "url": null,
                            "state": false,
                            "hourInit": "21:01",
                            "hourEnd": "22:01",
                            "entryQrToken": "http://127.0.0.1:8000/campus?generic-token=9dc69098119b7a494b82ea5f33952eab0e3fcff0c7392e06fda1411d8ee68f0c",
                            "exitQrToken": "http://127.0.0.1:8000/campus?generic-token=b92a4298f06f9cf41284701e4a20414614698d2ccf69efc1fec70e547ecb539f",
                            "entryMargin": 5,
                            "exitMargin": 5,
                            "entryMarginMaxTime": "21:04",
                            "exitMarginMinTime": "21:58",
                            "sessionTimeInMinutes": 60,
                            "timezone": "Europe/Madrid",
                            "timezoneOffset": "",
                            "place": "Erandio",
                            "assistance": false,
                            "name": "Sesión 2",
                            "stateSession": "NOT_STARTED",
                            "hasDigitalSignature": false,
                            "order": 2
                        },
                        {
                            "id": 236,
                            "startAt": "2024-01-21 20:01:00",
                            "finishAt": "2024-01-21 21:01:00",
                            "url": null,
                            "state": false,
                            "hourInit": "20:01",
                            "hourEnd": "21:01",
                            "entryQrToken": "http://127.0.0.1:8000/campus?generic-token=52ebc2a6e2058102c0427346021f2f75ea07d56400c3b3e8e852be16dc926268",
                            "exitQrToken": "http://127.0.0.1:8000/campus?generic-token=f0c11e0c82cb2def7d2502e14dd0633b6e88f89195897f7c367f8d1dacc8e2da",
                            "entryMargin": 5,
                            "exitMargin": 5,
                            "entryMarginMaxTime": "20:04",
                            "exitMarginMinTime": "20:58",
                            "sessionTimeInMinutes": 60,
                            "timezone": "Europe/Madrid",
                            "timezoneOffset": "",
                            "place": "Erandio",
                            "assistance": false,
                            "name": "Sesión 3",
                            "stateSession": "NOT_STARTED",
                            "hasDigitalSignature": false,
                            "order": 3
                        }
                    ]
                }
            ],
            "documentation": null,
            "new": false,
            "imageUrl": "uploads/images/course/f62a15ec36f5a22ae0781700732146.jpeg",
            "thumbnail": "http://127.0.0.1:8000/media/cache/resolve/thumb/uploads/images/course/f62a15ec36f5a22ae0781700732146.jpeg",
            "hasOwnQuestions": false,
            "typeCourse": {
                "id": 2,
                "name": "Presencial"
            }
        },
        "points": null,
        "userCourseId": "",
        "finished": null,
        "valued": false,
        "opinions": [],
        "averageTime": {
            "countUsers": null,
            "SUM( c.time )": null,
            "averageTime": null,
            "minutos": null
        },
        "progress": 0,
        "itineraries": [],
        "announcement": 474,
        "materials": [],
        "tasks": [],
        "messages": [],
        "tutor": {
            "id": 66,
            "firstName": "Ander",
            "lastName": "Regalado",
            "avatar": "uploads/users/avatars/6246d7d8e1ea6651478716.png"
        },
        "seasons": [
            {
                "id": 1,
                "name": "annoucement",
                "type": "announcement",
                "chapters": [
                    {
                        "id": 234,
                        "startAt": "2024-01-19 21:12:00",
                        "finishAt": "2024-01-19 23:00:00",
                        "url": null,
                        "state": true,
                        "hourInit": "21:12",
                        "hourEnd": "23:00",
                        "entryQrToken": "http://127.0.0.1:8000/campus?generic-token=c7ec2a9059f90efadef247153d24879d73ff8183278ec680cd81d5f7438c6f4a",
                        "exitQrToken": "http://127.0.0.1:8000/campus?generic-token=a2128c9578f627c3dfefd260110abe0c14a5a88462255e1b933d956495489fe3",
                        "entryMargin": 5,
                        "exitMargin": 5,
                        "entryMarginMaxTime": "21:17",
                        "exitMarginMinTime": "22:55",
                        "sessionTimeInMinutes": 108,
                        "timezone": "Europe/Madrid",
                        "timezoneOffset": "",
                        "place": "Erandio",
                        "assistance": false,
                        "name": "Sesión 1",
                        "stateSession": "IN_PROCESS",
                        "hasDigitalSignature": true,
                        "order": 1
                    },
                    {
                        "id": 235,
                        "startAt": "2024-01-20 21:01:00",
                        "finishAt": "2024-01-20 22:01:00",
                        "url": null,
                        "state": false,
                        "hourInit": "21:01",
                        "hourEnd": "22:01",
                        "entryQrToken": "http://127.0.0.1:8000/campus?generic-token=9dc69098119b7a494b82ea5f33952eab0e3fcff0c7392e06fda1411d8ee68f0c",
                        "exitQrToken": "http://127.0.0.1:8000/campus?generic-token=b92a4298f06f9cf41284701e4a20414614698d2ccf69efc1fec70e547ecb539f",
                        "entryMargin": 5,
                        "exitMargin": 5,
                        "entryMarginMaxTime": "21:04",
                        "exitMarginMinTime": "21:58",
                        "sessionTimeInMinutes": 60,
                        "timezone": "Europe/Madrid",
                        "timezoneOffset": "",
                        "place": "Erandio",
                        "assistance": false,
                        "name": "Sesión 2",
                        "stateSession": "NOT_STARTED",
                        "hasDigitalSignature": false,
                        "order": 2
                    },
                    {
                        "id": 236,
                        "startAt": "2024-01-21 20:01:00",
                        "finishAt": "2024-01-21 21:01:00",
                        "url": null,
                        "state": false,
                        "hourInit": "20:01",
                        "hourEnd": "21:01",
                        "entryQrToken": "http://127.0.0.1:8000/campus?generic-token=52ebc2a6e2058102c0427346021f2f75ea07d56400c3b3e8e852be16dc926268",
                        "exitQrToken": "http://127.0.0.1:8000/campus?generic-token=f0c11e0c82cb2def7d2502e14dd0633b6e88f89195897f7c367f8d1dacc8e2da",
                        "entryMargin": 5,
                        "exitMargin": 5,
                        "entryMarginMaxTime": "20:04",
                        "exitMarginMinTime": "20:58",
                        "sessionTimeInMinutes": 60,
                        "timezone": "Europe/Madrid",
                        "timezoneOffset": "",
                        "place": "Erandio",
                        "assistance": false,
                        "name": "Sesión 3",
                        "stateSession": "NOT_STARTED",
                        "hasDigitalSignature": false,
                        "order": 3
                    }
                ]
            }
        ],
        "channels": {
            "forumChannel": null,
            "chatChannel": null
        },
        "infoAnnouncement": {
            "id": 474,
            "guideURL": "http://127.0.0.1:8000/assets_announcement/didactic_guide//65a9bd5858194841979516.pdf",
            "totalHours": 10
        },
        "isReadDidacticGuide": false,
        "configurations": {
            "SECTIONS": {
                "INFORMATION": true,
                "CONTENT": true,
                "OPINIONS": true,
                "TASK": false,
                "CHAT": false,
                "MATERIALS": false,
                "FORUM": false
            },
            "CRITERIA": {
                "MAXIMUM_INACTIVITY_TIME": 5
            }
        },
        "hasDigitalSignature": true
    }
}
```

### Explicando la data
- **hasDigitalSignature:** Esto es a nivel de curso, nos sirve para abrir automáticamente la modal cuando usuario haga uso del código QR, o acceda 
al detalle del curso.

### Data de las sesiones
```json
 {
                        "id": 234,
                        "startAt": "2024-01-19 21:12:00",
                        "finishAt": "2024-01-19 23:00:00",
                        "url": null,
                        "state": true,
                        "hourInit": "21:12",
                        "hourEnd": "23:00",
                        "entryQrToken": "http://127.0.0.1:8000/campus?generic-token=c7ec2a9059f90efadef247153d24879d73ff8183278ec680cd81d5f7438c6f4a",
                        "exitQrToken": "http://127.0.0.1:8000/campus?generic-token=a2128c9578f627c3dfefd260110abe0c14a5a88462255e1b933d956495489fe3",
                        "entryMargin": 5,
                        "exitMargin": 5,
                        "entryMarginMaxTime": "21:17",
                        "exitMarginMinTime": "22:55",
                        "sessionTimeInMinutes": 108,
                        "timezone": "Europe/Madrid",
                        "timezoneOffset": "",
                        "place": "Erandio",
                        "assistance": false,
                        "name": "Sesión 1",
                        "stateSession": "IN_PROCESS",
                        "hasDigitalSignature": true,
                        "order": 1
                    },
```
- **hasDigitalSignature:** Cuando llega esta opción es que el usuario esta pendiente de aplicar la firma para esa sesión
