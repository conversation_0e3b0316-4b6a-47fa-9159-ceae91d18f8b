---
sidebar_position: 2
---

# Validación de asistencia
Los códigos QR dentro de Fundae se utilizan para implementar una doble autenticación, específicamente para validar la asistencia de los usuarios a los cursos. Dos posibles escenarios en los que se emplea esta funcionalidad son los siguientes:

**Primer escenario**: Durante una sesión de curso, el profesor proyecta una presentación en la que explica que para registrar su asistencia, los estudiantes deben escanear un código QR. Este código redirige a los estudiantes a la plataforma Campus, donde deben iniciar sesión. Es en este punto donde se registra si el estudiante asistió o no.

**Segundo escenario**: En lugar de utilizar una hoja física para que los estudiantes firmen o anoten su asistencia, el profesor proporciona una hoja con un código QR. Los estudiantes pueden simplemente escanear este código utilizando sus dispositivos móviles para validar su asistencia de manera rápida y sencilla.

Ambos escenarios ofrecen una alternativa digital y eficiente para llevar un registro de la asistencia de los estudiantes en los cursos, eliminando la necesidad de utilizar papel y firmas manuales. Además, proporcionan un proceso de verificación más seguro y automatizado.


## Generar código QR
Para cada sesión se generá una url, esto nos permitirá controlar la validación de asistencia por parte del usuario.

### Proceso de Validación:

1. El tutor muestra el código QR a los estudiantes en la sesión.

2. Los estudiantes escanean el código QR utilizando sus dispositivos móviles.

3. Al escanear el código QR, los estudiantes son redirigidos a la plataforma Campus.

4. La plataforma valida la asistencia del estudiante en función de que hayan escaneado el código QR.

5. La asistencia se registra como válida una vez que el código QR ha sido escaneado, independientemente de si el estudiante llega hasta el detalle del curso.

> Este proceso asegura que la asistencia se considere válida únicamente cuando el estudiante ha realizado el escaneo del código QR. No es necesario que lleguen al detalle del curso para que su asistencia sea registrada, ya que el simple acto de escanear el código QR indica su presencia en la sesión. Esto aborda la preocupación de que un estudiante pueda acceder desde casa sin escanear el código QR y garantiza que solo aquellos presentes en la sesión sean contabilizados como asistentes.

