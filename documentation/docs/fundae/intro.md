---
sidebar_position: 1
---

# ¿Qué es Fundae?
Es un módulo diseñado para facilitar la gestión de convocatorias. Una convocatoria en este contexto se refiere a un evento o programa de formación que una organización ofrece a sus usuarios o estudiantes. El módulo Fundae se utiliza para configurar y administrar diferentes aspectos relacionados con estas convocatorias, lo que incluye opciones como la habilitación de características específicas, la duración de la convocatoria y otros parámetros personalizables.

## Configuraciones
### Niveles
1. **Nivel de Cliente**: En este nivel, se pueden configurar los tipos de cursos que el cliente necesita gestionar, así como las configuraciones necesarias para las convocatorias.
2. **Nivel de Convocatoria Base**: Este nivel depende de la configuración del nivel de cliente. Aquí se pueden modificar las configuraciones globales definidas a nivel de cliente. Por ejemplo, si se ha habilitado la función de "COMUNICACIÓN" a nivel de cliente, en este nivel se pueden activar o desactivar características específicas como "CHAT", "FOROS" y más. También se pueden ajustar parámetros relacionados con la duración y la interacción entre tutores y estudiantes en las convocatorias.
3. **Nivel de Convocatoria Individual**: En este nivel, los usuarios pueden crear configuraciones individuales para convocatorias específicas. Esto permite adaptar aún más las características y configuraciones a las necesidades particulares de cada convocatoria en particular. Aquí se pueden agregar o quitar características según las necesidades específicas de cada programa de formación.

## Nivel Cliente
Para una fácil gestión se han creado unos fixtures por defecto, que permiten obtener el catálogo necesario para arrancar el módulo. La siguiente se guarda en una tabla con el nombre de `ConfigurationClientAnnouncement`

```json
 [
    {
        "id": 1,
        "name": "COMMUNICATION",
        "description": "Esto permite habilitar las comunicaciones dentro de la convocatoria",
        "active": true,
        "extra": {}
    },
    {
        "id": 2,
        "name": "CERTIFICATE",
        "description": "Esto permite habilitar la descarga de diplomas dentro de la convocatoria",
        "active": true,
        "extra": {}
    },
    {
        "id": 3,
        "name": "SURVEY",
        "description": "Esto permite habilitar las encuenstas, para ser heredadas en la convocatoria",
        "active": true,
        "extra": {}
    },
    {
        "id": 4,
        "name": "ALERT",
        "description": "Esto permite habilitar las alertas, para ser heredadas en el apartado alertas del tutor",
        "active": true,
        "extra": {}
    },
    {
        "id": 5,
        "name": "TEMPORALIZATION",
        "description": "Permite habilitar la temporalización de los capítulos dentro de una convocatoria",
        "active": true,
        "extra": {}
    },
    {
        "id": 6,
        "name": "BONIFICATION",
        "description": "Bonificación de la convocatoria, especial para la convocatoria de la fundación tripartita",
        "active": true,
        "extra": {}
    },
    {
        "id": 7,
        "name": "ACCESS_CONTENT",
        "description": "Permite habilitar el acceso al contenido de la convocatoria una vez finalizada",
        "active": true,
        "extra": {}
    }
]
```

## Nivel Convocatoria Base
Según las configuraciones definidas en el "Nivel de Cliente", se pueden realizar modificaciones a nivel de convocatoria. Por ejemplo, si se ha habilitado "COMUNICACIÓN" a nivel de cliente, se pueden activar funciones como "CHAT" y "FOROS".

Estas configuraciones permiten controlar el período de vida de la convocatoria y mejoran la interacción entre tutores y estudiantes.

```json
[
    {
        "id": 1,
        "name": "Temporalización",
        "description": "Para facilitar el seguimiento del curso, asignaremos un tiempo a cada bloque de contenidos y o actividades, pudiendo así detectar cuáles de los participantes llevan un ritmo de trabajo adecuado o se están rezagando en el proceso de formación",
        "active": true,
        "image": "temporalization.svg",
        "nameConfiguration": "TEMPORALIZATION"
    },
    {
        "id": 2,
        "name": "Curso bonificado",
        "description": "Los cursos bonificados son aquellos que se realizan a través de la Fundación Tripartita y que son financiados por las empresas a través de las cotizaciones a la Seguridad Social.",
        "active": true,
        "image": "bonification.svg",
        "nameConfiguration": "BONIFICATION"
    },
    {
        "id": 3,
        "name": "Chat",
        "description": "El chat es una herramienta de comunicación síncrona que permite a los participantes de un curso interactuar en tiempo real, a través de mensajes de texto.",
        "active": true,
        "image": "chat.svg",
        "nameConfiguration": "COMMUNICATION"
    },
    {
        "id": 4,
        "name": "Notificaciones",
        "description": "Las notificaciones son mensajes que se envían a los participantes de un curso para informarles de novedades o eventos importantes.",
        "active": true,
        "image": "notification.svg",
        "nameConfiguration": "COMMUNICATION"
    },
    {
        "id": 5,
        "name": "Mensajeria",
        "description": "Una mensajería es un sistema de comunicación que permite a los participantes de un curso enviar y recibir mensajes privados.",
        "active": true,
        "image": "message.svg",
        "nameConfiguration": "COMMUNICATION"
    },
    {
        "id": 6,
        "name": "Foros",
        "description": "Un foro es un espacio de comunicación asíncrona que permite a los participantes de un curso intercambiar mensajes sobre un tema determinado.",
        "active": true,
        "image": "foro.svg",
        "nameConfiguration": "COMMUNICATION"
    },
    {
        "id": 7,
        "name": "Diploma",
        "description": "Los diplomas son certificados que se entregan a los participantes de un curso para acreditar su realización.",
        "active": true,
        "image": "diploma.svg",
        "nameConfiguration": "CERTIFICATE"
    },
    {
        "id": 8,
        "name": "Activar alertas para el tutor",
        "description": "Las alertas son mensajes que se envían al tutor de un curso para informarle de novedades o eventos importantes.",
        "active": true,
        "image": "alert.svg",
        "nameConfiguration": "ALERT"
    },
    {
        "id": 9,
        "name": "Encuesta de satisfacción",
        "description": "Las encuestas de satisfacción son cuestionarios que se envían a los participantes de un curso para conocer su opinión sobre el mismo.",
        "active": true,
        "image": "survey.svg",
        "nameConfiguration": "SURVEY"
    },
    {
        "id": 10,
        "name": "El curso permanecerá activo al finalizar la convocatoria",
        "description": "El usuario podrá acceder al contenido del curso una vez finalizada la convocatoria.",
        "active": true,
        "image": "access_content.svg",
        "nameConfiguration": "ACCESS_CONTENT"
    }
]

```
## Nivel Convocatoria Individual
En este nivel, la configuración es completamente personalizable y se adapta a las necesidades específicas del cliente.


