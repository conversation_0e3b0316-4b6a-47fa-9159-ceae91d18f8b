---
sidebar_position: 1
---

# Proveedores

1. **Zoom**
2. **ClickMeeting**
3. **Jitsi**

Si estás configurando un nuevo entorno o necesitas mantener tus datos actualizados en la base de datos, te recomendamos actualizar las fixtures con la última información.

## Datos en Formato JSON

Puedes encontrar los datos en formato JSON a continuación:

```json
[
  { "id": 1, "name": "Zoom", "state": 1 },
  { "id": 2, "name": "ClickMeeting", "state": 1 },
  { "id": 3, "name": "Jitsi", "state": 1 }
]
```

- **State:** El campo State se utiliza para activar o desactivar la funcionalidad de una plataforma para un cliente específico. Aquí están los valores posibles:.
  - **Valor 1:** El estado esta activo
  - **Valor 2:** El esto es inactivo

### Recomendación

Si necesitas actualizar los catálogos de plataformas de reuniones, puedes utilizar el comando `symfony console c:u` para mantener tu base de datos actualizada y asegurarte de que todos los datos estén al día.

### Actualizar solo los catálogos de reuniones

`symfony console d:f:l --group ClassroomvirtualTypeFixtures --append `

### Servicios

![Docusaurus logo](/img/fundae/Services_classroom.png)

### Diagrama que muestra el proceso de creación

![Docusaurus logo](/img/fundae/create_virtual_class.png)

## Crear aula

- **ClassroomvirtualService:** El servicio denominado "ClassroomvirtualService" tiene la responsabilidad de gestionar la solicitud de ayuda para
  la creación de un aula virtual correspondiente a una clase específica.
  Este servicio se encuentra ubicado en la siguiente ruta.

  - **PATH:** `Services/VirtualClass/ClassroomvirtualService`
  - **METHOD:**`createRoom(array $parameters)`
  - **PARÁMETROS:**
    - **sessionNumber:** Numero de la sesión
    - **idAnnouncementGroup:** Id del grupo de la convocatoria
    - **idTypeClassroom:** El id del tipo de clase que se va a crear, puede ser zoom, clickmeeting entre otros,
    - **idAnnouncementGroupSession:** id del grupo de la sesión,
    - **idClassroomvirtual:** si este campo no llega se entiende que es una nueva sala la que se creará
    - **name:** El nombre que daremos a la sala
    - **description:** Descripcion de la sala
    - **startsat:** Fecha de inicio de la reunion
    - **duration:** Tiempo que tardará la reunión
    - **timezone:** Zona horaria donde se celebrará la reunion

  ```json
  {
    "sessionNumber": 1,
    "idAnnouncementGroup": 9,
    "idTypeClassroom": 1,
    "idClassroomvirtual": 80,
    "idAnnouncementGroupSession": 7,
    "name": "prueba creación meeting",
    "description": "prueba creación meeting",
    "startsat": "2023-10-10T6:30:00",
    "duration": 60,
    "timezone": "America/Los_Angeles"    
  }
  ```

### Modificar aula

Durante el proceso de creación el usuario decide cambiar de proveedor. Cuando llega el siguiente campo se entiende que el aula virtual se esta modificando.

- **idClassroomvirtual:** 80
- **idTypeClassroom**: 1

> En caso de que el usuario decida cambiar de proveedor, el servicio se encargará de detectar si dicha modificación es necesaria. Si no se requiere un cambio de proveedor, el servicio procederá a realizar modificaciones únicamente en el aula virtual que se creó originalmente

### Ejemplo de respuesta

```json
{
  "tutor_url": "https://zoom.us/s/93112783288?zak=eyJ0eXAiOiJKV1QiLCJzdiI6IjAwMDAwMSIsInptX3NrbSI6InptX28ybSIsImFsZyI6IkhTMjU2In0.eyJhdWQiOiJjbGllbnRzbSIsInVpZCI6IldJcy12SWZ6UzNLS0xmd09mV1hZU3ciLCJpc3MiOiJ3ZWIiLCJzayI6IjAiLCJzdHkiOjEwMCwid2NkIjoiYXcxIiwiY2x0IjowLCJtbnVtIjoiOTMxMTI3ODMyODgiLCJleHAiOjE2OTYzNDU5NzgsImlhdCI6MTY5NjMzODc3OCwiYWlkIjoiMVgyVDRic1NUR0tZYjZ5RVFORlBZUSIsImNpZCI6IiJ9.kE8CoQWOabm9ACyvScnBAixmgLAMFnlp9WfXC0kPUmU"
}
```

Para cada usuario del grupo, se genera una URL única que les permite acceder a la sala. Cuando se llama a este servicio, se actualiza la tabla `AnnouncementGroupSession` con la URL del tutor, lo que facilita el acceso a la sala de reuniones.


### Eliminar aula
Cuando se está configurando un aula virtual, puede surgir la situación en la que un usuario cambie de proveedor de servicios. En tal caso, es necesario eliminar la reunión que se había configurado inicialmente.

 - **PATH:** `Services/VirtualClass/ClassroomvirtualService`
  - **METHOD:**`deleteRoom(array $parameters)`
  - **PARÁMETROS:**
    - **idClassroomvirtual:** Identificador de la aula virtual

La función de este método es identificar el tipo de aula virtual en uso y contactar al proveedor correspondiente para eliminar la reunión programada.

### Ejemplo de respuesta

```json
{
  "message": "The meeting was successfully deleted"
}
```

> Cuando se cree una sesión y se ha configurado con algún proveedor, y se decide eliminar  llamar al metodo `deleteRoom(array $parameters)`
