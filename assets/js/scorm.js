import '../css/scorm.scss';

import $ from 'jquery';

const APP_URL = window.location.protocol + '//' + window.location.host + "/";
const API_URL = APP_URL + 'api/';
const SCORMS_URL = APP_URL + 'uploads/scorms/';
const BEARER_TOKEN = 'Bearer ' + window.localStorage.getItem('user-token');


var errorCode = '';

var suspend = "";
var VERSION = "5.1.1";
var PREFERENCE_DEFAULT = 0;
var PREFERENCE_OFF = -1;
var PREFERENCE_ON = 1;
var LESSON_STATUS_PASSED = 1;
var LESSON_STATUS_COMPLETED = 2;
var LESSON_STATUS_FAILED = 3;
var LESSON_STATUS_INCOMPLETE = 4;
var LESSON_STATUS_BROWSED = 5;
var LESSON_STATUS_NOT_ATTEMPTED = 6;
var ENTRY_REVIEW = 1;
var ENTRY_FIRST_TIME = 2;
var ENTRY_RESUME = 3;
var MODE_NORMAL = 1;
var MODE_BROWSE = 2;
var MODE_REVIEW = 3;
var MAX_CMI_TIME = 36002439990;
var NO_ERROR = 0;
var ERROR_LMS = 1;
var ERROR_INVALID_PREFERENCE = 2;
var ERROR_INVALID_NUMBER = 3;
var ERROR_INVALID_ID = 4;
var ERROR_INVALID_STATUS = 5;
var ERROR_INVALID_RESPONSE = 6;
var ERROR_NOT_LOADED = 7;
var ERROR_INVALID_INTERACTION_RESPONSE = 8;
var EXIT_TYPE_SUSPEND = "SUSPEND";
var EXIT_TYPE_FINISH = "FINISH";
var EXIT_TYPE_TIMEOUT = "TIMEOUT";
var EXIT_TYPE_UNLOAD = "UNLOAD";
var INTERACTION_RESULT_CORRECT = "CORRECT";
var INTERACTION_RESULT_WRONG = "WRONG";
var INTERACTION_RESULT_UNANTICIPATED = "UNANTICIPATED";
var INTERACTION_RESULT_NEUTRAL = "NEUTRAL";
var INTERACTION_TYPE_TRUE_FALSE = "true-false";
var INTERACTION_TYPE_CHOICE = "choice";
var INTERACTION_TYPE_FILL_IN = "fill-in";
var INTERACTION_TYPE_LONG_FILL_IN = "long-fill-in";
var INTERACTION_TYPE_MATCHING = "matching";
var INTERACTION_TYPE_PERFORMANCE = "performance";
var INTERACTION_TYPE_SEQUENCING = "sequencing";
var INTERACTION_TYPE_LIKERT = "likert";
var INTERACTION_TYPE_NUMERIC = "numeric";
var DATA_CHUNK_PAIR_SEPARATOR = '###';
var DATA_CHUNK_VALUE_SEPARATOR = '$$';
var APPID = "HUVDTEKM9A";
var CLOUDURL = null;
var blnDebug = true;
var strLMSStandard = 'SCORM';
var DEFAULT_EXIT_TYPE = EXIT_TYPE_SUSPEND;
var AICC_LESSON_ID = "1";
var EXIT_BEHAVIOR = "SCORM_RECOMMENDED";
var EXIT_TARGET = "goodbye.html";
var LMS_SPECIFIED_REDIRECT_EVAL_STATEMENT = "";
var AICC_COMM_DISABLE_XMLHTTP = false;
var AICC_COMM_DISABLE_IFRAME = false;
var AICC_COMM_PREPEND_HTTP_IF_MISSING = true;
var AICC_REPORT_MIN_MAX_SCORE = true;
var SHOW_DEBUG_ON_LAUNCH = false;
var DO_NOT_REPORT_INTERACTIONS = false;
var SCORE_CAN_ONLY_IMPROVE = false;
var REVIEW_MODE_IS_READ_ONLY = false;
var AICC_RE_CHECK_LOADED_INTERVAL = 250;
var AICC_RE_CHECK_ATTEMPTS_BEFORE_TIMEOUT = 240;
var USE_AICC_KILL_TIME = true;
var AICC_ENTRY_FLAG_DEFAULT = ENTRY_REVIEW;
var AICC_USE_CUSTOM_COMMS = false;
var FORCED_COMMIT_TIME = "0";
var ALLOW_NONE_STANDARD = true;
var USE_2004_SUSPENDALL_NAVREQ = false;
var USE_STRICT_SUSPEND_DATA_LIMITS = false;
var EXIT_SUSPEND_IF_COMPLETED = true;
var EXIT_NORMAL_IF_PASSED = false;
var AICC_ENCODE_PARAMETER_VALUES = true;

var cmistring256 = "^[\\u0000-\\uFFFF]{0,255}$";
var cmistring4096 = "^[\\u0000-\\uFFFF]{0,4096}$";
var CMIString256 = cmistring256;
var CMIString4096 = cmistring4096;
var CMITime = '^([0-2]{1}[0-9]{1}):([0-5]{1}[0-9]{1}):([0-5]{1}[0-9]{1})(.[0-9]{1,2})?$';
var CMITimespan = '^([0-9]{2,4}):([0-9]{2}):([0-9]{2})(.[0-9]{1,2})?$';
var CMIInteger = '^\\d+$';
var CMISInteger = '^-?([0-9]+)$';
var CMIDecimal = '^-?([0-9]{0,3})(.[0-9]*)?$';
var CMIIdentifier = '^[\\u0021-\\u007E]{0,255}$';
var CMIFeedback = CMIString256;
var CMIIndex = '[._](\\d+).';
var CMIStatus = '^passed$|^completed$|^failed$|^incomplete$|^browsed$';
var CMIStatus2 = '^passed$|^completed$|^failed$|^incomplete$|^browsed$|^not attempted$';
var CMIExit = '^time-out$|^suspend$|^logout$|^$';
var CMIType = '^true-false$|^choice$|^fill-in$|^matching$|^performance$|^sequencing$|^likert$|^numeric$';
var CMIResult = '^correct$|^wrong$|^unanticipated$|^neutral$|^([0-9]{0,3})?(.[0-9]*)?$';
var NAVEvent = '^previous$|^continue$';
var cmi_children = 'core,suspend_data,launch_data,comments,objectives,student_data,student_preference,interactions';
var core_children = 'student_id,student_name,lesson_location,credit,lesson_status,entry,score,total_time,lesson_mode,exit,session_time';
var score_children = 'raw,min,max';
var comments_children = 'content,location,time';
var objectives_children = 'id,score,status';
var correct_responses_children = 'pattern';
var student_data_children = 'mastery_score,max_time_allowed,time_limit_action';
var student_preference_children = 'audio,language,speed,text';
var interactions_children = 'id,objectives,time,type,correct_responses,weighting,student_response,result,latency';
var score_range = '0#100';
var audio_range = '-1#100';
var speed_range = '-100#100';
var weighting_range = '-100#100';
var text_range = '-1#1';
var progress_id;

var def = [];
def[100] = {
    "student_name": "stu",
    "lesson_location": "less_loc",
    "lesson_status": "",
    "suspend ": "",
    "cmi.core.credit": 4,
    "cmi.core.student_id": 43847,
    "cmi.core.entry": "",
    "cmi.core.score.raw": "",
    "cmi.core.score.max": "",
    "cmi.core.score.min": "",
    "cmi.core.total_time": "",
    "cmi.core.lesson_mode": "",
    "cmi.core.exit": "",
    "cmi.suspend_data": "",
    "cmi.launch_data": "",
    "cmi.comments": "",
    "cmi.student_data.mastery_score": "",
    "cmi.student_data.max_time_allowed": "",
    "cmi.student_data.time_limit_action": "",
    "cmi.student_preference.audio": "",
    "cmi.student_preference.language": "",
    "cmi.student_preference.speed": "",
    "cmi.student_preference.text": ""
};

def[100]['cmi.suspend_data'] = ''; //'{"v":1,"d":[123,34,112,114,111,103,114,101,115,115,34,58,256,108,263,115,111,110,265,267,34,49,266,256,99,266,49,44,257,281,48,48,283,105,278,34,48,290,280,58,282,34,289,275,292,275,294,49,125,304,305,283,112,266,51,51,304]}';
def[100]['cmi.core.lesson_location'] = ''; //'index.html#/lessons/LiIs75L9VWExKsvBe9RCEHYwTBfc4jVE';
def[100]['cmi.location'] = ''; //'index.html#/lessons/LiIs75L9VWExKsvBe9RCEHYwTBfc4jVE';

window.datamodel = {};
for (var scoid in def) {
    datamodel[scoid] = {
        'cmi._children': {
            'defaultvalue': cmi_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi._version': {
            'defaultvalue': '3.4',
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.core._children': {
            'defaultvalue': core_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.core.student_id': {
            'defaultvalue': def[scoid]['cmi.core.student_id'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.core.student_name': {
            'defaultvalue': def[scoid]['cmi.core.student_name'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.core.lesson_location': {
            'defaultvalue': def[scoid]['cmi.core.lesson_location'],
            'format': CMIString256,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.location': {
            'defaultvalue': def[scoid]['cmi.location'],
            'format': CMIString256,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.core.credit': {
            'defaultvalue': def[scoid]['cmi.core.credit'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.core.lesson_status': {
            'defaultvalue': def[scoid]['cmi.core.lesson_status'],
            'format': CMIStatus,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.core.entry': {
            'defaultvalue': def[scoid]['cmi.core.entry'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.core.score._children': {
            'defaultvalue': score_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.core.score.raw': {
            'defaultvalue': def[scoid]['cmi.core.score.raw'],
            'format': CMIDecimal,
            'range': score_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.core.score.max': {
            'defaultvalue': def[scoid]['cmi.core.score.max'],
            'format': CMIDecimal,
            'range': score_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.core.score.min': {
            'defaultvalue': def[scoid]['cmi.core.score.min'],
            'format': CMIDecimal,
            'range': score_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.core.total_time': {
            'defaultvalue': def[scoid]['cmi.core.total_time'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.core.lesson_mode': {
            'defaultvalue': def[scoid]['cmi.core.lesson_mode'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.core.exit': {
            'defaultvalue': def[scoid]['cmi.core.exit'],
            'format': CMIExit,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.core.session_time': {
            'format': CMITimespan,
            'mod': 'w',
            'defaultvalue': '00:00:00',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.suspend_data': {
            'defaultvalue': def[scoid]['cmi.suspend_data'],
            'format': CMIString4096,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.launch_data': {
            'defaultvalue': def[scoid]['cmi.launch_data'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.comments': {
            'defaultvalue': def[scoid]['cmi.comments'],
            'format': CMIString4096,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.evaluation.comments._count': {
            'defaultvalue': '0',
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.evaluation.comments._children': {
            'defaultvalue': comments_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.evaluation.comments.n.content': {
            'defaultvalue': '',
            'pattern': CMIIndex,
            'format': CMIString256,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.evaluation.comments.n.location': {
            'defaultvalue': '',
            'pattern': CMIIndex,
            'format': CMIString256,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.evaluation.comments.n.time': {
            'defaultvalue': '',
            'pattern': CMIIndex,
            'format': CMITime,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.comments_from_lms': {
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.objectives._children': {
            'defaultvalue': objectives_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.objectives._count': {
            'mod': 'r',
            'defaultvalue': '0',
            'writeerror': '402'
        },
        'cmi.objectives.n.id': {
            'pattern': CMIIndex,
            'format': CMIIdentifier,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.objectives.n.score._children': {
            'pattern': CMIIndex,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.objectives.n.score.raw': {
            'defaultvalue': '',
            'pattern': CMIIndex,
            'format': CMIDecimal,
            'range': score_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.objectives.n.score.min': {
            'defaultvalue': '',
            'pattern': CMIIndex,
            'format': CMIDecimal,
            'range': score_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.objectives.n.score.max': {
            'defaultvalue': '',
            'pattern': CMIIndex,
            'format': CMIDecimal,
            'range': score_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.objectives.n.status': {
            'pattern': CMIIndex,
            'format': CMIStatus2,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.student_data._children': {
            'defaultvalue': student_data_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.student_data.mastery_score': {
            'defaultvalue': def[scoid]['cmi.student_data.mastery_score'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.student_data.max_time_allowed': {
            'defaultvalue': def[scoid]['cmi.student_data.max_time_allowed'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.student_data.time_limit_action': {
            'defaultvalue': def[scoid]['cmi.student_data.time_limit_action'],
            'mod': 'r',
            'writeerror': '403'
        },
        'cmi.student_preference._children': {
            'defaultvalue': student_preference_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.student_preference.audio': {
            'defaultvalue': def[scoid]['cmi.student_preference.audio'],
            'format': CMISInteger,
            'range': audio_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.student_preference.language': {
            'defaultvalue': def[scoid]['cmi.student_preference.language'],
            'format': CMIString256,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.student_preference.speed': {
            'defaultvalue': def[scoid]['cmi.student_preference.speed'],
            'format': CMISInteger,
            'range': speed_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.student_preference.text': {
            'defaultvalue': def[scoid]['cmi.student_preference.text'],
            'format': CMISInteger,
            'range': text_range,
            'mod': 'rw',
            'writeerror': '405'
        },
        'cmi.interactions._children': {
            'defaultvalue': interactions_children,
            'mod': 'r',
            'writeerror': '402'
        },
        'cmi.interactions._count': {
            'mod': 'r',
            'defaultvalue': '0',
            'writeerror': '402'
        },
        'cmi.interactions.n.id': {
            'pattern': CMIIndex,
            'format': CMIIdentifier,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.objectives._count': {
            'pattern': CMIIndex,
            'mod': 'r',
            'defaultvalue': '0',
            'writeerror': '402'
        },
        'cmi.interactions.n.objectives.n.id': {
            'pattern': CMIIndex,
            'format': CMIIdentifier,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.time': {
            'pattern': CMIIndex,
            'format': CMITime,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.type': {
            'pattern': CMIIndex,
            'format': CMIType,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.correct_responses._count': {
            'pattern': CMIIndex,
            'mod': 'r',
            'defaultvalue': '0',
            'writeerror': '402'
        },
        'cmi.interactions.n.correct_responses.n.pattern': {
            'pattern': CMIIndex,
            'format': CMIFeedback,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.weighting': {
            'pattern': CMIIndex,
            'format': CMIDecimal,
            'range': weighting_range,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.student_response': {
            'pattern': CMIIndex,
            'format': CMIFeedback,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.result': {
            'pattern': CMIIndex,
            'format': CMIResult,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'cmi.interactions.n.latency': {
            'pattern': CMIIndex,
            'format': CMITimespan,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        },
        'nav.event': {
            'defaultvalue': '',
            'format': NAVEvent,
            'mod': 'w',
            'readerror': '404',
            'writeerror': '405'
        }
    }
}

class ApiSco {
    constructor() {
        this.name = 'miApi';//data.name;
        this.datos = {};
        this.chapter_id = $("#scorm-player").data("chapter");
        this.start = Date.now();
    }

    LMSInitialize(name) {
        logTxt('LMSInitialize', name);
        return true;
    }

    LMSFinish(name) {
        logTxt('LMSFinish', '');
        delete this;
        return true;
    }

    Initialize() {
        return this.LMSInitialize('');
    }

    SetValue(name, value) {
        logTxt('Calling SetValue', '');
        logTxt(name, value);
        return this.LMSSetValue(name, value);
    }

    LMSSetValue(name, value) {
        var usr = $('#user option:selected').val();
        var idusrscrm = $('#ActvUsrScrm').val();
        var URL = API_URL + 'chapter/' + progress_id + '/update';
        let scorm = this.getScormVariables();
        scorm[name] = value;
        localStorage.setItem('scorm', JSON.stringify(scorm));
        var data = {
            param: name,
            value: value
        };
        $.ajax({
            contentType: 'application/json',
            url: URL,
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data),
            // data: {usr: usr, idusrscrm: idusrscrm, fld: name, vl: value},
            headers: {'Authorization': BEARER_TOKEN},
        })
            .done(function (data) {
                if (!data.error) {
                    logTxt('LMSSetValue(' + name + ',' + value + ') actualizado ', data);
                }
            }).fail(function () {
            logTxt('LMSSetValue(' + name + ',' + value + ') error ', '');
            return false;
        });

        const time = Date.now();
        const interval = Math.round((time - this.start) / 1000);

        if(interval > 10) {
            data = {
                time: interval
            }

            $.ajax({
                contentType: 'application/json',
                url: URL,
                type: 'POST',
                async: false,
                dataType: 'json',
                data: JSON.stringify(data),
                // data: {usr: usr, idusrscrm: idusrscrm, fld: name, vl: value},
                headers: {'Authorization': BEARER_TOKEN},
            })
                .done(function (data) {
                    if (!data.error) {
                        logTxt('LMSSetValue(' + name + ',' + value + ') actualizado ', data);
                    }
                }).fail(function () {
                logTxt('LMSSetValue(' + name + ',' + value + ') error ', '');
                return false;
            });

            this.start = time;
        }

        return true;

    }

    GetValue(name) {
        return this.LMSGetValue(name);
    }

    LMSGetValue(name) {

        let scorm = this.getScormVariables();

        let valor = scorm[name];
        return (valor === null || valor === undefined) ? '' : valor;
    }

    LMSCommit() {
        logTxt('LMSCommit', '');
        return true;
    }

    Commit() {
        logTxt('Commit (SCORM 2004)', '');
        return this.LMSCommit();
    }

    Terminate() {
        logTxt('Terminate (SCORM 2004)', '');
        return this.LMSFinish('');
    }

    LMSSetLastError(error) {
        logTxt('LMSGetValue(' + name + ')', this.datos[name]);
        //this.datos['error'] = error;
        Object.assign(this.datos, {'error': error});
    }

    GetLastError() {
        return this.LMSGetLastError();
    }

    LMSGetLastError() {
        //logTxt('LMSGetValue('+name+') ->' + this.datos[name]);
        if (this.datos['error'] == undefined) {
            this.datos['error'] = 0;
        }
        return this.datos['error'];
    }

    GetErrorString(param){
        return this.LMSGetErrorString(param);
    }

    LMSGetErrorString (param) {
        if (param != "") {
            var errorString = new Array();
            errorString["0"] = "No error";
            errorString["101"] = "General exception";
            errorString["201"] = "Invalid argument error";
            errorString["202"] = "Element cannot have children";
            errorString["203"] = "Element not an array - cannot have count";
            errorString["301"] = "Not initialized";
            errorString["401"] = "Not implemented error";
            errorString["402"] = "Invalid set value, element is a keyword";
            errorString["403"] = "Element is read only";
            errorString["404"] = "Element is write only";
            errorString["405"] = "Incorrect data type";
            return errorString[param];
        } else {
            return "";
        }
    }

    GetDiagnostic (param) {
        return this.LMSGetDiagnostic(param)
    }

    LMSGetDiagnostic (param) {
        if (param == "") {
            param = errorCode;
        }
        return param;
    }

    GetBookmark() {
        console.log("Pasamos por aquí");
        return "";
    }

    startScorm() {
        // var dst = $(this).data('dest');
        var info = $(this).data('info');
        // logTxt("scrmOpnr", dst);

        var obj = $(this);
        var fldr = $(this).data('fldr');
        var usr = $('#user option:selected').val();

        var URL = API_URL + 'chapter/' + this.chapter_id + '/start';
        $.ajax({
            url: URL,
            type: 'GET',
            dataType: 'json',
            headers: {'Authorization': BEARER_TOKEN},
        })
            .done(function (response) {
                if (!response.error) {
                    var data = response.data;
                    var chapter = data.chapter.chapter;
                    var scorm = data.chapter.chapter.scorm;

                    progress_id = data.chapter.id;

                    const dst = SCORMS_URL + scorm.folder + '/' + scorm.entryPoint;

                    $('#user').data('iduserscorm', chapter.id);
                    $('#ActvUsrScrm').val(chapter.id);
                    var datosSc = data.chapter.data.scorm;
                    var scormVars;
                    localStorage.setItem('scorm', JSON.stringify(datosSc));
                    API.LMSSetValue('cmi.core.student_id', data.course.user.id);
                    API.LMSSetValue('cmi.core.student_name', data.course.user.firstName);
                    // $.each(datosSc, function (index, item) {
                    //     localStorage.setItem(index, item);
                    //     scormVars[index] = item;
                    // });
                    // localStorage.setItem('scorm', JSON.stringify(scormVars));

                    // $('#scorm-menu').html(scorm.menu);
                    $('#scorm-wrapper').attr('src', dst);
                    API.initializeMenu();
                }
            }).fail(function () {
            alert("Se ha producido un error.");
        });
    }
	/*Patch for extra scorm functions*/
		isProxyScoReady()
			{
			}

		GetErrorString()
			{
			}
		GetDiagnostic()
			{
			}

		getStoredData(param)
			{
			}


    initializeMenu() {
        let i = 0;
        $(".frmOpnr").each(function () {
            $(this).attr('data-index', i);
            i++;
        });
        $('.frmOpnr[data-index="0"]').addClass('current');

        $(".frmOpnr").off().on('click', function (e) {
            e.preventDefault();
            var dst = $(this).data('dest');

            $('#scorm-wrapper').attr('src', SCORMS_URL + dst);

            $('.current').removeClass('current');
            $(this).addClass('current');
        });

        $('#arrow-menu').data('max', $("#scorm-menu .frmOpnr").length);

        $('.prev').off().on('click', function () {
            menuMove(-1);
        });
        $('.next').off().on('click', function () {
            menuMove(+1);
        });
    }

    getScormVariables() {
        let scorm = localStorage.getItem('scorm');
        if(scorm === "undefined") return {};
        else scorm = JSON.parse(scorm);
        return scorm;
    }
}

function Start() {
    logTxt('Start', '');
}

function Unload() {
    logTxt('Unload', '');
}

function WriteToDebug() {
    logTxt('WriteToDebug', '');
}

function menuMove(dir) {
    let index = parseInt($('li a.current').data('index'));
    index = index + dir;
    if(index >= 0 && index < parseInt($('#arrow-menu').data('max'))) {
        $('.frmOpnr[data-index="'+ index +'"]').trigger('click');
    }

}

function logTxt(texto, valor) {
    if (typeof (valor) != 'object') {
        texto = texto + ' -> ' + '<span class="sb">' + valor + '</span>';
    } else {
        texto = texto + ' -> ' + '<span class="sb">' + JSON.stringify(valor) + '</span>';
    }
    $('#infolog').append(texto + "<br>");
}

$(window).on("load", function () {
    const image = '../../../uploads/images/chapter/' + $("#scorm-player").data("image"); 
    if(image !== undefined) $("#scorm-wrapper").attr("style", `background-image:url("${image}")`);
    $('#infolog').html('');
    setApi();
    API.startScorm();

    $('#show-menu').on("click", function(){
        $('#scorm-load-panel').addClass('col-md-9');
        $('#navigation').removeClass('d-none');
        $('#show-menu').addClass('d-none');
    });

    $('#hide-menu').on("click", function(){
        $('#scorm-load-panel').removeClass('col-md-9');
        $('#navigation').addClass('d-none');
        $('#show-menu').removeClass('d-none');
    });
});


function setApi() {
    window.API = new ApiSco({make: 'Chevy', model: 'Malibu', price: 30000, year: 2014});
    window.API_1484_11 = new ApiSco({make: 'Chevy', model: 'Malibu', price: 30000, year: 2014});
}

function go(data) {
    alert('go' + data);
};

function fail(data) {
    alert('fail' + data);
};
