export default {
  data() {
    return {
      debounceTimers: {},
    }
  },

  methods: {
    debounce(func, delay = 300, key = "default") {
      // Clear existing timer
      if (this.debounceTimers[key]) {
        clearTimeout(this.debounceTimers[key])
      }

      // Create new timer
      this.debounceTimers[key] = setTimeout(() => {
        try {
          func()
        } catch (error) {
          console.error("Error in debounced function:", error)
        }
        delete this.debounceTimers[key]
      }, delay)
    },

    cancelDebounce(key = "default") {
      if (this.debounceTimers && this.debounceTimers[key]) {
        clearTimeout(this.debounceTimers[key])
        delete this.debounceTimers[key]
      }
    },

    cancelAllDebounces() {
      if (this.debounceTimers) {
        Object.keys(this.debounceTimers).forEach((key) => {
          clearTimeout(this.debounceTimers[key])
        })
        this.debounceTimers = {}
      }
    },
  },

  beforeDestroy() {
    this.cancelAllDebounces()
  },
}