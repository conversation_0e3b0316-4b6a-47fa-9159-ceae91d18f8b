<template>
  <div class="AnnouncementManagerSelectionModal">
    <BaseModal identifier="AnnouncementManagerSelectionModal" @close="closeModal" :title="$t('ANNOUNCEMENT.SHARETAB.MODAL_TITLE')">

      <div class="form-group mb-0">
        <i class="fas fa-search content-search-icon"></i>
        <label class="content-search-label pb-0">
          <input type="search" class="form-control" :placeholder="$t('ANNOUNCEMENT.SHARETAB.FILTER.SEARCH')" v-model="filter"
            @input="onFilterInput" />
        </label>
      </div>

      <DataNotFound v-if="!companyManagers.length" :hide-on="!companyManagers.length"
        :text="$t('ANNOUNCEMENT.SHARETAB.FILTER.NOT_FOUND')" icon="fa-link" :banner="true" />
     
      <ItemDisplay v-else>
        <ItemSelectionCard 
          v-for="manager in companyManagers" 
          :key="manager.id" 
          :name="manager.first_name + ' ' + manager.last_name" 
          :info="manager.email" 
          :isSelected="isAlreadyManager(manager.id)"
          :selectedText="$t('ANNOUNCEMENT.SHARETAB.ALREADY_SHARED')"
          :buttonText="$t('ANNOUNCEMENT.SHARETAB.TITLE')"
          @itemSelected="handleManagerSelection(manager.id)" 
        />
      </ItemDisplay>
    </BaseModal>
  </div>
</template>
<script>
import debounceMixin from '../../../mixins/debounceMixin'
import BaseModal from '../../../../base/BaseModal.vue'
import Spinner from "../../../../admin/components/base/Spinner.vue";
import DataNotFound from '../../../components/details/DataNotFound.vue'
import ItemSelectionCard from './ItemSelectionCard.vue';
import ItemDisplay from './ItemDisplay.vue';

export default {
  name: "AnnouncementManagerSelectionModal",
  mixins: [debounceMixin],
  components: { BaseModal, Spinner, DataNotFound, ItemDisplay, ItemSelectionCard },
  props: {
    announcement: {
      type: Object,
      required: true
    },
    companyManagers: {
      type: Array,
      default: () => []
    },
    excludedManagerIds: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      isSearching: false,
      filter: '',
    }
  },
  computed: {
    filteredManagers() {
      return this.companyManagers.filter(manager => !this.excludedManagerIds.includes(manager.id));
    },
  },
  methods: {
    isAlreadyManager(userId) {
      return this.excludedManagerIds.includes(userId);
    },
    async handleManagerSelection(managerId) {

      try {
        const { data, error } = await this.$store.dispatch(
          "announcementModule/addAnnouncementManager",
          {
            announcementId: this.announcement.id,
            managerId: managerId
          }
        );
        
        if (!error) { //SERVER IS RESPONDING 204 - NO RESPONSE BODY
          //REFRESH THE VIEW IN THE PARENT (EMIT A REFRESH REQUEST)
          this.$emit('refreshAnnouncementManagers');
          this.$toast.success(this.$t('ANNOUNCEMENT.SHARETAB.SHARE.SUCCESS'));
        }
      } catch (error) {
        console.error('Failed to add manager', error);
        this.$toast.error(this.$t('ANNOUNCEMENT.SHARETAB.SHARE.FAILURE'));
      }
    },
    onFilterInput() {
      this.isSearching = true;
      this.debounce(() => {
        this.$emit('searchRequested', this.filter);
        this.isSearching = false;
      }, 600, 'search');
    },
    clearData() {
      this.filter = '';
    },
    closeModal() {
      this.clearData();
    },
  },
}

</script>
<style scoped lang="scss">
.AnnouncementManagerSelectionModal {
  :deep(.modal-body) {
    background-color: #eee;
  }
  .manager-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  .content-search-label {
    width: calc(100% - 26px);
  }
}
</style>