import { ROLE_LIST } from '../index'

export const COURSE_PERMISSIONS = {
  VIEW: 'view',
  CREATE: 'create',
  CLONE: 'clone',
  DELETE: 'delete',
  SHARE: 'share',
  EXPORT: 'excel-export',
  UPDATE: {
    BASIC: 'update-basic-info',
    AUDIENCE: 'update-audience-tab',
    CERTIFICATES: 'update-certificate-tab',
    PUBLISH: 'update-publish',
    CHAPTERS: 'update-chapters',
    SEASONS: 'update-seasons',
    MATERIALS: 'update-materials',
    ANNOUNCEMENT: 'update-announcements',
    TRANSLATIONS: 'update-translations',
    OPINIONS: 'update-opinions',
    MANAGER_LIST: 'update-manager-list',
    FIELDS: {
      STEP1: {
        NAME: "update-field-step1-name",
        CODE: "update-field-step1-code",
        TYPE: "update-field-step1-type",
        CATEGORY: "update-field-step1-category",
        DESCRIPTION: "update-field-step1-description",
        DURATION: "update-field-step1-duration",
        LEVEL: "update-field-step1-level",
        LOCALE: "update-field-step1-locale",
        SEGMENTS: "update-field-step1-segments",
        TAGS: "update-field-step1-tags",
        GENERAL_INFO: "update-field-step1-general-info",
        THUMBNAIL: "update-field-step1-thumbnail",
      },
      STEP2: {
        ACTIVE: "update-field-step2-active",
        OPEN: "update-field-step2-open",
        OPEN_VISIBLE: "update-field-step2-open-visible",
        POINTS: "update-field-step2-points",
        OPEN_NEW: "update-field-step2-open-new",
        FILTERS: "update-field-step2-filters",
        SURVEY: "update-field-step2-survey",
      },
      STEP3: {
        DIPLOMA_SETTINGS: "update-field-step3-diploma-settings"
      }
    },
  },
  SEASONS: 'seasons-tab',
  PERSONS: 'persons-tab',
  STATS: 'stats-tab',
  CREATE_CHAPTER: 'create-chapter',
}
const managerPermissions = [
  COURSE_PERMISSIONS.VIEW,
  COURSE_PERMISSIONS.SHARE,
  COURSE_PERMISSIONS.EXPORT,
  COURSE_PERMISSIONS.UPDATE.PUBLISH,
  COURSE_PERMISSIONS.UPDATE.ANNOUNCEMENT,
  COURSE_PERMISSIONS.UPDATE.CERTIFICATES,
  COURSE_PERMISSIONS.UPDATE.AUDIENCE,
  COURSE_PERMISSIONS.PERSONS,
  COURSE_PERMISSIONS.STATS,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP2.ACTIVE,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP2.OPEN,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP2.OPEN_VISIBLE,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP2.OPEN_NEW,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP2.FILTERS,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP2.SURVEY,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP2.POINTS,
  COURSE_PERMISSIONS.UPDATE.FIELDS.STEP3.DIPLOMA_SETTINGS,
]
export const COURSE_ACTIONS_BY_ROLE = {
  [ROLE_LIST.MANAGER]: managerPermissions,
  [ROLE_LIST.MANAGER_EDITOR]: managerPermissions,
  [ROLE_LIST.TEAM_MANAGER]: managerPermissions,
  [ROLE_LIST.CREATOR]: [
    COURSE_PERMISSIONS.VIEW,
    COURSE_PERMISSIONS.CREATE,
    COURSE_PERMISSIONS.CLONE,
    COURSE_PERMISSIONS.DELETE,
    COURSE_PERMISSIONS.UPDATE.BASIC,
    COURSE_PERMISSIONS.UPDATE.SEASONS,
    COURSE_PERMISSIONS.UPDATE.MATERIALS,
    COURSE_PERMISSIONS.UPDATE.ANNOUNCEMENT,
    COURSE_PERMISSIONS.UPDATE.TRANSLATIONS,
    COURSE_PERMISSIONS.UPDATE.OPINIONS,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.NAME,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.CODE,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.TYPE,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.CATEGORY,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.DESCRIPTION,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.DURATION,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.LEVEL,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.LOCALE,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.SEGMENTS,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.TAGS,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.GENERAL_INFO,
    COURSE_PERMISSIONS.UPDATE.FIELDS.STEP1.THUMBNAIL,
  ],
}