<template>
  <div class="LocaleSelector">
    <div class="d-flex align-items-center mb-2">
      <label class="mb-0">{{ $t('LOCALE') }}</label>
      <i v-show="tooltipDescription?.length" class="fas fa-question-circle text-primary ml-2 fs-4" data-toggle="tooltip" :title="tooltipDescription" data-placement="right" />
    </div>
    <div class="LocaleSelector__locale-selection">
      <div class="locales">
        <button type="button" class="locale"
                v-for="(name, key) in locales" :class="innerValue === key ? 'active' : ''"
                data-toggle="tooltip" data-placement="top" :title="name"
                :disabled="disabled"
                @click="setValue(key)" >{{ key }}</button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: "LocaleSelector",
  props: {
    locale: {
      type: String,
      default: null
    },
    tooltipDescription: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: 'en'
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    locales() {
      return this.$store.getters['localeModule/getLocales'];
    },
    innerValue: {
      get() {
        return this.locale ?? this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
        this.$emit('set-locale', newValue)
      }
    }
  },
  mounted() {
    if (this.tooltipDescription?.length) $('[data-toggle="tooltip"]').tooltip();
  },
  methods: {
    setValue(key) {
      if (this.disabled) {
        return;
      }
      this.innerValue = key
    }
  }
}
</script>

 <style scoped lang="scss"> 
.LocaleSelector {
  &__locale-selection {
    display: flex;
    flex-flow: row wrap;
    width: 100%;
    border: 1px solid $base-border-color;
    background-color: #FFFFFF;
    border-radius: 0.25rem;
    .locales {
      width: 100%;
      display: flex;
      flex-flow: row wrap;

      button {
        text-transform: uppercase;
        width: 50px;
        height: 50px;
        border: 1px solid $base-border-color;
        border-radius: 5px;
        margin: 5px;
        color: $base-border-color;
        background-color: #ffffff;
        font-size: 18px;
        display: flex;
        flex-flow: column;
        align-items: center;
        justify-content: center;
        span {
          font-size: 12px;
        }

        &.active {
          border: 1px solid $base-border-color-active;
          color: $base-border-color-active;
        }
      }
    }
  }
}
</style>
