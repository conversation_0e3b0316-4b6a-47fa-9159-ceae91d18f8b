<template>
  <div class="FileSelector">
    <div class="d-flex align-items-center mb-2">
      <label v-if="title && title.length > 0" class="mb-0">{{ $t(title) }}</label>
      <i v-if="titleTooltip?.length" class="fas fa-question-circle text-primary ml-2 fs-4" data-toggle="tooltip" :title="titleTooltip" data-placement="right" />
    </div>

    <div
          :id="`${name}-dropzone`"
         :ref="`${name}-dropzone`"
         class="FileSelector__preview" @click="openFileSelector()"
         :class="(isVideo && selectedFiles.length > 0 ? 'is-video ' : '') + previewDefaultClass"
         :style="{
            'background-image': `url(${ defaultImage })`
          }"
   >
      <file-selector-render :accept="accept"
                            v-for="(file, index) in selectedFiles"
                            :key="index"
                            :id="`${name}-file-renderer-${index}`"
                            :file="file"
                            :width="renderWidth"
                            :height="renderWidth"
                            :is-percent="asPercent"
                            @click.stop="onSelectedItemClick"
      />
    </div>
    <div v-if="!disabled" class="FileSelector__selector" id="FileSelector__selector" :class="applyExtraActionsStyle ? 'with-extra-actions' : ''">
      <input :multiple="multiple" :name="name"
             :id="name" :ref="name" type="file" :accept="accept" :disabled="disabled"
             @change="onFileChange($event.target)">

      <button @click="openFileSelector()" type="button" id="btn-select-image"
              :class="showResetButton ? '' : 'mr-auto'"
              class="btn btn-primary FileSelector__selector--select-file"
      ><i class="fa fa-upload"></i> {{ $t(btnSelectorValue) }}</button>
      <div  v-if="enableDelete">
        <button
              type="button"
              class="btn btn-danger FileSelector__selector--reset"
              :class="showResetButton ? 'mr-auto' : ''"
              @click="resetInput()"
              v-if="showResetButton"
        ><i class="fa fa-trash"></i></button>
      </div>

      <slot name="extra-actions"></slot>
    </div>
  </div>
</template>

<script>
import $ from 'jquery';
import FileSelectorRender from "./FileSelectorRender.vue";

const pdfJsLib = require('pdfjs-dist/build/pdf');
pdfJsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';

export default {
  name: "FileSelector",
  components: {FileSelectorRender},
  $,
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: 'file'
    },
    accept: {
      type: String,
      default: 'image/png, image/jpeg, image/jpg'
    },
    image: {
      type: String,
      default: null
    },
    defaultImage: {
      type: String,
      default: '/assets/common/add_image_file.svg'
    },
    title: {
      type: String,
      default: 'THUMBNAIL'
    },
    titleTooltip: {
      type: String,
      default: ''
    },
    btnSelectorValue: {
      type: String,
      default: 'SELECT_IMAGE'
    },

    /**
     * @values limited-size|full-size
     */
    previewDefaultClass: {
      type: String,
      default: 'limited-size'
    },

    /**
     * Allowed max size in px for internal selected elements
     */
    selectedElementsMaxSize: {
      type: Number,
      default: 300
    },

    selectedElementMinSize: {
      type: Number,
      default: 150
    },

    applyExtraActionsStyle: {
      type: Boolean,
      default: false
    },

    showResetButton: {
      type: Boolean,
      default: true
    },

    // Allow pass value from v-model and update content by using input event
    useModel: {
      type: Boolean,
      default: false
    },
    value: null,
    
    disabled: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      selectedFiles: [],// Used only when the file comes from drag&drop
      dropzoneWidth: 0,
      enableDelete:false,
    };
  },
  computed: {
    itemSelectedType() {
      if (this.accept.includes('video')) {
        return 'video.svg';
      }
      if (this.accept.includes('pdf')) {
        return 'pdf.svg';
      }
      return 'content.svg';
    },

    isVideo() {
      return this.accept.includes('video');
    },

    itemWidth() {
      // Calculate current width for the elements to be in correct size
      const dropZone = this.$refs[`${this.name}-dropzone`];
      if (dropZone === null || dropZone === undefined) return 0;
      const width = dropZone.clientWidth;
      if (this.selectedFiles.length > 0) {
        if (this.accept.includes('pdf') && !this.multiple) {
          return width - 20;
        }

        let divideBy = this.selectedFiles.length > 2 ? 2 : this.selectedFiles.length;
        if (this.accept.includes('pdf') && this.selectedFiles.length === 1) {
          divideBy = 2;
        }

        let elementWidth = Math.floor(width / divideBy);
        if (elementWidth < this.selectedElementMinSize) elementWidth = this.selectedElementMinSize;
        if (elementWidth > this.selectedElementsMaxSize) {
          elementWidth = Math.floor(width / 4);
        }
        return elementWidth - 20;// Subtract 20px used as margin and padding
      }
      return width;
    },

    renderWidth() {
      if (this.accept.includes('pdf') && this.selectedFiles.length === 1) {
        // Avoid using the full width when selecting only one pdf file
        return this.itemWidth;
      }
      return this.selectedFiles.length === 1 ? 99 : this.itemWidth;
    },

    asPercent() {
      return this.selectedFiles.length === 1 && !this.accept.includes('pdf');
    }
  },
  watch: {
    accept() {
      this.resetFileSelector();
    },
    value: {
      handler: function () {
        if (this.useModel) this.selectedFiles = this.value ?? [];
      },
      immediate: true
    }
  },
  mounted() {
    const dropZone = this.$refs[`${this.name}-dropzone`];
    const fileInput = this.$refs[this.name];
    let self = this;
    if(!this.defaultImage) this.enableDelete=true;

    dropZone.ondrop = function (event) {
      event.preventDefault();

      /**
       * Reset all current values: input file and background
       */
      self.resetFileSelector();
      self.selectedFiles = [];
      /*************************************/

      let filesValid = false;
      if(!self.multiple && event.dataTransfer.files.length > 1) {
        self.$toast.error(self.$t('FILE_UPLOAD.ONLY_ONE_FILE_ALLOWED') + '');
        return;
      }

      /**
       * Validate files to be the type specified in accept
       */
      [...event.dataTransfer.files].forEach((file, i) => {
        filesValid = self.isValid(file.type, self.accept);
      });

      if (filesValid) {
        fileInput.files = event.dataTransfer.files;
        self.selectedFiles = fileInput.files;

        dropZone.classList.remove('drag-enter');
        dropZone.classList.remove('failed-type');

        self.$toast.success(self.$t('FILE_UPLOAD.FILES_SUCCESSFULLY_ADDED'));
      }
      else {
        self.$toast.error(self.$t('FILE_UPLOAD.INVALID_FILE_SELECTION') + '')
        dropZone.classList.add('failed-type')
      }
      self.$emit('input', self.selectedFiles);
    }

    dropZone.ondragover = (event) => {
      event.preventDefault();// Stop the handler to avoid being handled by the browser
    }

    dropZone.ondragenter = ev => {
      dropZone.classList.add('drag-enter');
    }
    dropZone.ondragleave = ev => {
      dropZone.classList.remove('drag-enter');
    }
  },
  methods: {
    openFileSelector() {
      $('#' + this.name).click();
    },

    isValid(fileType, accept) {
      const regex = new RegExp( accept.replace( /\*/g, '.\*' ).replace( /\,/g, '|' ) );
      return regex.test(fileType);
    },

    onFileChange(input) {
      this.enableDelete= true;
      this.selectedFiles = input.files;
      this.$emit('input', this.selectedFiles);
    },

    resetInput() {
      this.resetFileSelector();
    },

    resetFileSelector() {
      if(this.defaultImage) this.enableDelete= false;
      $('#' +  this.name).val('');
      this.selectedFiles = [];
      this.$emit('input', this.selectedFiles);
    },

    onSelectedItemClick(e) {
      // this.$toast.success('Clicked')
      console.log('clicked')
    }
  },
}
</script>

 <style scoped lang="scss"> 
.FileSelector {
    display: flex;
    flex-flow: column;

  &__preview {
    &.is-video {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: center;
      background-color: #cecece;
    }
    &.limited-size {
      @media #{small-screen()} {
        width: 100%;
        height: 300px;
      }
    }

    &.centered {
      margin-left: auto;
      margin-right: auto;
    }

    &.full-size {
      width: 100%;
      height: 300px;
    }

    padding: 5px;
    gap: 5px;
    display: flex;
    flex-flow: row wrap;
    overflow-y: auto;

    .selected-file {
      border: 1px solid $base-border-color;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      border-radius: 3px;
      background-color: #FFFFFF;
      &:hover {
        border-color: $base-border-color-active;
      }
    }

    @media screen and (min-width: 400px) {
      width: 400px;
      height: 400px;
    }

    border: 1px solid $base-border-color;

    &.drag-enter {
      border-color: $base-border-color-active;
    }

    cursor: pointer;
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: center center;

    &.has-image {
      background-size: cover;
    }

    &.no-background-size {
      background-size: unset;
    }
  }

  &__selector {
    margin-top: 1rem;
    display: flex;
    input {
      display: none;
    }
    &--select-file {
      flex-grow: 1;
    }

    &.with-extra-actions {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: flex-end;
      .FileSelector__selector--select-file {
        flex-grow: unset;
      }
    }
  }
}
</style>
