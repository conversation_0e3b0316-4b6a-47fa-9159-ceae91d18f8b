<template>
  <div class="Tags">
    <div class="d-flex align-items-center mb-2">
      <label class="Tags__title mb-0">{{ title }}</label>
      <i v-if="tooltipDescription?.length" class="fas fa-question-circle text-primary ml-2 fs-4" data-toggle="tooltip" :title="tooltipDescription" data-placement="right" />
    </div>
    <div class="Tags__input" :class="inputFocused ? 'focused' : ''" v-if="editable">
      <input type="text"
             class="form-control Tags__input--input"
             v-model="tagValue"
             @keydown.enter.prevent="allowEnterButton && addTag()"
             @focus="inputFocused = true"
             @blur="inputFocused = false"
             :disabled="disabled"
             :placeholder="inputFocused ? $t('TAGS.PLACEHOLDER') : ''"
      >
      <loader :is-loaded="loadingItems"/>
      <ul v-if="autoCompleteItemsActive.length > 0">
        <li v-for="info in autoCompleteItemsActive" @click="setAutoCompleteItem(info)">
          {{ info.name || info }}
        </li>
      </ul>
    </div>
    <div class="mt-2 d-flex flex-wrap">
      <div class="Tags__tag" v-for="(tag, index) in tags" :key="index">
        <span class="tag--content">{{ tag.name || tag }}</span><span @click="remove(index)" class="remove" v-if="editable">&times;</span>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Loader from "../../admin/components/Loader.vue";

export default {
  name: "Tags",
  components: {Loader},
  props: {
    allowEnterButton: {
      type: Boolean,
      default: true
    },
    editable: {
      type: Boolean,
      default: true
    },
    propTags: [],
    value: {
      type: Object|Array,
      default: () => ([])
    },
    title: {
      type: String,
      default: 'Tags'
    },
    autoCompleteUrl: {
      type: String,
      default: null
    },
    unique: {
      type: Boolean,
      default: true
    },
    tooltipDescription: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      tags: [],
      tagValue: '',
      inputFocused: false,
      loadingItems: false,
      autoCompleteItems: [],
      autoCompleteItemsActive: [],
      filterDataOnLoad: false
    };
  },
  watch: {
    propTags(newValue) {
      this.tags = newValue;
    },
    tagValue() {
      if (this.tagValue.length === 3) {
        this.loadAutoCompleteData();
      } else if (this.tagValue.length > 3) {
        this.filterDataOnLoad = this.loadingItems;
        this.filterData();
      }
    }
  },
  created() {
    this.tags = this.propTags;
  },
  methods: {
    filterData() {
      this.autoCompleteItemsActive = this.autoCompleteItems.filter(item => this.normalize(item.name || item).includes(this.normalize(this.tagValue)))
    },
    async loadAutoCompleteData() {
      if (!this.autoCompleteUrl) return;
      this.loadingItems = true;
      try {
        const url = new URL(window.location.origin + this.autoCompleteUrl);
        if (this.tagValue.length > 0) url.searchParams.set('query', this.tagValue);
        const result = await axios.get(url.toString())
        const { data } = result.data;
        this.autoCompleteItems = data;
        if (this.filterDataOnLoad) {
          this.filterDataOnLoad = false;
          this.filterData();
        } else this.autoCompleteItemsActive = data;
      } finally {
        this.loadingItems = false;
      }
    },
    setAutoCompleteItem(item) {
      if (this.unique) {
        const index = this.tags.findIndex(tag => this.normalize(tag.name || tag) === this.normalize(item.name || item));
        if (index >= 0) {
          this.autoCompleteItemsActive = [];
          return;
        }
      }

      this.tags.push(item);
      this.tagValue = '';
      this.$emit('tags-updated', this.tags);
      this.autoCompleteItemsActive = [];
    },
    addTag() {
      if (this.tagValue.length < 1 || this.disabled) {
        return;
      }
      if (this.unique) {
        const index = this.tags.findIndex(tag => this.normalize(tag.name || tag) === this.normalize(this.tagValue));
        if (index >= 0) return;
      }

      this.tags.push(this.tagValue);
      this.tagValue = '';
      this.$emit('tags-updated', this.tags);
    },

    remove(index) {
      if (this.disabled) {
        return;
      }
      this.tags.splice(index, 1);
      this.$emit('tags-updated', this.tags);
    },

    normalize(string) {
      return string
          .toLowerCase()
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "");
    },
  }
}
</script>

 <style scoped lang="scss"> 
.Tags {
  max-width: 100%;
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  border-radius: 5px;
  background-color: #ffffff;

  &__title {
    width: 100%;
  }

  &__input {
    width: 100%;
    position: relative;
    .loader {
      padding: 0 !important;
      position: absolute;
      top: 0;
      right: 3px;
      bottom: 0;
    }
    &.focused {
      flex-grow: 1;
    }

    ul {
      border: 1px solid $base-border-color;
      border-radius: 5px;
      background: #ffffff;
      color: #212121;
      list-style: none;
      position: absolute;
      left: 0;
      right: 0;
      padding: 0;
      z-index: 10;
      max-height: 150px;
      overflow-y: auto;
      margin-top: 0.1rem;

      li {
        padding: 0.15rem;
        cursor: pointer;
        &:hover {
          background-color: rgba(0,0,0, 0.15);
        }
      }
    }
  }



  $color-text: #FFF;

  &__tag {
    position: relative;
    border-radius: 6px;
    clip-path: polygon(20px 0px, 100% 0px, 100% 100%, 0% 100%, 0% 20px);
    background: $color-primary;
    margin: 5px 8px;
    font-weight: 600;
    font-size: 18px;
    color: $color-text;
    transition: clip-path 500ms;
    display: grid;
    grid-template-columns: auto auto;
    align-items: center;

    .remove {
      padding: 0 7px;
      border-left: 1px solid #ffffff;
      cursor: pointer;
      color: $color-text;
    }

    .tag--content {
      margin: 5px 30px;
    }

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 20px;
      height: 20px;
      background: $color-primary-light;
      box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
      border-radius: 0 0 6px 0;
      transition: transform 500ms;
    }

    &:hover {
      clip-path: polygon(0px 0px, 100% 0px, 100% 100%, 0% 100%, 0% 0px);
    }

    &:hover:after {
      transform: translate(-100%, -100%);
    }
  }
}
</style>
