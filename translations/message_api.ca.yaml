message_api.content.scorm_correctly: 'Scorm correctament iniciat'
message_api.content.token_not_send: 'Token no enviat'
message_api.content.error_get_content: 'S''ha produït un error en intentar iniciar el capítol:'
message_api.content.scorm_user_not_access: 'L''usuari no té accés a aquest curs'
message_api.content.scorm_not_content: 'El capítol no és un paquet de continguts'
message_api.base.credentials_no_correct: 'Les credencials introduïdes no són correctes.'
message_api.base.authorized_access: 'Accés no autoritzat'
message_api.base.user_successfully: 'L''usuari ha tancat la sessió amb èxit'
message_api.base.error_login: 'S''ha produït un error en intentar iniciar la sessió de l''usuari - Error:'
message_api.base.authorized_token_fail: 'No s''ha pogut accedir a través de SSO. Si encara no s''ha registrat i activat el seu usuari en la plataforma, haur<PERSON> de fer-ho per a poder habilitar aquest tipus d''accés SSO.'
message_api.base.user_not_exists_in_ad: 'Credencials incorrectes o usuari no existeix en Directori Actiu'
message_api.controller.error_get_course: 'S''ha produït un error en intentar iniciar la sessió de l''usuari - Error:'
message_api.controller.chapter_init: 'Capítol correctament iniciat'
message_api.controller.error_chapter_init: 'S''ha produït un error en intentar iniciar el capítol:'
message_api.controller.error_chapter_update: 'Capítol correctament actualitzat'
message_api.controller.help_text: 'Ajuda. Els textos es recuperen amb èxit'
message_api.controller.course_not_found: 'No s''han trobat cursos'
message_api.controller.user_not_access: 'L''usuari no té accés a aquest curs'
message_api.controller.evaluation_course: 'Avaluació enviada'
message_api.controller.training_itinerary: 'El teu itinerari formatiu'
message_api.controller.continue_training: 'Continuar formant-te'
message_api.message.not_found: 'Missatge no trobat'
message_api.message.not_access: 'Accés no permès'
message_api.scorm.init: 'Scorm correctament iniciat'
message_api.scorm.not_package: 'El capítol no és un paquet SCORM'
message_api.scorm.button_add_scorm: 'Agregar paquet scorm'
message_api.scorm.button_delete_scorm: 'Eliminar paquet scorm'
message_api.scorm.title_package: 'paquet scorm'
message_api.scorm.upload_file_scorm: 'Seleccionar arxiu .zip'
message_api.scorm.info_upload_scorm: 'Has de carregar el paquet scorm'
message_api.notification.not_found: 'Notificació no trobada'
message_api.player.no_content: 'Aquest capítol no té contingut'
message_api.alert.minimal_question: 'Aquest joc té un mínim de %number% preguntes'
message_api.alert.image_puzzle: 'El puzle ha de tenir una imatge'
message_api.alert.question_correct: 'A aquesta pregunta has d''agregar-li una resposta correcta'
message_api.alert.chapter_content: 'A aquest capítol li fa falta contingut'
message_api.alert.pdf: 'A aquest capítol li fa falta un pdf'
message_api.alert.video: 'A aquest capítol li fa falta contingut'
message_api.alert.slider: 'Aquest capítol té un mínim de %number% imatges'
message_api.alert.scorm: 'A aquest capítol li fa falta paquet *scorm'
message_api.alert.question_hidden: 'Aquesta pregunta només ha de contenir una resposta i ha de ser correcta'
message_api.alert.minimal_pair: 'Aquest capítol requereix un mínim de %number% parelles.'
message_api.announcement.call_for_aplications: Convocatòria
message_api.alert.course_content_incomplete: 'Falta informació en els capítols, si us plau revisi i agregui contingut per a poder continuar amb la creació de la convocatòria.'
message_api.diploma.supered: 'Per haver superat'
message_api.diploma.granted: 'Concedit a'
message_api.diploma.date: Data
message_api.diploma.diploma: Diploma
message_api.diploma.trato: D./Sra
message_api.diploma.nif: 'amb NIF'
message_api.diploma.services: 'que presta els seus serveis en l''Empresa'
message_api.diploma.cif: 'amb CIF'
message_api.diploma.evaluation: 'Ha superat amb avaluació positiva l''Acció Formativa'
message_api.diploma.code: 'Codi AF/GRUP'
message_api.diploma.between: 'Durant els dies'
message_api.diploma.duration: 'amb una durada total de'
message_api.diploma.fomartionType: 'hores en la modalitat formativa'
message_api.diploma.courseContent: 'Continguts del curs en la següent pàgina'
message_api.diploma.signatureSeal: 'Signatura i segell de l''entitat responsable <br> d''impartir la formació'
message_api.diploma.signatureDate: 'Data d''expedició'
message_api.diploma.acreditation: Acreditatiu
message_api.diploma.signature: 'Signatura del participant'
