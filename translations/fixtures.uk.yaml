nps_question.text.description: 'Поділіться з нами своєю думкою'
nps_question.nps.description: 'Оцінювання курсу'
type_course.teleformacion.name: 'Електронне навчання'
type_course.teleformacion.description: 'Для курсів електронного навчання'
type_course.presencial.name: Особисто
type_course.presencial.description: 'Виїзні курси'
type_course.mixto.name: Змішаний
type_course.mixto.description: 'Це поєднання електронного та очного навчання'
type_course.aula_virtual.name: 'Віртуальний клас'
type_course.aula_virtual.description: 'Заняття проходять у режимі відеоконференції'
alert_type_tutor.1.name: '<PERSON><PERSON><PERSON><PERSON>ент, якому телефонували, не отримав доступ до курсу'
alert_type_tutor.1.description: 'Якщо викликана особа не отримала доступ до курсу, тьютору буде надіслано сповіщення'
alert_type_tutor.2.name: '50% розмови минуло, а 25% контенту не було завершено'
alert_type_tutor.2.description: 'Якщо минуло 50% дзвінка, а 25% контенту не пройдено, тьютору буде надіслано сповіщення'
alert_type_tutor.3.name: '80% розмови минуло, а 50% контенту ще не завершено'
alert_type_tutor.3.description: 'Якщо минуло 80% дзвінка, а 50% контенту не пройдено, тьютору буде надіслано сповіщення'
alert_type_tutor.4.name: 'До кінця набору залишилося лише кілька днів, а курс ще не завершено'
alert_type_tutor.4.description: 'Ви повинні оцінити кількість днів, які вважаються кількома днями'
alert_type_tutor.5.name: 'Людина, якій телефонували, пройшла курс, але не відповіла на опитування.'
alert_type_tutor.5.description: 'Якщо на платформі є опитування, тьютору буде надіслано сповіщення, якщо людина, якій телефонують, пройшла курс, але не відповіла на опитування'
alert_type_tutor.6.name: 'Особа пройшла курс, але не завантажила диплом'
alert_type_tutor.6.description: 'Якщо викликана особа пройшла курс, але не завантажила диплом, викладачеві буде надіслано сповіщення.'
announcement_configuration_type.temporalizacion.name: Час
announcement_configuration_type.temporalizacion.description: 'Для того, щоб полегшити моніторинг курсу, ми відведемо певний час для кожного блоку змісту та вправ, щоб мати змогу визначити, хто з учасників працює в адекватному темпі, а хто відстає в навчальному процесі'
announcement_configuration_type.curso_bonificado.name: 'Субсидований курс'
announcement_configuration_type.curso_bonificado.description: 'Субсидовані курси - це ті, що проводяться через Тристоронній фонд і фінансуються компаніями за рахунок внесків на соціальне страхування.'
announcement_configuration_type.chat.name: Чат
announcement_configuration_type.chat.description: 'Чат - це інструмент синхронного спілкування, який дозволяє учасникам курсу взаємодіяти в режимі реального часу за допомогою текстових повідомлень.'
announcement_configuration_type.notificaciones.name: Сповіщення
announcement_configuration_type.notificaciones.description: 'Сповіщення - це повідомлення, що надсилаються учасникам курсу для інформування про важливі новини або події.'
announcement_configuration_type.mensajeria.name: 'Обмін повідомленнями'
announcement_configuration_type.mensajeria.description: 'Система обміну повідомленнями - це комунікаційна система, яка дозволяє учасникам курсу надсилати та отримувати приватні повідомлення.'
announcement_configuration_type.foros.name: Форуми
announcement_configuration_type.foros.description: 'Форум - це асинхронний комунікаційний простір, який дозволяє учасникам курсу обмінюватися повідомленнями на задану тему.'
announcement_configuration_type.diploma.name: Диплом
announcement_configuration_type.diploma.description: 'Дипломи - це сертифікати, які видаються учасникам курсу на підтвердження його завершення.'
announcement_configuration_type.tutor_alerts.name: 'Активувати сповіщення про репетиторів'
announcement_configuration_type.tutor_alerts.description: 'Сповіщення - це повідомлення, які надсилаються тьютору курсу, щоб поінформувати його про важливі новини або події.'
announcement_configuration_type.encuesta_satisfaccion.name: 'Опитування щодо задоволеності'
announcement_configuration_type.encuesta_satisfaccion.description: 'Опитування задоволеності - це анкети, які розсилаються учасникам курсу, щоб дізнатися, що вони думають про курс.'
announcement_configuration_type.finalizar_convocatoria.name: 'Курс залишатиметься активним після завершення дзвінка'
announcement_configuration_type.finalizar_convocatoria.description: 'Користувач зможе отримати доступ до матеріалів курсу після його завершення.'
announcement_configuration_type.firma_digital.name: 'Цифровий підпис'
announcement_configuration_type.firma_digital.description: 'Цифровий підпис необхідний для того, щоб мати змогу зареєструватися на очний курс.'
announcement_configuration_type.gestion_costes.name: 'Управління витратами'
announcement_configuration_type.gestion_costes.description: 'Управління витратами дозволяє групам вказувати вартість дзвінка.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_ПОВІДОМЛЕННЯ_ПРО_АНОНС
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Увімкнути сповіщення на пошту.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: ПОВІДОМЛЕННЯ_ПРО_АНОНС
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Увімкнути звичайні сповіщення'
announcement_configuration_type.objetivos_contenidos.name: 'Цілі та зміст'
announcement_configuration_type.objetivos_contenidos.description: 'Цілі та зміст курсу будуть відображені в дипломі.'
announcement_configuration_type.dni.name: Днi
announcement_configuration_type.dni.description: 'Ідентифікаційний номер студента буде вказаний на дипломі'
announcement_configuration_type.template_excel.name: 'Шаблон реєстрації excel'
announcement_configuration_type.template_excel.description: 'Цей шаблон буде використовуватися для зарахування студентів з використанням коду HRBP замість DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Дозвольте тьютору завантажувати групові звіти у форматі ZIP'
announcement_criteria.1.name: 'Мінімальна кількість розділів для завершення'
announcement_criteria.1.description: 'Мінімальна оцінка може бути, наприклад, 70 зі 100'
announcement_criteria.2.name: 'Виконання завдань'
announcement_criteria.2.description: 'Оціночні перевірки'
announcement_criteria.3.name: 'Максимальний час простою'
announcement_criteria.3.description: 'Наприклад, користувач не може бути неактивним більше 10 хвилин'
announcement_criteria.4.name: 'Завершення діяльності'
announcement_criteria.4.description: 'Користувач повинен виконати запропоновані дії'
announcement_criteria.5.name: 'Завершено годин тренінгу'
announcement_criteria.5.description: 'Наприклад, якщо програма розрахована на 20 годин, користувач повинен пройти ці 20 годин'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Перший крок у створенні дзвінка'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Другий крок створення дзвінка, де заповнюється загальна інформація'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Цей крок залежить від того, чи активував клієнт бонус'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'На цьому етапі до дзвінка додаються студенти, які можуть бути призначені до групи'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'На цьому кроці налаштовуються групи учнів, створені на попередньому кроці'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'На цьому кроці налаштовується опитування, яке буде надіслано учням'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'На цьому кроці налаштовується опитування, яке буде надіслано учням'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'На цьому етапі відображаються дипломи, доступні на платформі, щоб клієнт міг вибрати той, який він/вона бажає'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Це спеціальні сповіщення, які інформують тьютора про події виклику'
class_room_virtual.zoom.name: Збільшити
class_room_virtual.zoom.description: 'Платформа для веб-конференцій, що дозволяє здійснювати відеодзвінки високої чіткості з функціями спільного доступу до робочого столу, дошки, чату, запису конференцій, обміну документами та доступом з будь-якого місця, оскільки вона доступна для мобільних пристроїв.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'Платформа ClickMeeting є одним з найбільш зручних інтерфейсів для проведення вебінарів на ринку і пропонує безліч гнучких можливостей для налаштування'
class_room_virtual.jitsi.name: Джітсі
class_room_virtual.jitsi.description: 'Рішення з відкритим вихідним кодом для відеоконференцій із зашифрованими з''єднаннями, доступне для різних операційних систем'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Просте в інтеграції та гнучко настроюване програмне забезпечення для відеоконференцій з відкритим вихідним кодом'
configuration_cliente_announcement.COMMUNICATION.description: 'Це дає змогу увімкнути комунікацію під час дзвінка'
configuration_cliente_announcement.CERTIFICATE.description: 'Це дозволяє завантажувати дипломи під час дзвінка'
configuration_cliente_announcement.SURVEY.description: 'Це дозволяє увімкнути опитування, які будуть доступні пізніше під час дзвінка'
configuration_cliente_announcement.ALERT.description: 'Це дозволяє увімкнути сповіщення, які будуть успадковані в розділі сповіщень тьютора'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Дозволяє темпоралізувати розділи в рамках конкурсу заявок'
configuration_cliente_announcement.BONIFICATION.description: 'Бонус за дзвінок, особливо за дзвінок тристороннього фонду'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Дозволяє отримати доступ до вмісту виклику після його завершення'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Дозволяє увімкнути цифровий підпис в оголошенні про прийом заявок, особливо для очних курсів'
configuration_cliente_announcement.COST.description: 'Дозвольте клієнтам розподіляти витрати на дзвінок.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Сповіщення про активацію виклику (email, фронтальне сповіщення)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Ця конфігурація спеціально розроблена для клієнтів Iberostar, щоб не впливати на потік коштів'
configuration_cliente_announcement.REPORT.description: 'Увімкніть звітування у конкурсах заявок'
type_course_announcement_step_creation.seleccionar_curso.name: 'Виберіть курс'
type_course_announcement_step_creation.seleccionar_curso.description: 'Виберіть курс, для якого потрібно створити виклик'
type_course_announcement_step_creation.convocatoria.name: 'Конкурс заявок на участь у конкурсі'
type_course_announcement_step_creation.convocatoria.description: 'Інформація про конкурс проектних заявок'
type_course_announcement_step_creation.bonificacion.name: Бонус
type_course_announcement_step_creation.bonificacion.description: 'Інформація про конкурс проектних заявок'
type_course_announcement_step_creation.alumnado.name: Випускники
type_course_announcement_step_creation.alumnado.description: 'Студенти додаються до курсу'
type_course_announcement_step_creation.grupos.name: Групи
type_course_announcement_step_creation.grupos.description: 'Інформація про групу деталізована, а також додано тьютора'
type_course_announcement_step_creation.comunicacion.name: Комунікація
type_course_announcement_step_creation.comunicacion.description: 'Цей крок залежить від того, чи активував клієнт зв''язок чи ні.'
type_course_announcement_step_creation.encuesta.name: Опитування
type_course_announcement_step_creation.encuesta.description: 'Цей крок залежить від того, активував клієнт опитування чи ні.'
type_course_announcement_step_creation.diploma.name: Диплом
type_course_announcement_step_creation.diploma.description: 'Цей крок залежить від того, чи активований диплом у замовника чи ні.'
type_course_announcement_step_creation.alertas.name: Сповіщення
type_course_announcement_step_creation.alertas.description: 'Це може залежати від конфігурації клієнта.'
type_diploma.easylearning.name: 'За замовчуванням'
type_diploma.easylearning.description: 'Це диплом компанії-клієнта'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'Це диплом Fundae'
type_diploma.hobetuz.name: Хобетуз
type_diploma.hobetuz.description: 'Це диплом Хобетуз'
type_money.euro.name: Євро
type_money.euro.country: Іспанія
type_money.dolar_estadounidense.name: 'Долар США'
type_money.dolar_estadounidense.country: 'Сполучені Штати'
section_default_front.mi_formacion.name: 'Моє навчання'
section_default_front.mi_formacion.description: 'У призначеному навчанні ви можете знайти всі курси, які вам призначені.'
section_default_front.formacion_adicional.name: 'Додаткове навчання'
section_default_front.formacion_adicional.description: 'У цьому розділі ви можете знайти всі відкриті курси кампусу.'
section_default_front.formacion_asignada.name: 'Призначене навчання'
section_default_front.formacion_asignada.description: 'У цьому розділі ви можете знайти всі призначені вам курси.'
setting.multi_idioma.name: Багатомовний
setting.multi_idioma.description: 'Багатомовний інтерфейс'
setting.default_lenguage.name: 'Мова за замовчуванням'
setting.default_lenguage.description: 'Мова інтерфейсу користувача системи за замовчуванням'
setting.languages.name: Мова
setting.languages.description: 'Мови, доступні в додатку'
setting.registro_libre.name: 'Безкоштовна реєстрація користувачів'
setting.registro_libre.description: 'Режим, який дозволяє користувачеві вільно реєструватися на платформі після заповнення відповідної форми.'
setting.opinion_plataforma.name: 'Думки платформи'
setting.opinion_plataforma.description: 'Думки платформи'
setting.validacion_automatica.name: 'Автоматична перевірка реєстрації користувача'
setting.validacion_automatica.description: 'Автоматична перевірка реєстрації користувача'
setting.filtros_plataforma.name: 'Фільтри на платформі'
setting.filtros_plataforma.description: 'Це потрібно для активації або деактивації фільтрів на платформі'
setting.itinearios_plataforma.name: 'Маршрути на платформі'
setting.itinearios_plataforma.description: 'Це активація або деактивація маршрутів на платформі'
setting.seccion_cursos.name: 'Розділи курсу [ПЕРЕДНІЙ]'
setting.seccion_cursos.description: 'Розділи курсу [ПЕРЕД],'
setting.set_points_course.name: 'Контрольні точки для курсу'
setting.set_points_course.description: 'Використовується для присвоєння балів курсам при створенні або редагуванні, особливо для електронних курсів'
setting.default_points_course.name: 'Бали за замовчуванням за курс'
setting.default_points_course.description: 'За використання ігрових формул, якщо розділ не є грою, нараховується половина балів'
setting.documentation_course.name: 'Загальна інформація'
setting.documentation_course.description: 'Активуйте froala, щоб додати загальну інформацію про курс на кроці 2 курсу'
setting.open_course.name: 'Відкриті курси'
setting.open_course.description: 'Це дозволяє додавати на платформу відкриті курси або проводити додаткове навчання'
setting.client_id.name: 'Ідентифікатор клієнта vimeo'
setting.client_id.description: 'Це ідентифікатор клієнта vimeo'
setting.client_secret.name: 'Секретний клієнт vimeo'
setting.client_secret.description: 'Секретний клієнт vimeo'
setting.access_token.name: 'Токен доступу'
setting.access_token.description: 'Токен доступу'
setting.user_id.name: 'Ідентифікатор користувача'
setting.user_id.description: 'Ідентифікатор зареєстрованого користувача vimeo'
setting.project_id.name: 'Тека з відео-розділом'
setting.project_id.description: 'Це ідентифікатор, за яким розміщуються ресурси відео-частини'
setting.project_id_resource_course.name: 'Комплект матеріалів (конкурс заявок)'
setting.project_id_resource_course.description: 'Це ідентифікатор папки, в якій зберігаються відео, пов''язані з матеріалами курсу та дзвінком'
setting.project_id_task_course.name: 'Папка ресурсів завдання'
setting.project_id_task_course.description: 'Це ідентифікатор папки, в якій зберігаються відео, пов''язані із завданнями курсу та дзвінком'
setting.project_id_video_Quiz.name: 'Папка з ресурсами відеовікторини'
setting.project_id_video_Quiz.description: 'Це ідентифікатор папки, в якій розміщені відео, пов''язані з грою-вікториною'
setting.project_id_Roleplay.name: 'Набір ресурсів для рольових ігор'
setting.project_id_Roleplay.description: 'Визначено для ресурсів відео типу в рольовій грі'
setting.upload_sudomain.name: 'Завантаження на субдомен'
setting.upload_sudomain.description: 'Ця змінна використовується для завантаження відео та SCORM-файлів, що дозволяє обійти обмеження Cloudflare'
setting.from_email.name: 'З електронної пошти'
setting.from_email.description: 'Саме звідси надсилаються електронні листи з платформи'
setting.from_name.name: 'З назви платформи'
setting.from_name.description: 'Назва платформи, вказана в електронних листах та дипломах'
setting.from_cif.name: 'Від CIF'
setting.from_cif.description: 'Номер ПДВ компанії'
setting.email_support.name: 'Підтримка електронною поштою'
setting.email_support.description: 'Ці електронні листи використовуються для надсилання повідомлень служби підтримки'
setting.email_support_register.name: 'Прийом адміністратора електронної пошти'
setting.email_support_register.description: 'Це електронна пошта, яка використовується для отримання запитів на реєстрацію на платформі'
setting.news.name: Новини
setting.news.description: Новини
setting.foro.name: Форум
setting.foro.description: Форум
setting.desafios.name: Виклики
setting.desafios.description: Виклики
setting.secciones.name: Розділи
setting.secciones.description: 'Ця змінна дозволяє налаштувати відображення розділів спереду'
setting.encuestas.name: Опитування
setting.encuestas.description: Опитування
setting.active_cron_exports.name: 'Активний експорт cron'
setting.active_cron_exports.description: 'Активний cron-експорт, який зазвичай використовується для експорту даних з платформи'
setting.gender_excel.name: 'Genero excel'
setting.gender_excel.description: 'Використовується для додавання статі в експортні таблиці Excel'
setting.code.name: Код
setting.code.description: 'Показати поле коду в експорті статистики курсу'
setting.finished_chapters.name: 'Завершені розділи'
setting.finished_chapters.description: 'показати завершені розділи в розділі курс-статистика-експорт'
setting.zoom_cliente_id.name: 'Збільшити ідентифікатор клієнта'
setting.zoom_cliente_id.description: 'Ідентифікатор клієнта Zoom - необхідний для використання API Zoom'
setting.zoom_cliente_secret.name: 'Збільшити Таємниця клієнта'
setting.zoom_cliente_secret.description: 'клієнтський ключ масштабування - необхідний для використання API масштабування'
setting.zoom_account_id.name: 'Збільшити ідентифікатор облікового запису'
setting.zoom_account_id.description: 'Номер рахунку клієнта Zoom - необхідний для використання API Zoom'
setting.zoom_email.name: 'Збільшити електронну пошту'
setting.zoom_email.description: 'клієнт масштабування пошти - необхідний для використання API масштабування'
setting.clickmeeting_api_key.name: 'Ключ API ClickMeeting'
setting.clickmeeting_api_key.description: 'Ідентифікатор клієнта ClickMeeting - необхідний для використання API ClickMeeting'
setting.clikmeeting_dirbase.name: 'Клацніть на директорію бази зустрічей'
setting.clikmeeting_dirbase.description: 'Адреса сервера ClickMeeting'
setting.clikmeeting_events_paralel.name: 'Паралельні заходи ClickMeeting'
setting.clikmeeting_events_paralel.description: 'Кількість укладених контрактів на проведення паралельних заходів'
setting.plugnmeet_serverurl.name: 'Урл сервера plugNmeet'
setting.plugnmeet_serverurl.description: 'Адреса сервера plugNmeet'
setting.plugnmeet_api_key.name: 'Ключ API plugNmeet'
setting.plugnmeet_api_key.description: 'PlugNmeet ID клієнта'
setting.plugnmeet_secret.name: 'PlugNmeet секретний ключ'
setting.plugnmeet_secret.description: 'Ключ клієнта plugNmeet'
setting.plugnmeet_analyticsurl.name: 'Аналітика URL-адрес plugNmeet'
setting.plugnmeet_analyticsurl.description: 'Адреса сервера plugNmeet для аналітики'
setting.zoom_urlreports.name: 'Збільшити URL звіту'
setting.zoom_urlreports.description: 'Адреса, де зберігаються звіти про масштабування'
setting.plugnmeet_urlreports.name: 'PlugNmeet url звітування'
setting.plugnmeet_urlreports.description: 'Адреса, де зберігаються звіти plugNmeet'
setting.clickmeeting_urlreports.name: 'Натисніть URL-адресу звіту про зустріч'
setting.clickmeeting_urlreports.description: 'Адреса, де зберігаються звіти ClickMeeting'
setting.library_enabled.name: 'Бібліотеку увімкнено'
setting.library_enabled.description: 'Бібліотеку увімкнено'
setting.library_audio_local.name: 'Локальне аудіо бібліотеки'
setting.library_audio_local.description: 'Локальне аудіо бібліотеки'
setting.library_audio_path.name: 'Бібліотечний аудіошлях'
setting.library_audio_path.description: 'Бібліотечний аудіошлях'
setting.library_file_path.name: 'Шлях до файлу бібліотеки'
setting.library_file_path.description: 'Шлях до файлу бібліотеки'
setting.library_data_page_size.name: 'Розмір сторінки даних бібліотеки'
setting.library_data_page_size.description: 'Розмір сторінки даних бібліотеки'
setting.library_comments.name: 'Коментарі від бібліотеки'
setting.library_comments.description: 'Коментарі від бібліотеки'
setting.challenge_loops.name: 'Проблеми з циклом'
setting.challenge_loops.description: 'Проблеми з циклом'
setting.points_for_win.name: 'Бали за перемогу'
setting.points_for_win.description: 'Бали за перемогу'
setting.points_for_lose.name: 'Бали за програш'
setting.points_for_lose.description: 'Бали за програш'
setting.points_fortie.name: 'Очки за нічию'
setting.points_fortie.description: 'Очки за нічию'
setting.points_corrects.name: 'Бали за правильну'
setting.points_corrects.description: 'Бали за правильну'
setting.points_for_left.name: 'Залишилося балів'
setting.points_for_left.description: 'Залишилося балів'
setting.total_duels.name: 'Загальна кількість дуелей'
setting.total_duels.description: 'Загальна кількість дуелей'
setting.seconds_per_question.name: 'Секунди на запитання'
setting.seconds_per_question.description: 'Секунди на запитання'
setting.user_dni.name: 'Ідентифікатор користувача'
setting.user_dni.description: 'З''являється при створенні або редагуванні користувача'
setting.edit_code.name: 'Код для редагування'
setting.edit_code.description: 'Це унікальний ідентифікатор користувача'
setting.stats_acumulative.name: 'Кумулятивна статистика'
setting.stats_acumulative.description: 'Це на випадок, якщо ви хочете, щоб статистика була кумулятивною'
setting.maximo_fechas.name: 'Максимальний діапазон дат'
setting.maximo_fechas.description: 'Максимальний діапазон дат для консультацій'
setting.maximo_horas.name: 'Максимальна кількість запитів на годину'
setting.maximo_horas.description: 'Максимальна кількість запитів на годину'
setting.maximo_dia.name: 'Максимальна кількість запитів на день'
setting.maximo_dia.description: 'Максимальна кількість запитів на день'
setting.fundae.name: Fundae
setting.fundae.description: 'Якщо ця опція увімкнена, при публікації виклику користувачі повинні заповнити всі необхідні поля таблиці users_extra_fundae'
setting.margen_entrada.name: 'Вхідний запас за замовчуванням'
setting.margen_entrada.description: 'Поле введення за замовчуванням, яке використовується в QR-коді'
setting.margen_salida.name: 'Вихідний запас за замовчуванням'
setting.margen_salida.description: 'За замовчуванням виводиться поле, яке використовується в QR-коді'
setting.registrar_qr.name: 'Зареєструйтеся за допомогою QR-сесії'
setting.registrar_qr.description: 'Якщо ця опція увімкнена, сеанси будуть реєструватися за допомогою QR-коду'
setting.maximo_alumnos.name: 'Максимальна кількість студентів у групі'
setting.maximo_alumnos.description: 'Максимальна кількість студентів у групі'
setting.min_score.name: 'Мінімальний прохідний бал'
setting.min_score.description: 'Мінімальний прохідний бал'
setting.types_action.name: 'Типи дій'
setting.types_action.description: 'Типи дій'
setting.materiales_convocatoria.name: 'Уможливлення створення матеріалів у конкурсах проектних заявок'
setting.materiales_convocatoria.description: 'Уможливлення створення матеріалів у конкурсах проектних заявок'
setting.tareas_convocatoria.name: 'Увімкніть створення завдань у конкурсі пропозицій'
setting.tareas_convocatoria.description: 'Увімкніть створення завдань у конкурсі пропозицій'
setting.minimo_minutos.name: 'Мінімальний час простою в хвилинах'
setting.minimo_minutos.description: 'Мінімальний час простою в хвилинах, стосується користувачів, які знаходяться на платформі'
setting.timezones.name: 'Часові пояси, дозволені в конкурсі заявок'
setting.timezones.description: 'Часовий пояс, який можна налаштувати в конкурсі заявок'
catalog.1.name: 'Типи розділів'
catalog.1.description: 'Конфігурація типів розділів, які будуть доступні на платформі'
catalog.2.name: 'Типи курсів'
catalog.2.description: 'Конфігурація типів курсів, які будуть доступні на платформі'
catalog.3.name: 'Критерії схвалення'
catalog.3.description: 'Конфігурація критеріїв затвердження, які будуть доступні на платформі'
catalog.4.name: 'Сповіщення про репетиторів'
catalog.4.description: 'Налаштування сповіщень для тьюторів, які будуть доступні на платформі'
catalog.5.name: 'Типи дипломів'
catalog.5.description: 'Конфігурація типів дипломів, які будуть доступні на платформі'
catalog.6.name: 'Конфігурація клієнта у виклику'
catalog.6.description: 'Конфігурація кроків, які будуть відображатися в оголошенні про конкурс'
catalog.7.name: 'Типи монет'
catalog.7.description: 'Конфігурація типів валют, які будуть доступні на платформі'
catalog.8.name: 'Група конфігурацій'
catalog.8.description: 'Група конфігурацій, які будуть доступні на платформі'
catalog.9.name: Конфігурації
catalog.9.description: 'Конфігурації для кожної групи, які будуть доступні на платформі'
catalog.10.name: Компанія
catalog.10.description: 'Компанії-користувачі, які будуть доступні на платформі'
catalog.11.name: 'Професійна категорія'
catalog.11.description: 'Професійні категорії користувачів, які будуть доступні на платформі'
catalog.12.name: 'Робочий центр користувача'
catalog.12.description: 'Робочі місця для користувачів, які будуть доступні на платформі'
catalog.13.name: 'Відділ роботи з користувачами'
catalog.13.description: 'Відділи роботи для користувачів, які будуть доступні на платформі'
catalog.14.name: 'Рівень вивчення користувача'
catalog.14.description: 'Рівні навчання користувачів, які будуть доступні на платформі'
catalog.15.name: 'Кроки для різних типів курсів'
catalog.15.description: 'Конфігурація кроків для різних типів курсів, які будуть доступні на платформі'
catalog.16.name: 'Типи віртуальних класів'
catalog.16.description: 'Типи віртуальних класів для різних типів курсів, які будуть доступні на платформі'
catalog.17.name: 'Види ідентифікації'
catalog.17.description: 'Типи ідентифікації, доступні на платформі'
nps_question.text.name: Текст
nps_question.text.descripction: 'Поділіться з нами своєю думкою'
setting.help.user.name: 'Включити довідку у форматі pdf у меню користувача'
setting.help.user.description: 'Ця допомога була створена спеціально для iberostar'
catalog.18.name: 'Умови для особистих дзвінків'
catalog.18.description: 'Це особлива потреба для Iberostar'
setting.userPolicies_plataforma.name: 'Політика конфіденційності'
setting.userPolicies_plataforma.description: 'Ця змінна використовується, щоб увімкнути модальне вікно на інтерфейсі, коли користувач не приймає політику конфіденційності'
setting.course.tab.person.name: 'Деталі курсу Tab people'
setting.course.tab.stats.name: "Деталі курсу \"Статистична таблиця\n"
setting.course.tab.opinions.name: "Деталі курсу \"Вкладка думок\n"
setting.documentation.name: Документація
setting.documentation.description: 'Активуйте модуль Документація в бічному меню'
setting.user_company.name: Компанії
setting.user_company.description: 'Активуйте модуль "Компанії" в бічному меню'
setting.pages.name: 'Нижній колонтитул'
setting.pages.description: 'Активувати нижній колонтитул на кампусі'
setting.lite_formation.name: 'Навчальна група із загальної статистики'
setting.lite_formation.description: 'Навчальна група із загальної статистики'
setting.lite_formation.formationHours.name: 'Навчальні години'
setting.lite_formation.formationHours.description: 'Загальна кількість годин навчання та середня кількість годин навчання на одну особу'
setting.lite_formation.peopleWithCourses.name: 'Люди з курсами'
setting.lite_formation.peopleWithCourses.description: 'Особи, які наразі проходять навчання, та особи, які закінчили хоча б один курс'
setting.lite_formation.courseStartedAndFinished.name: 'Розпочаті, поточні та завершені курси'
setting.lite_formation.courseStartedAndFinished.description: 'Кількість розпочатих, поточних та завершених курсів'
setting.lite_formation.requiredCourses.name: 'Обов''язкові курси'
setting.lite_formation.requiredCourses.description: 'Обов''язкові курси, призначені для виклику або траєкторії'
setting.lite_formation.general.name: Генерал
setting.lite_formation.general.description: Генерал
setting.lite_formation.openedCourses.name: 'Відкриті курси'
setting.lite_formation.openedCourses.description: 'Добровільні курси'
setting.lite_formation.educativeStatus.name: 'Рівень освіти'
setting.lite_formation.educativeStatus.description: 'Статус навчання за рівнями балів'
setting.lite_formation.gamifiedPills.name: 'Гейміфіковані таблетки'
setting.lite_formation.gamifiedPills.description: 'Кількість гейміфікованих розділів, невдач та успіхів у гейміфікованих вікторинах'
setting.lite_formation.gamifiedTest.name: Тест-таблетки
setting.lite_formation.gamifiedTest.description: 'Використані гейміфіковані тести та успіхи і промахи за типами тестів'
setting.lite_formation.peoplePerformance.name: 'Продуктивність людей'
setting.lite_formation.peoplePerformance.description: 'Ефективність роботи людей'
setting.lite_formation.coursesByStars.name: 'Курси за кількістю балів'
setting.lite_formation.coursesByStars.description: 'Зірковий рейтинг курсів'
setting.lite_formation.structureAndHotel.name: 'Квартири та готелі'
setting.lite_formation.structureAndHotel.description: 'Відсоток по групах'
setting.lite_formation.schoolFinishedAndProgress.name: 'Школа завершена і триває'
setting.lite_formation.schoolFinishedAndProgress.description: 'Школа з найбільшою кількістю учасників, курсів, що тривають, та завершених курсів'
setting.lite_formation.coursesBySchool.name: 'Курси за школами'
setting.lite_formation.coursesBySchool.description: 'Кількість курсів за категоріями'
setting.lite_formation.coursesByDepartment.name: 'Курси за факультетами'
setting.lite_formation.coursesByDepartment.description: 'Створення курсів за факультетами'
setting.lite_formation.usersMoreActivesByCourses.name: 'Найактивніші користувачі за курсом'
setting.lite_formation.usersMoreActivesByCourses.description: 'Найактивніші та найменш активні учасники завершених курсів'
setting.lite_evolution.name: 'Група розвитку загальної статистики'
setting.lite_evolution.description: 'Група розвитку загальної статистики'
setting.lite_evolution.trainedPerson.name: 'Підготовлені працівники'
setting.lite_evolution.trainedPerson.description: 'Особи, які закінчили хоча б один курс'
setting.lite_evolution.startedCourses.name: 'Розпочалися курси'
setting.lite_evolution.startedCourses.description: 'Розпочалися курси'
setting.lite_evolution.proccessCourses.name: 'Поточні курси'
setting.lite_evolution.proccessCourses.description: 'Поточні курси'
setting.lite_evolution.finishedCourses.name: 'Закінчені курси'
setting.lite_evolution.finishedCourses.description: 'Закінчені курси'
setting.lite_evolution.segmentedHours.name: 'Сегментація годин'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Нові користувачі, які пройшли курс'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Новачки на платформі, які пройшли принаймні один курс'
setting.lite_demography.name: 'Демографічна група в загальній статистиці'
setting.lite_demography.description: 'Демографічна група в загальній статистиці'
setting.lite_demography.usersBySexAndAge.name: 'Користувачі за статтю та віком'
setting.lite_demography.usersBySexAndAge.description: 'Користувачі за статтю та віком'
setting.lite_demography.ageDistribution.name: 'Віковий розподіл'
setting.lite_demography.ageDistribution.description: 'Віковий розподіл'
setting.lite_demography.deviceDistribution.name: 'Розподіл за пристроями'
setting.lite_demography.deviceDistribution.description: 'Розподіл за пристроями'
setting.lite_demography.usersByCountries.name: 'Розподіл за країнами'
setting.lite_demography.usersByCountries.description: 'Розподіл за країнами'
setting.lite_activity.name: 'Група активності "Загальна статистика'
setting.lite_activity.description: 'Група активності "Загальна статистика'
setting.lite_activity.activityInfo.name: 'Інформація про діяльність'
setting.lite_activity.activityInfo.description: 'Активні на порталі особи, зареєстровані особи, особи, які входили на портал принаймні один раз за останні 30 днів, деактивовані особи та особи, які ніколи не входили на портал'
setting.lite_activity.accessDays.name: 'Доступ по днях'
setting.lite_activity.accessDays.description: 'Дні доступу'
setting.lite_activity.platformAccessByHours.name: 'Доступ за платформою та часом'
setting.lite_activity.platformAccessByHours.description: 'Час доступу до платформи за днями та годинами (теплова карта)'
setting.lite_activity.courseStartTime.name: 'Розподіл за часом початку курсів'
setting.lite_activity.courseStartTime.description: 'Час початку курсу'
setting.lite_activity.courseEndTime.name: 'Розподіл за годинами проходження курсу'
setting.lite_activity.courseEndTime.description: 'Години проходження курсу (теплова карта)'
setting.lite_activity.coursesStartedVsFinished.name: 'Розпочаті курси у порівнянні з завершеними курсами'
setting.lite_activity.coursesStartedVsFinished.description: 'Розпочаті курси проти завершених курсів'
setting.lite_activity.usersMoreActivesByActivity.name: 'Найактивніші користувачі'
setting.lite_activity.usersMoreActivesByActivity.description: 'Найактивніші та найменш активні люди та час, проведений ними на платформі'
setting.lite_itinerary.name: 'Маршрути в групі загальної статистики'
setting.lite_itinerary.description: 'Маршрути в групі загальної статистики'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Розпочаті та завершені маршрути'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Розпочаті та завершені маршрути'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Завершені маршрути за країнами'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Завершені маршрути за країнами'
setting.survey.hide_empty_comment.name: 'Приховати думку з порожнім коментарем'
setting.survey.hide_empty_comment.description: 'Опитування, приховувати ім''я, коли коментар порожній'
setting.survey.show_only_ratings.name: 'Опитування, показувати тільки назви кваліфікацій'
setting.survey.show_only_ratings.description: 'Опитування показує лише назву кваліфікації'
app.survey.post_nps.enabled.name: 'Публікація опитування імені nps включена'
app.survey.post_nps.enabled.description: 'Назва публікації опитування додатку nps увімкнено'
setting.lite_evolution.segmentedHours.description: Години
setting.course.tab.person.description: 'Люди, деталі курсу'
setting.course.showDeactivatedCourses.name: 'Вкладка показує назву деактивованих курсів'
setting.course.showDeactivatedCourses.description: 'Відображає назви деактивованих курсів'
catalog.19.name: 'Адміністратор перекладів'
catalog.19.description: 'Переклади адміністратора'
setting.lenguage.platform: 'Переклади адміністратора'
setting.module.announcement.name: 'Конкурс заявок на участь у конкурсі'
setting.module.announcement.description: 'Активуйте модуль дзвінків у підменю курсів'
course.diploma.index: Зміст
setting.zip.day_available_until.name: 'Доступні дні'
setting.zip.day_available_until.description: 'Кількість днів до автоматичного видалення архіву.'
catalog.20.name: 'Виклик додаткових полів'
course.diploma.filters: 'Активація додаткових фільтрів у звіті про диплом'
setting.lenguage.platform.description: 'Мови, доступні в панелі адміністратора'
translations_admin.title1: 'Призначене навчання'
translations_admin.title2: 'Додаткове навчання'
translations_admin.title3: 'Призначені курси'
translations_admin.title4: 'Добровільні курси'
setting.course.tab.stats.description: 'Увімкнено активує розділ "Статистика" на детальному рівні курсу.'
setting.course.tab.options.description: 'Увімкнено - активує розділ "Зворотній зв''язок" на рівні деталізації курсу.'
course.diploma.index.description: 'Увімкнено Розділ "Дипломи" активується при створенні/модифікації курсу'
setting.use.filter_in_ranking.name: 'Використовуйте фільтри в рейтингу користувачів'
setting.use.filter_in_ranking.description: 'Дозволяє вибрати з меню фільтри категорій, за якими користувач бажає, щоб його порівнювали. Якщо цю опцію вимкнено, користувач буде порівнюватися за замовчуванням з усіма фільтрами, доступними на платформі'
setting.use.include_only_first_category_name: 'Показувати тільки першу категорію фільтра в рейтингу'
setting.use.include_only_first_category_description: 'Якщо ця опція активна, відображається лише перше посилання користувача. В іншому випадку відображаються всі пов''язані категорії. Наприклад, якщо категорія "країна", користувач може бути пов''язаний як з Іспанією, так і з Нікарагуа.'
setting.email_support_error.name: 'Пошта підтримки щодо помилок'
setting.email_support_error.description: 'Пошти, на які будуть надсилатися повідомлення про інциденти на платформі'
setting.export.task.slot_quantity.name: 'Кількість слотів завдань на користувача'
setting.export.task.slot_quantity.description: 'Кількість слотів, доступних для обробки завдань експорту на одного користувача.'
setting.export.task.long_running_type_tasks.name: 'Типи довгострокових завдань'
setting.export.task.long_running_type_tasks.description: 'Перелік типів завдань, які вважаються довготривалими для експорту.'
setting.export.zip_task.slot_quantity.name: 'Кількість слотів для завдань zip на користувача'
setting.export.zip_task.slot_quantity.description: 'Кількість слотів, доступних для обробки завдань стиснення архівів на одного користувача.'
setting.export.zip_task.long_running_type_tasks.name: 'Типи довгострокових завдань zip'
setting.export.zip_task.long_running_type_tasks.description: 'Список типів zip-завдань, які вважаються довготривалими.'
setting.export.task.user_pending_max_count_task.name: 'Максимальна кількість відкладених завдань на одного користувача'
setting.export.task.user_pending_max_count_task.description: 'Максимальна кількість завдань, які користувач може мати в черзі.'
setting.export.task.timeout.name: 'Обмеження часу на виконання завдань'
setting.export.task.timeout.description: 'Максимальний час у секундах, за який завдання експорту вважається виконаним.'
setting.export.zip_task.timeout.name: 'Обмеження часу на виконання zip-завдань'
setting.export.zip_task.timeout.description: 'Максимальний час у секундах, за який завдання стиснення архіву вважається виконаним.'
setting.export.task.timeout_seconds.name: 'Час таймауту для завдань у стані TIMEOUT'
setting.export.task.timeout_seconds.description: 'Максимальний час у секундах, після якого завдання зі статусом TIMEOUT більше не вважається запущеним.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'Це іменний диплом для компанії Novomatic'
app.announcement.managers.sharing.name: 'Увімкніть створення завдань у конкурсі пропозицій'
app.announcement.managers.sharing.description: 'Увімкніть створення завдань у конкурсі пропозицій'
