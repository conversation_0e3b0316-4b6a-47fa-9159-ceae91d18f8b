challenges.challenge_title: '<PERSON><PERSON><PERSON> der Herausforderung'
challenges.no_images: '<PERSON><PERSON>'
challenges.import_questions_to_excel: 'Fragen zum Excel-Import'
challenges.import_from_excel: 'Aus Excel importieren'
challenges.guest: <PERSON><PERSON>
challenges.add_user: '<PERSON><PERSON><PERSON> hinzuf<PERSON>gen'
challenges.performance: <PERSON><PERSON><PERSON>
challenges.points: <PERSON><PERSON>
challenges.downloadtemplate: 'Vorlage herunterladen'
challenges.import: Importieren
observations.addfiles: 'Datei hinzufügen'
challenges.challenges: Her<PERSON>forderung
questions.manageresponse: 'Verwal<PERSON> von Antworten'
questions.see_answers: '<PERSON><PERSON><PERSON> Antworten'
clone_course.title: 'wollen Sie diesen Kurs wirklich wiederholen?'
clone_course.content: 'Mit dieser Aktion wird eine Kopie der Kursinhalte erstellt.'
clone_course.clonarcourse: Klonkurs
fundae_catalogs.user_professional_category.not_label_in_plural: 'Es gibt keine Berufskategorien'
menu.users_managment.nomanagers: 'Keine Manager zugewiesen'
message_api.forcingdisplaymenu: 'Menüanzeige erzwingen'
messages.notcontainchapter: '<PERSON><PERSON> hat keinen Inhalt'
messages.nologinuser: 'Benutzer nicht eingeloggt'
messages.seesummary: 'siehe Zusammenfassung'
messages.e_learning: E-learning
messages.editchallenges: 'Herausforderung bearbeiten'
messages.challengeindex: 'Index der Herausforderungen'
messages.createnew: 'Neu erstellen'
messages.createnewchallenge: 'Neue Herausforderung erstellen'
messages.infgroup: 'Informationen zur Gruppe'
message_api.scorm.score_system_desc: 'Standardmäßig gilt die Trainingsmaßnahme als bestanden, wenn der Benutzer die gesamte Aktivität "abschließt" oder "besteht". Wenn die Schulungsmaßnahme ein Punktesystem hat, um bestanden zu werden, aktivieren Sie die folgende Option:'
message_api.scorm.score_system_label: 'Mindestpunktzahl (zwischen 0 und 100)'
message_api.scorm.allow_reset_desc: 'möchten Sie die Fortbildungsmaßnahme nach ihrer Beendigung erneut einsehen können?'
message_api.scorm.allow_reset_label: 'Zurücksetzen zulassen'
message_api.scorm.config_label: 'Konfiguration:'
message_api.scorm.config_desc: 'Die Aktivität ist abgeschlossen, wenn:'
