challenges.challenge_title: 'Titlul provocării'
challenges.no_images: '<PERSON><PERSON><PERSON><PERSON> imagine'
challenges.import_questions_to_excel: 'Import Excel întrebări'
challenges.import_from_excel: 'Import din Excel'
challenges.guest: Invitat
challenges.add_user: 'Adăugați utilizator'
challenges.performance: Performanț<PERSON>
challenges.points: Puncte
challenges.downloadtemplate: 'Descărcați șablonul'
challenges.import: Import
observations.addfiles: 'Adăugați fișier'
challenges.challenges: Provocare
questions.manageresponse: 'Gestionarea răspunsurilor'
questions.see_answers: 'Vezi răspunsurile'
clone_course.title: 'chiar doriți să duplicați acest curs?'
clone_course.content: 'Această acțiune va crea o copie a conținutului cursului.'
clone_course.clonarcourse: 'Curs de clonare'
fundae_catalogs.user_professional_category.not_label_in_plural: 'Nu există categorii profesionale'
menu.users_managment.nomanagers: 'Nu există manageri desemnați'
message_api.forcingdisplaymenu: 'Forțați afișarea meniului'
messages.notcontainchapter: 'Acest capitol nu are niciun conținut'
messages.nologinuser: 'Utilizatorul nu este conectat'
messages.seesummary: 'a se vedea rezumatul'
messages.e_learning: E-learning
messages.editchallenges: 'Editează provocarea'
messages.challengeindex: 'Indexul provocărilor'
messages.createnew: 'Crearea de noi'
messages.createnewchallenge: 'Creați o nouă provocare'
messages.infgroup: 'Informații despre grup'
message_api.scorm.score_system_desc: 'În mod implicit, acțiunea de formare va fi considerată ca fiind aprobată dacă utilizatorul "Finalizează sau "Trece" toate activitățile. Dacă acțiunea de formare are un sistem de notare pentru a fi aprobată, activați următoarea opțiune:'
message_api.scorm.score_system_label: 'Punctaj minim (între 0 și 100)'
message_api.scorm.allow_reset_desc: 'doriți să puteți consulta din nou acțiunea de formare după ce aceasta a fost finalizată?'
message_api.scorm.allow_reset_label: 'Permiteți resetarea'
message_api.scorm.config_label: 'Configurație:'
message_api.scorm.config_desc: 'Activitatea este finalizată atunci când:'
