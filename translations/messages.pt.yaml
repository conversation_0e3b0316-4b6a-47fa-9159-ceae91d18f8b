menu.title_platform: 'Campus de formação'
complete: Concluído
Show: Ver
Edit: Modificar
Remove: Remover
Delete: Eliminar
total: Total
'Yes': Sim
'No': Não
Actions: Acções
Clear: Limpo
'No results found': 'Não foram encontrados resultados'
Configuration: Configuração
Limit: Limite
Close: Fechar
Save: Guardar
'Save and create other': 'Criar e adicionar outro'
'Save changes': 'Guardar alterações'
'Save and keep editing': 'Guardar e continuar a editar'
state: Estado
create: Criar
cancelar: Cancelar
back: 'Regressar a'
add: Adicionar
no_content: 'Sem conteúdo'
no_result: 'Não foram encontrados resultados'
configure_simulator: 'Configurar o simulador'
edit_configure_simulator: 'Editar configuração'
configure_success: 'A configuração foi bem sucedida'
save_success: 'Registo guardado com sucesso'
error_success: 'Ocorreu um erro ao guardar o registo'
configure_completed: 'Configuração concluída'
'Created At': <PERSON><PERSON>o
'Created By': 'Criado por'
'Created by': 'Criado por'
'Updated At': Atualizado
'Updated By': 'Atualizado por'
'Deleted By': 'Eliminado por'
'Deleted At': Eliminado
menu.courses_managment.title: 'Gestão de cursos'
menu.courses_managment.Segments: Segmentos
menu.courses_managment.categories: Categorias
menu.courses_managment.level: Níveis
menu.courses_managment.courses: Cursos
menu.courses_managment.announcements: Chamada
menu.courses_managment.nps_question: 'Perguntas do NPS'
menu.courses_managment.opinion_course: 'Opiniões sobre o curso'
menu.help_managment.title: 'Gestão da ajuda'
menu.help_managment.content_help: 'Ajuda de conteúdo'
menu.help_managment.categories_help: 'Categoria ajuda'
menu.users_managment.title: 'Gestão de utilizadores'
menu.users_managment.users: Utilizadores
menu.users_managment.managers: Gestores
menu.users_managment.filter: Filtros
menu.news.title: Notícias
menu.stats.title: Estatísticas
menu.stats.export: 'Ferramenta Excel'
menu.users.edit_profile: 'Editar perfil'
form.label.delete: Eliminar
action.save: 'Guardar agora'
common_areas.created_at: Criado
common_areas.updated_at: Atualizado
common_areas.deleted_at: Atualizado
common_areas.created_by: 'Criado por'
common_areas.updated_by: 'Atualizado por'
common_areas.actions: Acções
common_areas.basic_information: 'Informações de base'
common_areas.edit: Editar
common_areas.delete: Eliminar
common_areas.name: Nome
common_areas.image: Imagem
common_areas.state: Estado
common_areas.create: Criar
common_areas.save: Guardar
common_areas.back_list: 'Voltar à lista'
course_category.label_in_singular: 'Categoria do curso'
course_category.label_in_plural: 'Categorias de cursos'
course_category.configureFields.category_name: 'Categorias de cursos'
course_category.configureFields.category_order: 'Ordem de categoria'
course_category.configureFields.translations: Traduções
course.label_in_singular: Curso
course.label_in_plural: Cursos
course.back_to_course: 'Voltar ao curso'
course.configureFields.basic_information: 'Informações de base'
course.configureFields.code: Código
course.configureFields.name: Nome
course.configureFields.description: Descrição
course.configureFields.basic: Básico
course.configureFields.access_level: 'Nível de acesso'
course.configureFields.clone: Clone
course.configureFields.open: Aberto
course.configureFields.open_visible: 'Visível no campus aberto'
course.configureFields.active: Publicado
course.configureFields.categories: Categorias
course.configureFields.profesional_categories: 'Categorias profissionais'
course.configureFields.image: Imagem
course.configureFields.chapter: Capítulo
course.configureFields.translation: Tradução
course.configureFields.general_information: 'Informações gerais'
course.configureFields.segment: Segmento
course.configureFields.category: Categoria
course.configureFields.thumbnail_url: 'Miniatura do URL'
course.configureFields.locale: Língua
course.configureFields.all_seasons: 'Todas as estações'
course.configureFields.chapters: Capítulos
course.configureFields.seasons: Temporadas
course.configureFields.courses_translate: Traduções
course.configureFields.add_chapter: 'Adicionar capítulo'
course.configureFields.no_seasons: 'Sem temporada'
course.configureFields.add_seasons: 'Adicionar temporada'
course.configureFields.add_annuncement: 'Adicionar chamada'
course.configureFields.question_modal_translate: 'Quer mesmo traduzir este curso?'
course.configureFields.content_modal_translate: 'Esta ação criará uma cópia da disciplina para ser utilizada como guia para a tradução noutra língua.'
course.configureFields.translate_already: 'Este curso já tem uma tradução nesta língua'
course.configureFields.tag_description: 'Separar etiquetas premindo enter'
course.configureFields.new: Novo
course.configureFields.add_material: 'Adicionar material'
course.configureFields.add_task: 'Adicionar tarefa'
course.configureFields.task: Tarefas
course.season_add: 'A estação foi acrescentada corretamente'
course.season_update: 'A época foi corretamente actualizada'
course.season_add_error: 'Ocorreu um erro ao adicionar a estação'
course.panel.class: 'Detalhes do curso'
chapter.label_in_plural: Capítulos
chapter.configureFields.title: Título
chapter.configureFields.course: Curso
chapter.configureFields.type: Tipo
chapter.configureFields.season: Temporada
chapter.configureFields.description: Descrição
chapter.configureFields.image: Imagem
chapter.configureFields.image_file: 'Imagem do ficheiro'
chapter_type.description.1: '<p>O capítulo Scorm é muito interessante:</p><p>Permite-nos carregar uma vasta gama de conteúdos gerados com outras ferramentas, por exemplo, documentos, conteúdos interactivos e até jogos.</p>'
chapter_type.description.2: '<p>Este é um dos capítulos mais versáteis.</p><p>No lado esquerdo, são mostrados os títulos inseridos, que servem como um índice para encontrar o conteúdo rapidamente e facilitar a leitura.</p>'
chapter_type.description.3: '<p>Trata-se de um jogo de perguntas e respostas que acrescenta uma componente aleatória, uma vez que é necessário completar os segmentos de uma roleta para passar.</p><p>Baseia-se na criação de uma bateria de perguntas para reforçar os conhecimentos adquiridos. Pode introduzir o número de perguntas que quiser e também acompanhá-las com imagens.</p><p>Para que o jogo funcione corretamente, é aconselhável incluir um mínimo de 10 perguntas. </p>'
chapter_type.description.4: '<p>Este jogo apresenta uma série de perguntas que incluem um fator de risco adicional. Depois de cada pergunta, os participantes têm a opção de ficar de pé e manter a pontuação atual, ou correr o risco de responder a uma pergunta adicional para obter mais pontos. No entanto, se a resposta for incorrecta, todos os pontos acumulados até esse momento são perdidos.</p>'
chapter_type.description.5: '<p>Este é o capítulo mais clássico do jogo.</p><p>A ideia é criar uma bateria de perguntas para reforçar os conhecimentos aprendidos. Pode introduzir um número ilimitado de perguntas acompanhadas de uma imagem e com apenas uma resposta correcta.</p>'
chapter_type.description.6: '<p>Distribuir e rodar as peças até ficarem na posição e orientação correctas, caso contrário não se encaixam.</p><p>Na parte superior existem quatro segmentos que correspondem ao tempo disponível para completar o puzzle. Quando um segmento de tempo se esgota, é colocada uma das perguntas que introduzimos. Se acertar nas perguntas, ganha-se mais tempo para resolver o puzzle. A pontuação final dependerá de uma combinação do tempo necessário para completar o puzzle, do número de perguntas correctas respondidas e do número de perguntas falhadas.</p>'
chapter_type.description.7: '<p>Este jogo apresenta um puzzle baseado numa imagem. Para o resolver, tem de selecionar cuidadosamente as letras certas antes que o tempo se esgote.</p>'
chapter_type.description.8: '<p>Dado que el formato PDF está muy extendido en contenidos de diversa índole, como protocolos o manuales, los capítulos de tipo PDF son muy interesantes, ya que nos permiten reutilizar materiales ya editados.</p>'
chapter_type.description.9: '<p>Os recursos audiovisuais têm um grande potencial pedagógico, atraem, captam a atenção e despertam a curiosidade.</p><p>A plataforma permite-nos escolher como introduzir o vídeo através do "url", ou seleccionando um ficheiro que esteja no nosso computador. Neste último caso, podemos anexar um ficheiro de legendas.</p>'
chapter_type.description.10: '<p>Capítulo tipo slide com imagens.</p>'
chapter_type.description.11: '<p>O jogo consiste em fazer corresponder as palavras, cada uma das quais corresponde a uma letra da roda. Por vezes, a solução será uma palavra que começa com a letra e, por vezes, conterá simplesmente a letra.</p>'
chapter_type.description.12: '<p>Neste jogo, ser-lhe-á feita uma série de perguntas sob a forma de texto, imagem ou uma combinação de ambos, sob a forma de afirmações. Há duas respostas possíveis "Verdadeiro" ou "Falso" e apenas uma está correcta. O tempo para resolver o jogo é limitado.</p>'
chapter_type.description.13: '<p>Neste jogo, terás de resolver um enigma antes que o tempo acabe. A pista estará escondida por detrás de uma imagem desfocada que irá gradualmente tornar-se visível à medida que avança no jogo. Para além da imagem, há também ajuda adicional sob a forma de texto.</p>'
chapter_type.description.14: '<p>Neste jogo clássico, tens de organizar os elementos arrastando os blocos para a ordem correcta. A diversidade de possibilidades torna-o ideal para exercícios de matemática e outros desafios educativos. Ideal para criar um teste que desafie as capacidades de raciocínio e de ordenação.</p>'
chapter_type.description.15: '<p>Este jogo é ideal para treinar a memória e a concentração. O objetivo é encontrar todos os pares de cartas iguais. A localização das cartas é criada aleatoriamente, pelo que cada jogo será diferente.</p>'
chapter_type.description.16: '<p>Neste jogo, ser-te-á apresentada uma série de palavras, frases ou conceitos que deves associar à família ou grupo correspondente apresentado abaixo. Testarás as tuas capacidades de associação e rapidez mental enquanto corres contra o relógio.</p>'
chapter_type.description.17: '<p>Neste jogo de gramática e aprendizagem, o objetivo é preencher as lacunas das frases com as palavras certas para testar as suas competências linguísticas e gramaticais. Este jogo é versátil e pode ser utilizado para muitos outros fins educativos.</p>'
chapter_type.description.18: '<p>Neste jogo, ser-lhe-á apresentada uma pergunta ou um enigma para resolver. A tarefa será examinar cuidadosamente o enigma e utilizar as letras fornecidas para descobrir a palavra correcta. Mas tem cuidado, porque o tempo é limitado, o que significa que terás de ser rápido e preciso para ganhares.</p>'
chapter_type.description.19: '<p>Neste jogo, terás de adivinhar uma palavra escondida num máximo de seis tentativas. Cada tentativa consiste em introduzir uma palavra válida e, após cada tentativa, a cor dos quadrados muda para mostrar quais as letras correctas e quais as que estão na posição correcta.</p>'
chapter_type.description.20: '<p>Este jogo consiste em encontrar palavras escondidas num caça-palavras. O objetivo é marcar uma sequência de letras na horizontal, na vertical ou na diagonal. As palavras podem ser encontradas em ambas as direcções, da esquerda para a direita ou da direita para a esquerda. Se a sequência fizer parte de uma palavra escondida, será considerada uma resposta correcta.</p>'
chapter_type.description.21: '<p>Durante a reprodução de um vídeo, são inseridas perguntas interactivas que exigem que o espetador preste atenção ao conteúdo do vídeo para responder corretamente. Em suma, combinando o poder do vídeo com a interatividade do questionário para proporcionar uma experiência de aprendizagem eficaz e cativante.</p>'
chapter_type.add.1: 'Adicionar Scorm'
chapter_type.add.2: 'Adicionar conteúdo'
chapter_type.add.3: 'Criar jogo'
chapter_type.add.4: 'Criar jogo'
chapter_type.add.5: 'Criar questionário'
chapter_type.add.6: 'Criar jogo'
chapter_type.add.7: 'Criar jogo'
chapter_type.add.8: 'Adicionar PDF'
chapter_type.add.9: 'Adicionar vídeo'
chapter_type.add.10: 'Adicionar um seletor'
chapter_type.add.11: 'Criar jogo'
chapter_type.add.12: 'Criar jogo'
chapter_type.add.13: 'Criar jogo'
chapter_type.add.14: 'Criar jogo'
chapter_type.add.15: 'Criar jogo'
chapter_type.add.16: 'Criar jogo'
chapter_type.add.17: 'Criar jogo'
chapter_type.add.18: 'Criar jogo'
chapter_type.add.19: 'Criar jogo'
chapter_type.add.20: 'Criar jogo'
chapter_type.add.21: 'Criar um questionário em vídeo'
chapter_type.all: Todos
chapter_type.content: Teoria
chapter_type.games_test: Avaliação
chapter_type.description_test: 'Descrição do teste'
chapter_type.type: 'Tipo de capítulo'
chapter.add_pdf: 'Adicionar pdf'
chapter.chapter.show_video: 'Ver vídeo'
chapter.message_pdf_success: 'O PDF foi adicionado com sucesso'
chapter.message_pdf_error: 'Ocorreu um erro ao guardar o PDF'
chapter.chapter.materials: Materiais
chapter.chapter.show_pdf: 'Ver PDF'
announcements.label_in_singular: Anúncio
announcements.label_in_plural: Chamadas
announcements.configureFields.courses: Cursos
announcements.configureFields.start_at: Início
announcements.configureFields.finish_at: Fim
announcements.configureFields.called: chamado
announcements.configureFields.subsidized: Subsidiados
announcements.configureFields.subsidizer: Inspetor
announcements.configureFields.subsidizer_entity: 'Entidade subvencionadora'
announcements.configureFields.subsidized_announcement: 'Convite à apresentação de propostas'
announcements.configureFields.max_users: 'Máximo de utilizadores'
announcements.configureFields.formative_action_type: 'Tipo de ação de formação'
announcements.configureFields.format: Formato
announcements.configureFields.total_hours: 'Total de horas'
announcements.configureFields.place: Local
announcements.configureFields.training_center: 'Centro de formação'
announcements.configureFields.training_center_address: 'Endereço do centro de formação'
announcements.configureFields.training_center_nif: 'Centro de formação NIF'
announcements.configureFields.training_center_phone: 'Número de telefone do centro de formação'
announcements.configureFields.training_center_email: 'E-mail do centro de formação'
announcements.configureFields.training_center_teacher_dni: 'Centro de formação de professores DNI'
announcements.configureFields.called_user: 'Pessoas convocadas'
announcements.configureFields.search: Pesquisar
announcements.configureFields.announcement_for: 'Convite à apresentação de propostas'
announcements.configureFields.search_user_title: 'Procurar pessoas para convocar'
announcements.configureFields.placeholder_search_user: 'Procurar pessoas'
announcements.configureFields.placeholder_search_category: 'Pesquisa por categoria'
announcements.configureFields.placeholder_search_department: 'Pesquisa por departamento'
announcements.configureFields.placeholder_search_center: 'Pesquisa por centro'
announcements.configureFields.placeholder_search_country: 'Pesquisa por país'
announcements.configureFields.placeholder_search_division: 'Pesquisa por divisão'
announcements.configureFields.result_found: 'Resultados encontrados'
announcements.configureFields.clear_result: 'Resultados claros'
announcements.configureFields.error_already_called_user: 'Erro: A pessoa já foi convocada!'
announcements.configureFields.error_already_called_user_date: 'Error: ¡La persona ya ha sido llamada en un intervalo de fechas similar!'
announcements.configureFields.notified: Notificado
announcements.configureFields.content_course: 'Conteúdo do curso'
announcements.configureFields.report: Relatório
announcements.configureFields.title_report: 'Relatório dos alunos'
announcements.configureFields.direction: Endereço
announcements.configureFields.telephone: Telefone
announcements.configureFields.nif: NIF
announcements.configureFields.tutor: Tutor
announcements.configureFields.apt: Apt
announcements.configureFields.time_total: 'Tempo total'
question.label_in_singular: Questão
question.label_in_plural: Perguntas
question.configureFields.question: Questão
question.configureFields.random: Aleatório
question.configureFields.answers: Respostas
question.configureFields.image_file: 'Imagem de ficheiro'
question.configureFields.question_for: 'Perguntas para'
question.configureFields.image_for: 'Imagem para'
question.configureFields.add_image_puzzle: 'Adicionar imagem do puzzle'
question.configureFields.add_question: 'Criar pergunta'
question.configureFields.see_image: 'Ver imagem'
content.label_in_singular: Conteúdo
content.label_in_plural: Conteúdo
content.configureFields.title: Título
content.configureFields.content: Conteúdo
content.configureFields.position: Posição
content.configureFields.add_content: 'Adicionar conteúdo'
content.configureFields.content_for: 'Conteúdo para'
question_nps.label_in_singular: 'Pergunta NPS'
question_nps.label_in_plural: 'Perguntas NPS'
question_nps.configureFields.type: Tipo
question_nps.configureFields.position: Posição
question_nps.configureFields.question: Questão
question_nps.configureFields.course: Curso
question_nps.configureFields.name_question: 'Nome pergunta'
question_nps.configureFields.translations: Traduções
opinions.label_in_singular: Opinião
opinions.label_in_plural: Opiniões
opinions.configureFields.course: Curso
opinions.configureFields.question: Pregunta
opinions.configureFields.to_post: Publicar
opinions.configureFields.value: Valor
opinions.configureFields.valoration: Avaliação
help_category.label_in_singular: 'Categoria ajuda'
help_category.label_in_plural: 'Categorias ajuda'
help_category.configureFields.category_name: 'Nome da categoria'
help_category.configureFields.translations: Traduções
help_text_content.label_in_singular: 'Ajuda de conteúdo'
help_text_content.label_in_plural: 'Ajuda ao conteúdo'
help_text_content.configureFields.category: Categoria
help_text_content.configureFields.title: Título
help_text_content.configureFields.text: Texto
help_text_content.configureFields.translations: Traduções
user.label_in_singular: Utilizador
user.label_in_plural: Utilizadores
user.configureFields.division: Divisão
user.configureFields.country: País
user.configureFields.category: Categoria
user.configureFields.departament: Departamento
user.configureFields.center: Centro
user.configureFields.gender: Género
user.configureFields.first_name: Nome
user.configureFields.last_name: Apelido
user.configureFields.code: Código
user.configureFields.password: Palavra-passe
user.configureFields.change_password: 'Alterar a palavra-passe'
user.configureFields.courses: Cursos
user.configureFields.extra: Extra
user.configureFields.announcements: Chamadas
user.configureFields.extra_fields: 'Campos adicionais'
user.configureFields.avatar_image: 'Imagem de perfil'
user.configureFields.new_password: 'Nova palavra-passe'
user.configureFields.birthdate: 'Data de nascimento'
user.configureFields.edit_user: 'Editar utilizador'
user.configureFields.user_data: 'Dados do utilizador'
user.configureFields.stats: Estatísticas
user.configureFields.chapter: Capítulos
user.configureFields.ratio_course: 'Rácio Cursos/Pessoas'
user.configureFields.avg_stars: 'Número médio de estrelas'
user.configureFields.time: Tempo
user.configureFields.chapter_time: 'Tempo gasto por capítulo'
user.configureFields.available: Disponível
user.configureFields.messages: Mensagens
user.configureFields.login_history: 'Histórico de início de sessão'
user.configureFields.started_at: Início
user.configureFields.finished_at: Fim
user.configureFields.time_spent: 'Tempo despendido'
user.configureFields.content_viewed: 'Conteúdo visualizado'
user.configureFields.interaction_with_teacher: 'Interacções com o professor'
user.configureFields.course_content: 'Conteúdo do curso'
user.configureFields.content_type: 'Tipo de conteúdo'
user.configureFields.finished: Concluído
user.configureFields.teacher_interaction: 'Interacções com os professores'
user.configureFields.date: Data
user.configureFields.sender: Remetente
user.configureFields.recipient: Destinatário
user.configureFields.subject: Assunto
user.configureFields.questions: Perguntas
user.configureFields.chapter_type: 'Tipo de capítulos'
user.configureFields.finished_chapter_types: 'Tipos de capítulos concluídos'
user.configureFields.button_validate: Validar
user.configureFields.open: 'Campus aberto'
user.configureFields.computer: Computador
user.configureFields.mobile: Celular
user.configureFields.tablet: Tablet
user.manage.assign_data: 'Atribuir dados'
user.gender.m: Masculino
user.gender.f: Feminino
user.configureFields.time_title: 'Dedicação por dia'
user.configureFields.interaction_in_forum: 'Interacções no fórum'
user.configureFields.email: 'Endereço eletrónico'
user.configureFields.fullname: 'Nome e apelido'
user.filtersRequired: 'Selecionar pelo menos um filtro'
stats.general_stats: 'Estatísticas gerais'
stats.total_times_spent: 'Tempo total despendido'
stats.users_activity: 'Atividade do utilizador'
stats.users_active_last_30: 'Activos nos últimos 30 dias'
stats.users_inactive_last_30: 'Nenhuma atividade nos últimos 30 dias'
stats.users_never_login: 'Nunca entraram'
stats.daily_chapter: 'Capítulos diários concluídos'
stats.finished_chapters: 'Capítulos concluídos'
stats.daily_course: 'Cursos diários concluídos'
stats.finished_courses: 'Cursos concluídos'
stats.daily_login: 'Inicio de sesión diario'
stats.daily_login_tooltip: 'Iniciar sessão'
stats.all_courses: 'Todos os cursos'
stats.all_countries: 'Todos os países'
stats.all_centers: 'Todos os centros'
stats.all_categories: 'Todas as categorias'
stats.all_departament: 'Todos os departamentos'
stats.all_gender: 'Todos os géneros'
stats.all_divisions: 'Todas as divisões'
stats.filters: Filtros
stats.filter_by: 'Filtrar por'
stats.modal_close: Fechar
stats.clear_filters: 'Limpar os filtros'
stats.apply_filters: 'Aplicar filtros'
stats.export_title: 'Exportação de dados'
stats.export.start_date: 'Data de início'
stats.export.end_date: 'Data final'
stats.export.filename: 'Nome do ficheiro'
stats.export.request_date: 'Data do pedido'
stats.export.available_until: 'Disponível até'
stats.export.loading_data: 'Carregamento de dados'
stats.export.no_data: 'Não existem dados disponíveis'
stats.export.download_file: 'Descarregar ficheiro'
stats.export.abort_export_request: 'Cancelar pedido de exportação'
stats.export.view_details: 'Ver detalhes'
stats.export.reset_form: 'Redefinir campos'
stats.export.error_start_date: 'A data de início não pode ser posterior à data de fim.'
stats.export.export_error: 'Ocorreu um erro ao gerar o relatório'
stats.export.export_success: 'O relatório foi adicionado com êxito à fila de descarregamento.'
stats.export.export_dir: 'Estatísticas / ferramenta Excel'
stats.devices_login: 'Iniciar sessão em dispositivos'
stats.distribution_ages: 'Distribuição etária'
stats.generation_babyboom: BabyBoom
stats.generation_x: 'Geração X'
stats.generacion_milenials: Millennials
stats.generacion_z: 'Geração Z'
stats.title_information_user: 'Informações sobre as pessoas'
stats.title_information_content: 'Informações sobre o conteúdo'
stats.title_information_courses: 'Informações sobre o curso'
stats.title_information_chapter: 'Informações sobre o capítulo'
stats.distribution_country: 'Distribuição por país'
stats.title_finish_m: terminado
stats.title_made: efectuada
stats.title_made_f: efectuada
stats.chapter_day: dias
stats.chaper_hours: horas
stats.chapter_minutes: minutos
stats.chapter_total: TOTAL
stats.chapter_media: MEDIA
stats.content_active: activos
stats.content_active_f: activas
stats.totalLogin: Totais
stats.access: Acesso
stats.uniqueLogin: Único
stats.at_least_one_course_finished: 'Pessoas formadas'
stats.top_rated_courses: 'Cursos com melhor classificação'
stats.lowest_rated_courses: 'Cursos com a classificação mais baixa'
stats.most_completed_courses: 'Cursos mais concluídos'
stats.users_more_actives: 'Pessoas mais activas'
stats.users_less_actives: 'Pessoas menos activas'
stats.accumulative.title: 'Evolutiva e cumulativa'
stats.accumulative.trained: 'Pessoas únicas formadas'
stats.accumulative.new: Novo
stats.accumulative.accumulated: Acumulado
stats.accumulative.chart: Gráfico
stats.accumulative.logins: 'Iniciar sessão'
stats.accumulative.courses: Cursos
stats.accumulative.courses_started: 'Cursos iniciados'
stats.accumulative.courses_finished: 'Cursos concluídos'
stats.accumulative.ratings: Classificações
stats.accumulative.time: 'Tempo investido (em horas)'
stats.accumulative.filters: 'Distribuição dos filtros'
'stats. daily_posts': 'Mensagens diárias no fórum'
stats.most_active_threads: 'Tópicos mais activos'
stats.most_active_users: 'Pessoas mais activas'
stats.forum_post_messages_count: Mensagens
stats.forum_post_title: Título
task.status.pending: Pendente
task.status.in_progress: 'Em curso'
task.status.success: Concluído
task.status.failure: Erro
security.login_button_login: Entrar
security.login_button_create_account: 'Criar uma conta'
security.login_title: 'Introduza os seus dados'
security.login_remember_me: 'Recordar os meus dados'
security.login_question_password: 'Esqueceu-se da sua palavra-passe?'
security.button_register: Registo
security.button_exist_accoutn: 'Já criei uma conta'
security.button_account: 'Aceitar as condições'
security.first_name: Nome
security.last_name: Apelido
security.password: Palavra-passe
security.repeat_password: 'Repetir a palavra-passe'
security.register: Registo
security.remembered_the_password: 'Esqueceu-se da sua palavra-passe? Iniciar sessão'
security.button_send_email: 'Enviar correio eletrónico'
security.reset_your_password: 'Repor a sua palavra-passe'
security.text_reset_password: 'Introduza o seu endereço de correio eletrónico e enviar-lhe-emos uma ligação para redefinir a sua palavra-passe.'
course_level.label_in_singular: Nível
course_level.label_in_plural: Níveis
component_video.add_package_video: 'Adicionar vídeo'
component_video.edit_package_video: 'Editar vídeo'
component_video.type: Tipo
component_video.url_video: 'URL do vídeo'
component_video.file_subtitle: 'Ficheiro de legendas'
component_video.button_save: Guardar
component_video.text_content_subtitle_video: 'Este vídeo já tem uma legenda carregada. Se adicionar uma nova, esta será substituída.'
component_video.upload_file_video: 'Selecionar ficheiro de vídeo'
component_video.preparing_file: 'Aguardar, preparar ficheiro'
component_video.package_video: 'Pacote de vídeo'
component_video.optimizing_video: 'O vídeo está a ser otimizado e estará disponível em breve'
component_video.text_good: Bem
filter_category.label_in_singular: 'Categoria de filtro'
filter_category.label_in_plural: 'Categorias de filtros'
filter_category.configureFields.name: Nome
filter.label_in_singular: Filtro
filter.label_in_plural: Filtros
filter.configureFields.name: Nome
filter.configureFields.action_add: 'Adicionar filtro'
filter.extras.no_filters: 'Nenhum filtro atribuído'
filter.extras.loadings: Carregando...
filter.extras.no_filter_selected: 'Nenhum filtro selecionado'
filter.extras.no_filter_assigned: 'Não há filtros a atribuir'
news.form.title: Título
news.form.text: Texto
help.pdf.general: ADMIN_ES
help.video.general: '549279910'
segment_category.label_in_singular: 'Segmento da categoria'
segment_category.label_in_plural: 'Segmento de categorias'
segment_category.configureFields.name: Nome
course_segmente.label_in_singular: Segmento
course_segmente.label_in_plural: Segmentos
course_segmente.configureFields.name: Nome
course_segmente.configureFields.action_add: 'Adicionar segmento'
documentation.label: Documentação
documentation.title: Título
documentation.description: Descrição
documentation.type: Tipo
documentation.file: Arquivo
documentation.locale: Língua
pdf.downloadable: Descarregável
itinerary.label_in_singular: Itinerário
itinerary.label_in_plural: Itinerários
itinerary.name: Nome
itinerary.description: Descrição
itinerary.tab.courses: Cursos
itinerary.tab.users: Pessoas
itinerary.no_courses: 'Não foram acrescentados cursos ao percurso'
itinerary.no_users: 'Não foram acrescentadas pessoas ao itinerário'
itinerary.saving_courses: 'Guardar cursos'
itinerary.find_available_courses: 'Pesquisar cursos disponíveis'
itinerary.find_selected_courses: 'Pesquisar cursos seleccionados'
itinerary.course.position_updated: 'Posição do curso actualizada'
itinerary.course.update_warning: 'Os cursos desta via serão actualizados'
itinerary.user.add_success: 'A pessoa do utilizador foi criada com sucesso'
itinerary.user.remove_success: 'O utilizador foi eliminado com sucesso'
itinerary.user.confirm_delete: 'Cuidado! Esta pessoa perderá o acesso ao itinerário'
itinerary.user.confirm_delete_all: 'Cuidado! As pessoas ligadas a esta via perderão o acesso'
itinerary.manager.add_success: 'Gestor adicionado com sucesso'
itinerary.manager.remove_success: 'Gestor removido com sucesso'
itinerary.manager.edit_manager: 'Editar gestores'
itinerary.manager.find_managers: 'Pesquisa de gestores'
itinerary.manager.confirm_delete: 'O gestor perderá o acesso ao itinerário.'
itinerary.manager.confirm_delete_all: 'Os gestores perderão o acesso ao itinerário'
itinerary.filter.added: 'Filtro adicionado ao itinerário corretamente'
itinerary.filter.removed: 'Filtro removido do itinerário corretamente'
itinerary.total_courses: 'Total de cursos'
common_areas.cancel: Cancelar
common_areas.add_all: 'Adicionar tudo'
common_areas.remove_all: 'Eliminar tudo'
user_filter.modify_users: 'Modificar pessoas'
user_filter.find_by: 'Pesquisar por'
common_areas.total: Total
common_areas.confirm_delete: "<p style=\"font-size: 14px;\"><span>Pretende mesmo eliminar este elemento?</span></p>\n<p style=\"font-size: 14px;\"><span>Esta ação não pode ser anulada.</span></p>"
common_areas.confirm_save: 'Quer mesmo guardar?'
challenges: Duelos
challenges.random: Aleatório
challenges.question: Questão
challenges.correct: Correto
challenges.answer1: 'Resposta 1'
challenges.answer2: 'Resposta 2'
challenges.answer3: 'Resposta 3'
challenges.answer4: 'Resposta 4'
challenges.answer5: 'Resposta 5'
challenges.answer6: 'Resposta 6'
material_course.configureFields.type: 'Tipo de ficheiro'
material_course.configureFields.save: 'O material foi armazenado corretamente'
material_course.configureFields.type_1: PDF
material_course.configureFields.type_2: Vídeo
material_course.configureFields.type_3: Zip/Rar
material_course.configureFields.type_4: Imagem
material_course.configureFields.type_5: 'Pacotes Office'
material_course.configureFields.type_6: 'Blocos de notas'
material_course.configureFields.file: Arquivo
material_course.configureFields.no_material: 'Não foram adicionados materiais'
material_course.configureFields.question_delete: 'Quer mesmo apagar este material?'
material_course.configureFields.question_decition: 'Esta ação não pode ser anulada posteriormente'
material_course.configureFields.delete: 'Remover material'
material_course.placeholder.file: 'Selecionar ficheiro'
material_course.download: Descarregar
taskCourse.configureFields.noFile: 'Sem ficheiros '
taskCourse.configureFields.question_delete: 'Deseja mesmo apagar este ficheiro?'
taskCourse.labelInSingular: Tarefa
taskCourse.labelInPlural: Tarefa
taskCourse.configureFields.dateDelivery: 'Data de entrega'
taskCourse.configureFields.startDate: 'Data de início'
taskCourse.configureFields.visible: Visível
taskCourse.configureFields.senTask: 'A tarefa foi enviada'
taskCourse.configureFields.senTaskUser: 'Enviado para'
taskCourse.configureFields.addFile: 'Adicionar ficheiro'
taskCourse.configureFields.state_0: Pendente
taskCourse.configureFields.state_1: Entregue
taskCourse.configureFields.state_2: 'Em análise'
taskCourse.configureFields.state_3: Rejeitado
taskCourse.configureFields.state_4: Aprovado
taskCourse.configureFields.files_attachment: Anexos
taskCourse.configureFields.sendComment: 'O comentário foi enviado'
taskCourse.configureFields.stateTask: 'A tarefa mudou de estado'
taskCourse.configureFields.history: História
component_game.true_or_false: 'Verdadeiro ou falso'
component_game.adivina_imagen: Adivinhação
component_game.ordenar_menorMayor: 'Do mais alto ao mais baixo'
component_game.parejas: Casais
component_game.rouletteWheel: 'Roda de letras'
component_game.categorized: 'Onde é que ele se encaixa?'
component_game.fillgaps: 'Preenchimento de lacunas'
component_game.guessword: 'Cartas de encomenda'
component_game.wordle: 'Palavra secreta'
component_game.lettersoup: 'Sopa de letrinhas'
component_game.videoquiz: 'Quiz de vídeo'
games.letterwheel: 'Roda de letras'
games.opciones: 'Selecionar uma opção'
games.categorize: 'Onde é que ele se encaixa?'
games.optiones_empty: 'É necessário adicionar pelo menos duas opções para configurar corretamente o desafio.'
games.validate_add_categorize: 'É necessário editar o campo de afirmação, selecionar uma resposta correta ou selecionar uma imagem.'
games.add_category: 'Adicionar uma opção'
games.add_categories: 'Adicionar grupo ou família'
games.add_word: 'Adicionar palavra'
games.words: Palavras
games.edit_option: 'Editar opção'
games.text_common.answer: Resposta
'games.text_common:correct': Correto
games.text_common.time: Tempo
games.text_common.word: Palavra
games.text_common.no_questions: 'Sem pergunta'
games.text_common.text_question: 'Texto da pergunta'
games.text_common.word_question: 'Palavra da pergunta'
games.text_common.message_guess_word_question: 'Deve escrever o texto da pergunta'
games.text_common.message_guess_word_word: 'Deve escrever a palavra da pergunta'
games.text_common.message_guess_word_time: 'É necessário definir o tempo para a pergunta'
games.text_common.message_guess_word_answer: 'A resposta deve conter apenas uma palavra'
games.text_common.select_image: 'Selecionar imagem'
games.text_common.ilustre_category: 'Imagem para ilustrar a categoria'
games.text_common.ilustre_question: 'Imagem para ilustrar a pergunta'
games.text_common.message_higher_lower: 'Para personalizar o conteúdo do jogo, cria as palavras que queres que apareçam e ordena-as como quiseres. Para o fazer, basta arrastar as palavras para alterar a sua ordem.'
games.validate_memory_match: 'Ainda falta acrescentar um título ou uma imagem'
games.help: Ajuda
games.validate_hidden_image: 'A pergunta, a solução e a imagem são obrigatórias'
games.fillgap.title: 'Como construir o jogo?'
games.fillgap.message: 'No campo "Adicionar frase", pode desenhar a estrutura do jogo e depois decidir que palavra do texto será um espaço. Quando adiciona uma lacuna, esta é apresentada a azul para que a possa identificar facilmente.'
games.fillgap.result_question: 'Resultado do jogo'
games.fillgap.word: 'Adicionar frase ou intervalo'
games.fillgap.add_filler: 'Adicionar uma frase'
games.fillgap.add_gap: 'Adicionar um intervalo'
games.fillgap.new_option: 'Nova opção'
games.fillgap.validate_save: 'É necessário acrescentar uma frase e, pelo menos, dois espaços'
games.videoquiz.message_validate_answer: 'Deve acrescentar um título, um mínimo de duas respostas e deve conter uma resposta correcta'
games.videoquiz.time_video: 'Tempo de vídeo'
games.videoquiz.savell_all_changes: 'Guardar todas as alterações'
games.videoquiz.validate_to_add_question: 'É necessário ter pelo menos uma pergunta para poder guardar as alterações'
games.videoquiz.validate_letter_soup: 'Parece que lhe faltam as palavras ou a frase'
chapter_type.1: Scorm
chapter_type.2: Conteúdo
chapter_type.3: Roleta
chapter_type.4: 'O dobro ou nada'
chapter_type.5: Quiz
chapter_type.6: Puzzle
chapter_type.7: 'Palavra secreta'
chapter_type.8: Pdf
chapter_type.9: Víideo
chapter_type.10: Slider
chapter_type.11: 'Rodas de letras'
chapter_type.12: 'Verdadeiro ou Falso'
chapter_type.13: Adivinhação
chapter_type.14: 'Do mais alto ao mais baixo'
chapter_type.15: Casais
chapter_type.16: 'Onde é que ele se encaixa?'
chapter_type.17: 'Preenche as lacunas'
chapter_type.18: 'Ordenar as letras'
chapter_type.19: Enigma
chapter_type.20: 'Sopa de letrinhas'
chapter_type.21: 'Quiz de vídeo'
menu.users.exit_impersonate: 'Sair da personificação'
menu.forum: Fórum
course.export: 'Cursos de exportação'
course.export.confirm: 'Pretende realmente exportar as informações de todas as disciplinas?'
announcements.configureFields.opinions: Opiniões
announcements.configureFields.no_messages: 'Sem mensagens'
announcements.configureFields.info_max_users: 'O número máximo de utilizadores que podem ser chamados para a chamada é:'
announcements.configureFields.annoucement_all: 'Convocar todos'
question_nps.configureFields.source: 'Candidatar-se a'
user.actions.impersonate: 'Fazer-se passar por'
user.show_cv: 'Ver CV'
user.delete_cv: 'Eliminar CV'
stats.export.download_file_pdf: 'Descarregar PDF'
stats.export.download_file_xlsx: 'Descarregar o Excel'
stats.segmented.title: 'Estatísticas segmentadas'
filter.removed_filter: 'O filtro %s foi removido com sucesso.'
filter.added_filter: 'O filtro %s foi adicionado com êxito.'
filter.all_removed: 'Os filtros foram removidos'
filter.all_added: 'Os filtros foram adicionados'
itinerary.chart.users: 'as pessoas completaram o itinerário'
itinerary.chart.users_process: 'em curso'
itinerary.chart.users_incomplete: 'sem arranque'
itinerary.chart.users_title: ' pessoas afectadas de'
itinerary.chart.total_time: 'Tempo total acumulado'
itinerary.chart.avg_time: 'Tempo médio por pessoa'
itinerary.chart.by_country: 'Itinerários por país'
itinerary.chart.by_hotel: 'Itinerários por centro'
itinerary.chart.by_department: 'Itinerários por departamento'
itinerary.chart.by_grouping: 'Itinerários por agrupamento'
itinerary.users_assign: 'Este itinerário foi atribuído a pessoas'
itinerary.users.progress: 'Progresso no itinerário'
itinerary.users.download_user: 'Descarregar o Excel'
itinerary.courses.selected: 'Cursos seleccionados'
itinerary.status.completed: Concluído
itinerary.status.started: 'Em curso'
itinerary.status.unstarted: 'Sem arranque'
segmented_stats.title1: 'Pessoas formadas'
segmented_stats.title2: Horas
segmented_stats.title3: Cursos
segmented_stats.title4: Acesso
segmented_stats.distribution_by_country: 'Distribuição por país'
segmented_stats.structure: Estrutura
segmented_stats.hotel: Hotel
segmented_stats.by_department: 'Por Departamento'
segmented_stats.by_school: 'Por Escola'
segmented_stats.total_hours: 'Total de horas'
segmented_stats.total_avg: 'Média de horas'
segmented_stats.structure_avg: 'Estrutura média'
segmented_stats.structure_total: 'Estrutura total'
segmented_stats.hotel_avg: 'Hotel médio'
segmented_stats.hotel_total: 'Hotel Total'
segmented_stats.avg: Média
segmented_stats.courses_started: 'Cursos iniciados'
segmented_stats.courses_finished: 'Cursos concluídos'
segmented_stats.total_courses_started: 'Total de cursos iniciados'
segmented_stats.total_courses_finished: 'Total de cursos concluídos'
segmented_stats.access_totals: 'Total de acessos'
segmented_stats.access_uniques: 'Início de sessão único'
segmented_stats.certificates: Diplomas
segmented_stats.total_certificates: 'Total de diplomas emitidos'
library.createdAtView: 'Adicionado por: {email} em {date} às {time}'
library.no_text_provided: 'Não foi introduzido qualquer texto'
library.maximum_allowed_size_exceeded: '%s: O número máximo de caracteres permitido foi ultrapassado.'
library.category.created: 'A categoria foi criada com sucesso'
library.category.updated: 'A categoria foi atualizada com sucesso'
library.category.deleted: 'A categoria foi eliminada com sucesso'
library.category.activated: 'A categoria foi ativada com sucesso'
library.category.deactivated: 'A categoria foi desativada com sucesso'
library.library.updated: 'A biblioteca foi atualizada com sucesso'
library.library.created: 'A biblioteca foi criada com sucesso'
library.library.deleted: 'A biblioteca foi removida com sucesso'
library.library.name_required: 'O nome é obrigatório e deve ter menos de 100 caracteres.'
library.library.type_required: 'O campo "tipo" é obrigatório'
library.library.link_required: 'Se o tipo for "LINK", deve ser especificado um URL válido.'
forum.configureFields.thread: Fio
forum.configureFields.message: Mensagem
forum.configureFields.comment: 'Relatório de comentários'
forum.configureFields.title_modal_add: 'Adicionar Fórum'
forum.configureFields.title_modal_edit: 'Editar Fórum'
course_press.label_in_singular: 'Curso no local'
course_press.label_in_plural: 'Cursos no local'
menu.courses_managment.course_sections: Secções
common.write: 'Escrever algo'
common_areas.accept: Aceitar
common_results: resultados
games.answers: 'Adicionar resposta'
games.text_common.order_ramdom: 'Ordenar aleatoriamente'
games.puzzle.description_cropper: 'Em seguida, selecione a área da imagem que será utilizada para criar o puzzle.'
games.validation_truefalse.question_or_image: 'É necessário escrever a pergunta ou selecionar uma imagem'
games.help.write_question: 'Escrever pergu'
games.help.write_word: 'Escrever palavra'
games.help.write_title: 'Escrever um título'
games.help.write_answer: 'Escrever uma resposta'
games.true: Verdadeiro
games.false: Falso
games.edit_video_quiz: 'Ver e editar quiz de vídeo'
games.delete_video_quiz: 'Eliminar quiz de vídeo'
game.feedback.title: 'Ativar o feedback'
game.feedback.title_positive: 'Em caso de sucesso'
game.feedback.title_negative: 'Em caso de falha (opcional)'
announcements.common.group: Grupo
announcements.common.action_denomination: 'Denominação Ação'
announcements.common.modality: Modalidade
announcements.common.place_of_instruction: 'Local de entrega'
announcements.common.collaboration_type: 'Tipo de colaboração'
announcements.common.provider: Fornecedor
announcements.common.provider_cif: 'CIF Fornecedor'
announcements.observations.costs: Custos
announcements.observations.course_status: 'Estado do curso'
announcements.observations.comunicado_fundae: 'Comunicado da FUNDAE'
announcements.observations.comunicado_abilitia: 'Comunicação a ABILITIA'
announcements.observations.economic_module: 'Módulo económico'
announcements.observations.travel_and_maintenance: 'Deslocação e manutenção'
announcements.observations.provider_cost: 'Fornecedor de custos'
announcements.observations.hedima_management_cost: 'Custo de gestão da HEDIMA (10%)'
announcements.observations.travel_and_maintenance_cost: 'Despesas de deslocação e de estadia'
announcements.observations.total_cost: 'Custo total'
announcements.observations.final_pax: 'PAX Final'
announcements.observations.maximum_bonus: 'Bónus máximo (PAX final)'
announcements.observations.subsidized_amount: 'Montante subvencionado'
announcements.observations.private_amount: 'Montante privado'
announcements.observations.provider_invoice_number: 'Nº da fatura Fornecedor'
announcements.observations.hedima_management_invoice_number: 'Fatura nº HEDIMA Management'
announcements.observations.invoice_status: 'Estado da Fatura'
announcements.observations.observations: Observações
announcements.observations.observation: Observação
announcements.course.no_chapter: 'Este curso não tem capítulos, porque é um curso presencial'
announcements.formativeActionTypes.intern: Interno
announcements.formativeActionTypes.extern: Externo
announcements.formativeActionTypes.session_congress: Externo
common_areas.confirm_file_upload: 'Tem a certeza de que pretende carregar o(s) documento(s)?'
common_areas.confirm_file_delete: 'Tem a certeza de que pretende apagar?'
course_section.label_in_singular: Secção
course_section.label_in_plural: Secções
course_section.configureFields.name: Nome
course_section.configureFields.description: Descrição
course_section.configureFields.active: Ativa
course_section.configureFields.sort: Ordem
course_section.configureFields.translations: Traduções
course_section.configureFields.section_name: 'Nome da secção'
course_section.configureFields.categories: Categorias
user.roles.administrator: Administrador
user.roles.user: Utilizador
user.roles.tutor: Tutor
user.roles.subsidizer: Inspetor
user.roles.manager: Gestor
user.roles.manager_editor: 'Gestor - Editor'
user.roles.team_manager: 'Gestor de equipa'
survey.label_in_plural: Inquéritos
course.configureFields.is_main: 'Este curso utilizará apenas as suas próprias perguntas para avaliação.'
global.error: 'Ocorreu um erro. Por favor, tente novamente mais tarde'
quiz.configureFields.title_creation: 'Criação de perguntas'
quiz.configureFields.question: 'Texto da pergunta'
quiz.configureFields.question_placeholder: 'Escrever o enunciado da pergunta'
quiz.configureFields.question_delete: 'Quer mesmo eliminar esta pergunta?'
rouletteWord.configureFields.statement: Declaração
rouletteWord.configureFields.answer: Resposta
rouletteWord.configureFields.type_0: 'Começar com a letra'
rouletteWord.configureFields.type_1: 'Contém a carta'
rouletteWord.configureFields.error.statement.max: 'A declaração não pode exceder ${max} caracteres.'
rouletteWord.configureFields.error.statement.empty: 'A declaração não pode estar vazia'
rouletteWord.configureFields.error.answer.max: 'A resposta não pode exceder {max} caracteres.'
rouletteWord.configureFields.error.answer.empty: 'A resposta não pode ser vazia'
rouletteWord.configureFields.error.answer.starts: 'A resposta deve começar com a letra'
rouletteWord.configureFields.error.answer.includes: 'A resposta deve conter a letra'
rouletteWord.response.update_letter: 'Os dados foram actualizados corretamente'
rouletteWord.response.delete_letter: 'Os dados foram eliminados com êxito'
trueorFalse.configureFields.true: Correto
trueorFalse.configureFields.false: Falso
enigma.configureFields.title_creation: 'Criação do enigma'
puzzle.configureFields.save_image: 'Imagem guardada com sucesso'
puzzle.configureFields.select_correct_answer: 'Deve selecionar uma resposta correta'
puzzle.configureFields.recomendation: Recomendação
puzzle.configureFields.recomendation_dimentions: '<p>Recomendamos uma dimensão <span class="text-primary"><b>mínima de 1024 píxeis por lado </b></span> e <span class="text-primary"><b> uma dimensão máxima de 2000 píxeis por lado.</b></span></p>'
puzzle.configureFields.recomendation_description: '<p> O formato do puzzle é <span class="text-primary"><b>quadrado</b></span>, por isso, selecionarmos-se uma imagem com uma relação de aspeto diferente, por exemplo, uma imagem de paisagem, a ferramenta permitir-nos-á cortar selecionando a área pretendida.</p>'
hiddenword.configureFields.title: 'Criação de palavra secreta'
hiddenword.configureFields.answers_title: 'Palavra secreta'
hiddenword.configureFields.answers_placeholder: 'Escrever a palavra secreta'
categorize.configureFields.title_group: 'Grupos ou famílias'
fillgaps.configureFields.title: 'Criação de frases'
fillgaps.configureFields.fillgap: Oco
fillgaps.configureFields.fillgaps: Ocos
fillgaps.configureFields.type_list: Lista
fillgaps.configureFields.type_drag: Arrastar
guesword.configureFields.word_title: 'Palavra não ordenada'
guesword.configureFields.word_title_placeholder: 'Escrever uma palavra que apareça sem ordem'
guesword.configureFields.solution: Solução
guesword.configureFields.solution_placeholder: 'Escreve a solução do jogo'
guesword.configureFields.help_placeholder: 'Escrever ajuda para o jogo'
pairs.configureFields.title: 'Criação do jogo'
pairs.configureFields.placeholder_title: 'Escrever a declaração'
pairs.configureFields.create_game: 'Criação de jogos'
chapter_type.description.22: '<p>A solução ideal para criar conteúdos dinâmicos e atrativos nos seus cursos ou pílulas de formação, apresentando a informação de uma forma visual e com uma grande variedade de interações baseadas em texto, imagens, vídeos, áudio, ligações multimédia, cartões interativos, cenas ligadas, etc.</p>'
chapter_type.add.22: 'Criar VCMS'
games.videoquiz_exist_question: 'Já existe uma pergunta sobre esta altura'
chapter_type.22: VCMS
video.configureFields.title: 'Criação de questionários em vídeo'
video.configureFields.add_question: 'Adicionar pergunta'
Next: Próximo
hours: Horas
minutes: Minutos
seconds: Segundos
field_required: 'Campo obrigatório'
field_invalid: 'Campo inválido'
field_invalid_format: 'Formato inválido'
remaining_characters: 'Caracteres restantes'
minimiun_characters: 'Número mínimo de caracteres'
menu.home.title: 'Ir para o Campus'
course.season.type.sequential: Sequencial
course.season.type.free: Grátis
course.season.type.exam: Exame
games.fillgap.add_fillgap: 'Adicionar espaços clicando em algumas palavras'
course_section.configureFields.hideCategoryName: 'Ocultar o nome da categoria'
user.roles.super_administrator: SuperAdministrador
settings.menu.label: Configuração
settings.header.title: Configuração
setting.menu.general: Geral
setting.menu.catalog: Catálogos
share: Partilhar
report.announcement.participants: Participantes
report.announcement.groupCode: 'Código do grupo'
report.announcement.enterpriseProfile: 'Perfil da empresa'
report.announcement.file: Ficheiro
report.announcement.totalStudents: 'Total de alunos'
report.announcement.enterpriseCIF: 'Número de IVA da empresa'
report.announcement.advisor: Tutor
course.stats.started: Iniciado
course.stats.ended: Concluído
course.stats.total_time: 'Tempo total'
course.stats.avg_time: 'Tempo médio'
course.stats.minutes: minutos
course.stats.minute: minuto
course.stats.hours: horas
course.stats.hour: tempo
course.stats.second: segundo
course.stats.seconds: segundos
question.configureFields.quantity_max_question: 'Número máximo de perguntas a apresentar:'
user.configureFields.available_courses: 'Dostępne kursy'
user.configureFields.available_chapter: 'Capítulo disponível'
user.configureFields.courses_stats.finished: concluído
user.configureFields.courses_stats.started: iniciado
user.configureFields.courses_stats.available: disponível
user.configureFields.courses_stats.sent_messages: 'enviado para'
user.configureFields.courses_stats.received_messages: recebido
user.configureFields.courses_stats.others: Outros
user.configureFields.permissions: Autorizações
course.configureFields.segments: Segmentos
stats.roles: Funções
course.configureFields.language: Língua
game.feedback.wrong: 'Exemplo: Uau!'
chapter_type.description.23: '<p>O roleplay é uma atividade em que os participantes assumem e agem como personagens fictícias, muitas vezes dentro de um cenário ou contexto específico. Durante o roleplay, os participantes adoptam temporariamente a personalidade, as características e os comportamentos das personagens que representam, interagindo uns com os outros de acordo com as circunstâncias e o ambiente imaginário estabelecido. Esta prática é utilizada em diversos contextos, como jogos, terapia, simulações educativas e actividades recreativas, com o objetivo de fomentar a criatividade, a empatia, a resolução de problemas e a exploração de situações hipotéticas.</p>'
password.uppercase: 'Obrigatório 1 ou mais caracteres em maiúsculas'
password.number: '1 ou mais dígitos numéricos necessários'
password.minimum: 'A palavra-passe deve ter um mínimo de %s caracteres.'
password.disable_3_consecutive_chars: 'Não é permitido repetir um carácter mais de 3 vezes seguidas.'
password.lowercase: '1 ou mais caracteres minúsculos necessários'
chapter_type.23: 'Jogo de papéis'
chapter_type.add.23: 'Criar jogo de papéis'
password.special_characters: '1 ou mais caracteres especiais necessários'
roleplay.status.failure: Falhou
roleplay.status.success: Passou
user.configureFields.locale: Língua
course.created: 'O curso foi guardado com sucesso'
question.configureFields.do_all_questions: 'Use todas as perguntas'
announcements.news.start_announcement: 'O curso %course% está mesmo a chegar!'
announcements.news.finish_announcement: 'O curso %course% está quase a terminar!'
user.configureFields.dni: DNI
course_section.configureFields.section_aditional: 'Formação complementar'
report.announcement.time_conexion: 'Tempo de ligação'
report.announcement.init_finish: 'Início e fim'
report.annnouncement.conexions: Ligações
report.annnouncement.chat_tutor: 'Conversar com o tutor'
report.annnouncement.first_conexion: 'Primeira ligação'
report.annnouncement.last_conexion: 'Última ligação'
generic_token.assistance.success: 'A sua presença foi registada com sucesso'
generic_token.assistance.user_not_in_group: 'O utilizador não pertence ao grupo de sessão'
chat.notification.number_of_messages: 'Tem %s de mensagem(ns) por ler'
certificate.notification.available: 'Diploma da convocatória do curso %s disponível para descarregamento'
user_fields_fundae.title: 'Campos adicionais FUNDAE'
user_fields_fundae.social_security_number: 'Número de segurança social'
user_fields_fundae.gender: Género
user_fields_fundae.email_work: 'Correio de trabalho'
user_fields_fundae.birthdate: 'Data de nascimento'
user_fields_fundae.dni: DNI
user_fields_fundae.contribution_account: 'Conta de contribuições'
user_fields_fundae.incapacity: Incapacidade
user_fields_fundae.victim_of_terrorism: 'Vítima de terrorismo'
user_fields_fundae.gender_violence: 'Vítima de violência baseada no género'
fundae_assistance_template.main_title: 'CONTROLO DA ASSIDUIDADE NA FORMAÇÃO'
fundae_assistance_template.action_type: 'NOME DA ACÇÃO DE FORMAÇÃO'
fundae_assistance_template.action_code: 'CÓDIGO DE ACÇÃO'
fundae_assistance_template.group: GRUPO
fundae_assistance_template.start_at: 'DATA DE INÍCIO'
fundae_assistance_template.finish_at: 'DATA FIM'
fundae_assistance_template.main_formation_teacher: 'FORMADOR/ GESTOR DE FORMAÇÃO'
fundae_assistance_template.session_number: 'SESSÃO NO.'
fundae_assistance_template.date: DATA
fundae_assistance_template.morning_afternoon: 'AMANHÃ/ TARDE'
fundae_assistance_template.signed: Assinado
fundae_assistance_template.info_signed_person: 'Formador/representante de formação'
fundae_assistance_template.assistance_data: 'Dados dos participantes'
fundae_assistance_template.signatures: ASSINATURAS
fundae_assistance_template.observations: OBSERVAÇÕES
fundae_catalogs.main_page.title: 'Catálogos FUNDAE'
fundae_catalogs.user_company.label_in_plural: Empresas
fundae_catalogs.user_company.label_in_singular: Empresa
fundae_catalogs.user_professional_category.label_in_plural: 'Categorias profissionais'
fundae_catalogs.user_professional_category.label_in_singular: 'Categoria profissional'
fundae_catalogs.user_study_level.label_in_plural: 'Níveis de estudo'
fundae_catalogs.user_study_level.label_in_singular: 'Níveis de estudos'
fundae_catalogs.user_work_center.label_in_plural: 'Locais de trabalho'
fundae_catalogs.user_work_center.label_in_singular: 'Local de trabalho'
fundae_catalogs.user_work_department.label_in_plural: 'Departamentos de trabalho'
fundae_catalogs.user_work_department.label_in_singular: 'Ministério do Trabalho'
fundae_catalogs.fields.state.title: Estado
fundae_catalogs.fields.state.active: Ativo
fundae_catalogs.fields.state.inactive: Inativo
excel.userAnnouncement.sheet1.title: 'Informações gerais'
excel.userAnnouncement.sheet1.colum1: 'Número de itinerários'
excel.userAnnouncement.sheet1.colum2: 'Número de utilizadores que têm itinerários'
excel.userAnnouncement.sheet1.colum3: 'Número de utilizadores que têm itinerários'
excel.userAnnouncement.sheet2.title: 'Itinerários do catálogo'
excel.userAnnouncement.sheet2.colum1: Id
excel.userAnnouncement.sheet2.colum2: 'Nome Itinerários'
excel.userAnnouncement.sheet2.colum3: Divisão
excel.userAnnouncement.sheet2.colum4: Categoria
excel.userAnnouncement.sheet2.colum5: 'Cursos atribuídos'
excel.userAnnouncement.sheet2.colum6: 'Pessoas afectadas'
excel.userAnnouncement.sheet2.colum7: 'As pessoas completaram o itinerário'
excel.userAnnouncement.sheet2.colum8: 'Pessoas em processo'
excel.userAnnouncement.sheet2.colum9: 'Não iniciados'
excel.userAnnouncement.sheet2.colum10: 'TEMPO TOTAL ACUMULADO'
excel.userAnnouncement.sheet2.colum11: 'TEMPO MÉDIO POR PESSOA'
excel.userAnnouncement.sheet3.title: 'Cursos de itinerário'
excel.userAnnouncement.sheet3.colum1: Id
excel.userAnnouncement.sheet3.colum2: 'Nome Itinerários'
excel.userAnnouncement.sheet3.colum3: 'Nome Curso'
excel.userAnnouncement.sheet3.colum4: Concluído
excel.userAnnouncement.sheet3.colum5: 'Em curso'
excel.userAnnouncement.sheet3.colum6: 'Sem arranque'
course.message_saved: 'Curso guardado'
chapter.configureFields.create_chapter: 'Criar capítulo'
user_filter.assign_manual: 'Atribuir manualmente'
user_filter.assign_filters: 'Atribuir por filtros'
user.configureFields.configureLocale: 'Definições de idioma'
user.configureFields.configureLocaleAdmin: 'Configurar o idioma do painel de administração'
user.configureFields.configureLocaleCampus: 'Definir a língua do campus'
stats.export.configsheet.title: Configuração
stats.export.configsheet.content_title: 'Relatório de estatísticas gerais'
stats.export.configsheet.content_period: 'Período coberto (data final)'
stats.export.configsheet.content_filters: 'Filtros activos'
stats.export.configsheet.content_period_from: De
stats.export.configsheet.content_period_to: Para
stats.export.datasheet.title: Dados
stats.export.filter.category: Categoria
stats.export.filter.departament: Departamento
stats.export.filter.gender: Género
stats.export.filter.activeUsers: Utilizadores
stats.export.filter.activeUsers_val_yes: Activos
stats.export.filter.activeUsers_val_no: Inativo
stats.export.filter.course_full_title: 'Curso a 100%'
stats.export.filter.course_full_val_yes: Sim
stats.export.filter.course_full_val_no: Não
stats.export.filter.course_full_descr: '(Incluir apenas os cursos que tenham sido concluídos no período indicado)'
stats.export.filter.course_intime_title: 'Vida do curso no período'
stats.export.filter.course_intime_val_yes: Sim
stats.export.filter.course_intime_val_no: Não
stats.export.filter.course_intime_descr: '(Incluir apenas os cursos iniciados e concluídos no período indicado)'
stats.export.filter.course_started_in_period_title: 'Curso iniciado no intervalo de datas'
stats.export.filter.course_started_in_period_val_yes: Sim
stats.export.filter.course_started_in_period_val_no: Não
stats.export.filter.course_finished_in_period_title: 'Curso concluído no intervalo de datas'
stats.export.filter.course_finished_in_period_val_yes: Sim
stats.export.filter.course_finished_in_period_val_no: Não
stats.export.filter.customFilters: Personalizado
stats.content_allusers: 'Todos os utilizadores'
stats.content_inactive: Inativo
stats.content_inactive_f: Inativo
itinerary.user.assign_manual: 'Atribuir manualmente'
itinerary.user.assign_filter: ' Atribuir por filtros'
itinerary.user.modify_users: 'Atribuir pessoas manualmente'
itinerary.user.filter_find_by: 'Pesquisar por'
common_areas.close: Fechar
itinerary.chart.avg_time_active: '(Total ativo)'
itinerary.chart.avg_time_all: '(Total atribuído)'
itinerary.courses.modify: 'Atribuir cursos'
itinerary.courses.appliedfilter: ' curso(s) apresentado(s) (filtrado(s) por'
itinerary.users.appliedfilter: ' pessoa/s apresentada/s (filtrada/s por'
itinerary.courses.available: 'Cursos disponíveis'
excel.userAnnouncement.sheet1.colum2b: 'Número de utilizadores únicos que têm itinerários'
excel.userAnnouncement.sheet1.colum3b: 'Número de cursos únicos nos percursos'
common_areas.select_choice: 'Selecionar uma opção'
chapter_type.24: LTI
chapter_type.description.24: LTI
lti_chapter.title: 'Capítulo LTI'
lti_chapter.add: 'Adicionar capítulo LTI'
lti_chapter.edit: 'Editar o capítulo LTI'
lti_chapter.identifier: 'Identificador LTI'
lti_chapter.identifier_required: 'Identificador LTI necessário'
chapter_type.add.24: 'Adicionar capítulo LTI'
categoryFilter.label: 'Filtros de categoria'
categoryFilter.title: 'Categorias de filtros'
user.configureFields.localeCampus: 'Língua do campus'
global.bulk.sheetValidation.error_tab_1: 'O primeiro separador não se chama "Lista de formações".'
global.bulk.sheetValidation.error_tab_2: 'O segundo separador não se chama "Participantes".'
stats.export.user_creation: 'Filtrar utilizadores por data de criação'
stats.export.users_export_title: 'Estatísticas dos utilizadores'
chapter_type.validation_course: "\nO capítulo não pode ser eliminado porque alguns utilizadores já registaram atividade no mesmo."
user.configureFields.courses_stats.notstarted: 'não iniciado'
course.configureFields.created_at: 'Data de criação'
course.configureFields.translate: Traduzir
messages.configureFields.timezone: 'Fuso horário'
user.email: E-mail
course.diploma.index: 'Índice de conteúdos'
menu.stats.reports.diplomas: 'Relatórios e Diplomas'
itinerary.succes.download: 'O relatório do itinerário está a ser processado e pode encontrá-lo em "Relatórios e diplomas"'
user.diploma.generate: 'Gerar diplomas'
filters.placeholder: 'Pesquisa de tipos'
filters.remove_all: 'Remover tudo'
filters.add_all: 'Adicionar tudo'
announcement.report_group_resume_individual: 'Resumo executivo individual'
announcement.report_downloaded_diploma: 'Diploma descarregado'
announcements.configureFields.code: 'Nome da chamada'
task.status.review: 'Em análise'
task.status.error: 'Erro do sistema'
email.error.subject: 'Erro em %context% (ID: %id%) - Ambiente: %appName% - Erro em %context% (ID: %id%) - Ambiente: %appName%'
email.error.subject_no_id: 'Erro em %context% - Ambiente: %appName%'
email.error.title: 'Erro na execução da tarefa'
email.error.environment: Ambiente
email.error.context: Contexto
email.error.task_id: 'ID da tarefa'
email.error.error_details: 'Detalhes do erro'
email.error.error_message: 'Mensagem de erro'
email.error.error_line: Linha
email.error.error_file: Arquivo
email.error.additional_info: 'Informações adicionais'
email.error.regards: 'Com os melhores cumprimentos'
email.error.team: 'A equipa %appName%'
email.zombie.subject: 'Tarefa no estado ZOMBIE em %context% (ID: %id%) - Ambiente: %appName% (ID: %id%) - Ambiente: %appName% (ID: %id%)'
email.zombie.title: 'Notificação de tarefa em estado ZOMBIE'
email.zombie.environment: Ambiente
email.zombie.context: Contexto
email.zombie.task_id: 'ID da tarefa'
email.zombie.marked_as: 'foi marcado como'
email.zombie.zombie_status: ZOMBIE
email.zombie.timeout_reason: 'porque excedeu o tempo de execução permitido'
email.zombie.check_details: 'Consulte o painel de administração ou a consola para obter mais informações e tomar as medidas adequadas'
email.zombie.additional_info: 'Informações adicionais'
email.zombie.regards: 'Com os melhores cumprimentos'
email.zombie.team: 'A equipa %appName%'
season.delete: 'Não é possível apagar esta temporada, atualmente tem capítulos ligados'
delete.season.chapters.users: "Não é possível eliminar a época %seasonName%,\n Os capítulos (%ChaptesTitles%) têm atividade do utilizador"
delete.season.danger: 'A época não pode ser eliminada'
stats.task.queued: 'Pedido de tarefa colado'
itinerary.delete.confirm.validation: 'Atualmente o itinerário está ativo, para prosseguir com a ação deve desativar o itinerário'
itinerary.delete.confirm.title: 'Deseja realmente excluir o itinerário?'
course.publish.message.active: 'Curso publicado'
course.publish.message.unactive: 'Curso marcado como não publicado'
itinerary.delete.error: 'Não é possível eliminar o itinerário'
courser.chaperts.orders.succes: 'Ordem de capítulo actualizada com sucesso'
course.publish.message.unactive.chapters: 'Não pode ser publicado porque o curso está incompleto'
course.undelete.message: 'A disciplina não pode ser eliminada porque tem conteúdos atribuídos'
user.roles.creator: Criador
