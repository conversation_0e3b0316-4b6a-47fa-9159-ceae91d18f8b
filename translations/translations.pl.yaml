challenges.challenge_title: '<PERSON><PERSON><PERSON> wy<PERSON>wan<PERSON>'
challenges.no_images: '<PERSON><PERSON> obrazu'
challenges.import_questions_to_excel: 'Pytania dotyczące importowania programu Excel'
challenges.import_from_excel: 'Import z programu Excel'
challenges.guest: <PERSON><PERSON><PERSON>
challenges.add_user: '<PERSON><PERSON><PERSON>'
challenges.performance: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
challenges.points: <PERSON><PERSON>
challenges.downloadtemplate: 'Pobierz szablon'
challenges.import: Import
observations.addfiles: 'Dodaj plik'
challenges.challenges: W<PERSON><PERSON>wan<PERSON>
questions.manageresponse: 'Zarządzanie odpowiedziami'
questions.see_answers: '<PERSON><PERSON><PERSON><PERSON> odpowiedzi'
clone_course.title: 'czy naprawdę chcesz powielić ten kurs?'
clone_course.content: 'Ta czynność spowoduje utworzenie kopii zawartości kursu.'
clone_course.clonarcourse: 'Kurs klonowania'
fundae_catalogs.user_professional_category.not_label_in_plural: 'Nie ma kategorii zawodowych'
menu.users_managment.nomanagers: '<PERSON><PERSON> przypisanych menedżerów'
message_api.forcingdisplaymenu: 'Wymuś wyświetlenie menu'
messages.notcontainchapter: 'Ten rozdział nie zawiera treści'
messages.nologinuser: 'Użytkownik niezalogowany'
messages.seesummary: 'zobacz podsumowanie'
messages.e_learning: E-learning
messages.editchallenges: 'Edytuj wyzwanie'
messages.challengeindex: 'Indeks wyzwań'
messages.createnew: 'Utwórz nowy'
messages.createnewchallenge: 'Utwórz nowe wyzwanie'
messages.infgroup: 'Informacje o grupie'
message_api.scorm.score_system_desc: 'Domyślnie akcja szkoleniowa zostanie uznana za zaliczoną, jeśli użytkownik "ukończy" lub "zaliczy" całą aktywność. Jeśli akcja szkoleniowa ma system punktacji do zaliczenia, należy aktywować następującą opcję:'
message_api.scorm.score_system_label: 'Minimalny wynik (między 0 a 100)'
message_api.scorm.allow_reset_desc: 'czy chcesz mieć możliwość ponownego zapoznania się z akcją szkoleniową po jej zakończeniu?'
message_api.scorm.allow_reset_label: 'Zezwalaj na reset'
message_api.scorm.config_label: 'Konfiguracja:'
message_api.scorm.config_desc: 'Działanie jest zakończone, gdy:'
