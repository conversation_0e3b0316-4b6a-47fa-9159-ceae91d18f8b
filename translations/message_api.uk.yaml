message_api.content.scorm_correctly: 'Скорм розпочався належним чином'
message_api.content.token_not_send: 'Токен не надіслано'
message_api.content.error_get_content: 'Виникла помилка при спробі запустити розділ:'
message_api.content.scorm_user_not_access: 'Користувач не має доступу до цього курсу'
message_api.content.scorm_not_content: 'Розділ не є пакетом контенту'
message_api.base.credentials_no_correct: 'Введені облікові дані є невірними.'
message_api.base.authorized_access: 'У доступі відмовлено'
message_api.base.user_successfully: 'Користувач успішно вийшов з системи'
message_api.base.error_login: 'Виникла помилка при спробі входу користувача - Помилка:'
message_api.base.authorized_token_fail: 'Ви не змогли отримати доступ через SSO. Якщо ви ще не зареєструвалися та не активували свого користувача на платформі, ви повинні це зробити, щоб увімкнути цей тип доступу через SSO.'
message_api.base.user_not_exists_in_ad: 'Неправильні облікові дані або користувача не існує в Active Directory'
message_api.controller.error_get_course: 'Виникла помилка при спробі зареєструвати користувача - Помилка:'
message_api.controller.chapter_init: 'Розділ розпочато належним чином'
message_api.controller.error_chapter_init: 'Виникла помилка при спробі запустити розділ:'
message_api.controller.error_chapter_update: 'Розділ коректно оновлено'
message_api.controller.help_text: 'Допоможіть. Тексти успішно отримано'
message_api.controller.course_not_found: 'Курсів не знайдено'
message_api.controller.user_not_access: 'Користувач не має доступу до цього курсу'
message_api.controller.evaluation_course: 'Оцінка подана'
message_api.controller.training_itinerary: 'Ваш тренувальний маршрут'
message_api.controller.continue_training: 'Подальше навчання'
message_api.message.not_found: 'Повідомлення не знайдено'
message_api.message.not_access: 'У доступі відмовлено'
message_api.scorm.init: 'Скорм розпочався належним чином'
message_api.scorm.not_package: 'Розділ не є пакетом SCORM'
message_api.scorm.button_add_scorm: 'Додавання пакета scorm'
message_api.scorm.button_delete_scorm: 'Видаліть пакет черв''яка'
message_api.scorm.title_package: 'Паркетний черв''як'
message_api.scorm.upload_file_scorm: 'Виберіть файл .zip'
message_api.scorm.info_upload_scorm: 'Ви повинні завантажити пакет scorm'
message_api.notification.not_found: 'Повідомлення не знайдено'
message_api.player.no_content: 'Цей розділ не має змісту'
message_api.alert.minimal_question: 'Для цієї гри потрібно мінімум %number% запитань.'
message_api.alert.image_puzzle: 'Пазл повинен мати зображення'
message_api.alert.question_correct: 'До цього питання потрібно додати правильну відповідь'
message_api.alert.chapter_content: 'У цьому розділі не вистачає змісту'
message_api.alert.pdf: 'Для цього розділу потрібен pdf-файл'
message_api.alert.video: 'У цьому розділі не вистачає змісту'
message_api.alert.slider: 'Цей розділ має щонайменше %number% зображень.'
message_api.alert.scorm: 'Для цієї глави потрібен пакет scorm'
message_api.alert.question_hidden: 'Це питання має містити лише одну відповідь, і вона має бути правильною'
message_api.alert.minimal_pair: 'Цей розділ вимагає мінімум пар %number%.'
message_api.announcement.call_for_aplications: Скликання
message_api.alert.course_content_incomplete: 'У розділах бракує інформації, будь ласка, перегляньте і додайте контент, щоб продовжити роботу над конкурсом заявок.'
message_api.diploma.supered: 'За подолання'
message_api.diploma.granted: Надано
message_api.diploma.date: Дата
message_api.diploma.diploma: Диплом
message_api.diploma.trato: Д./Донья
message_api.diploma.nif: 'з ідентифікаційним податковим номером'
message_api.diploma.services: 'хто надає послуги Компанії'
message_api.diploma.cif: 'з номером ПДВ'
message_api.diploma.evaluation: 'Ви пройшли навчальний захід з позитивною оцінкою'
message_api.diploma.code: 'Код AF/GRUPO'
message_api.diploma.between: 'Протягом днів'
message_api.diploma.duration: 'загальною тривалістю'
message_api.diploma.fomartionType: 'годин у режимі навчання'
message_api.diploma.courseContent: 'Зміст курсу на наступній сторінці'
message_api.diploma.signatureSeal: 'Підпис та печатка особи, відповідальної <br> за проведення навчання.'
message_api.diploma.signatureDate: 'Дата випуску'
message_api.diploma.acreditation: Акредитація
message_api.diploma.signature: 'Підпис учасника'
message.api.diploma.type_course_default: 'Електронне навчання'
message.api.diploma.dates: 'Дата з %dateFrom% по %dateTo%'
message.api.diploma.hours: '{0} %count% годин|{1} %count% час|]1,Inf] %count% годин'
message.api.diploma.duration: Тривалість
message.api.diploma.with_dni: 'з %nameIdentification% %dni%'
message_api.announcement.cancelation_announcement: 'Скасування виклику %course%'
message_api.help_category.delete_error: 'Неможливо видалити категорію %categoryName%, вона має пов''язаний вміст'
