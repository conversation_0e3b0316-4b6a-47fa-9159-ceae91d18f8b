message_api.content.scorm_correctly: 'Scorm korrekt gestartet'
message_api.content.token_not_send: 'Token nicht abgeschickt'
message_api.content.error_get_content: '<PERSON><PERSON>, ein <PERSON><PERSON><PERSON> zu starten, ist ein <PERSON> aufgetreten:'
message_api.content.scorm_user_not_access: 'Der Benutzer hat keinen Zugang zu diesem Kurs'
message_api.content.scorm_not_content: 'Das Kapitel ist kein Inhaltspaket'
message_api.base.credentials_no_correct: 'Die eingegebenen Anmeldedaten sind nicht korrekt.'
message_api.base.authorized_access: 'Unerlaubter Zugriff'
message_api.base.user_successfully: 'Benutzer erfolgreich abgemeldet'
message_api.base.error_login: '<PERSON><PERSON>, den Benutzer anzumelden, ist ein Fehler aufgetreten - Fehler:'
message_api.base.authorized_token_fail: 'Sie konnten nicht über SSO zugreifen. Wenn Sie Ihren Benutzer noch nicht auf der Plattform registriert und aktiviert haben, müssen <PERSON>e dies tun, um diese Art des SSO-Zugangs zu ermöglichen.'
message_api.base.user_not_exists_in_ad: 'Falsche Anmeldeinformationen oder der Benutzer existiert nicht in Active Directory'
message_api.controller.error_get_course: 'Beim Versuch, den Benutzer zu registrieren, ist ein Fehler aufgetreten - Fehler: '
message_api.controller.chapter_init: 'Kapitel richtig gestartet'
message_api.controller.error_chapter_init: 'Beim Versuch, ein Kapitel zu starten, ist ein Fehler aufgetreten:'
message_api.controller.error_chapter_update: 'Kapitel korrekt aktualisiert'
message_api.controller.help_text: 'Hilfetexte erfolgreich abrufen'
message_api.controller.course_not_found: 'Keine Kurse gefunden'
message_api.controller.user_not_access: 'Der Benutzer hat keinen Zugang zu diesem Kurs'
message_api.controller.evaluation_course: 'Bewertung eingereicht'
message_api.controller.training_itinerary: 'Ihr Lernweg'
message_api.controller.continue_training: 'Weiter lernen'
message_api.message.not_found: 'Nachricht nicht gefunden'
message_api.message.not_access: 'Zugang nicht gestattet'
message_api.scorm.init: 'Scorm korrekt gestartet'
message_api.scorm.not_package: 'Das Kapitel ist kein SCORM-Paket'
message_api.scorm.button_add_scorm: 'Scorm-Paket hinzufügen'
message_api.scorm.button_delete_scorm: 'Scorm-Paket löschen'
message_api.scorm.title_package: 'Scorm Paket'
message_api.scorm.upload_file_scorm: 'Wählen Sie eine .zip-Datei'
message_api.scorm.info_upload_scorm: 'Sie müssen das Scorm-Paket laden'
message_api.notification.not_found: 'Benachrichtigung nicht gefunden'
message_api.player.no_content: 'Dieses Kapitel hat keinen Inhalt'
message_api.alert.minimal_question: 'Dieses Spiel erfordert ein Minimum von %number% Fragen.'
message_api.alert.image_puzzle: 'Das Rätsel muss ein Bild enthalten'
message_api.alert.question_correct: 'Zu dieser Frage müssen Sie eine richtige Antwort hinzufügen'
message_api.alert.chapter_content: 'Diesem Kapitel fehlt es an Inhalt'
message_api.alert.pdf: 'Dieses Kapitel benötigt ein pdf'
message_api.alert.video: 'Diesem Kapitel fehlt es an Inhalt'
message_api.alert.slider: 'Dieses Kapitel besteht aus mindestens %number% Bildern.'
message_api.alert.scorm: 'Dieses Kapitel benötigt ein Scorm-Paket'
message_api.alert.question_hidden: 'Diese Frage darf nur eine Antwort enthalten, und diese muss richtig sein'
message_api.alert.minimal_pair: 'Dieses Kapitel erfordert ein Minimum von %number% Paaren.'
message_api.announcement.call_for_aplications: Einberufung
message_api.alert.course_content_incomplete: 'Es fehlen Informationen in den Kapiteln, bitte überarbeiten und ergänzen Sie den Inhalt, um mit der Erstellung der Aufforderung zur Einreichung von Vorschlägen fortzufahren.'
message_api.diploma.supered: 'Für die Überschreitung'
message_api.diploma.granted: 'Erteilt an'
message_api.diploma.date: Datum
message_api.diploma.diploma: Diplom
message_api.diploma.trato: Herr/Frau
message_api.diploma.nif: 'mit Steueridentifikationsnummer'
message_api.diploma.services: 'die für das Unternehmen arbeiten'
message_api.diploma.cif: 'mit Mehrwertsteuernummer'
message_api.diploma.evaluation: 'Hat die Ausbildungsmaßnahme mit einer positiven Bewertung bestanden.'
message_api.diploma.code: 'Kode AF/ GROUP'
message_api.diploma.between: 'Während der Tage'
message_api.diploma.duration: 'mit einer Gesamtdauer von'
message_api.diploma.fomartionType: 'Stunden im Trainingsmodus'
message_api.diploma.courseContent: 'Kursinhalte auf der folgenden Seite'
message_api.diploma.signatureSeal: 'Unterschrift und Stempel der für die Durchführung der Ausbildung verantwortlichen <br> Stelle.'
message_api.diploma.signatureDate: 'Datum der Ausstellung'
message_api.diploma.acreditation: Akkreditierung
message_api.diploma.signature: 'Unterschrift des Teilnehmers'
message.api.diploma.type_course_default: E-learning
message.api.diploma.dates: 'Datum von %dateFrom% bis %dateTo%'
message.api.diploma.hours: '{0} %count% Stunden|{1} %count% Stunde|]1,Inf] %count% Stunden'
message.api.diploma.duration: Dauer
message.api.diploma.with_dni: 'mit %nameIdentification% %dni%'
message_api.announcement.cancelation_announcement: 'Annullierung des Anrufs %course%'
message_api.help_category.delete_error: 'Kategorie %categoryName% kann nicht gelöscht werden, da sie verknüpften Inhalt hat'
