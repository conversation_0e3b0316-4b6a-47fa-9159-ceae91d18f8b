menu.title_platform: 'Campus Formativo'
complete: Completato
Show: Ver
Edit: Modificar
Remove: Quitar
Delete: Cancellare
total: Totale
'Yes': Sì
'No': 'No'
Actions: Azioni
Clear: Limpiar
'No results found': 'Nessun risultato trovato'
Configuration: Configurazione
Limit: Limite
Close: Cerrar
Save: Guardar
'Save and create other': 'Creare e aggiungere un altro'
'Save changes': 'Salva le modifiche'
'Save and keep editing': 'Salvare e continuare a modificare'
state: Stato
create: Crear
cancelar: Annullamento
back: 'Ritorno a'
add: Agregar
no_content: 'Nessun contenuto'
no_result: 'Non sono stati trovati risultati'
configure_simulator: 'Configurare il simulatore'
edit_configure_simulator: 'Modifica della configurazione'
configure_success: 'La configurazione è riuscita'
save_success: 'Registrazione salvata con successo'
error_success: 'Si è verificato un errore durante il salvataggio del record'
configure_completed: 'Configurazione completata'
'Created At': Creato
'Created By': 'Creato da'
'Created by': 'Creato da'
'Updated At': Aggiornato
'Updated By': 'Aggiornato da'
'Deleted By': 'Eliminato da'
'Deleted At': Soppresso
menu.courses_managment.title: 'Gestione del corso'
menu.courses_managment.Segments: Segmenti
menu.courses_managment.categories: Categorie
menu.courses_managment.level: Livelli
menu.courses_managment.courses: Corsi
menu.courses_managment.announcements: Convocatoria
menu.courses_managment.nps_question: 'Preguntas NPS'
menu.courses_managment.opinion_course: 'Opinioni sul corso'
menu.help_managment.title: 'Gestione degli aiuti'
menu.help_managment.content_help: 'Aiuto per i contenuti'
menu.help_managment.categories_help: 'Aiuto di categoria'
menu.users_managment.title: 'Gestione degli utenti'
menu.users_managment.users: Utenti
menu.users_managment.managers: Dirigenti
menu.users_managment.filter: Filtros
menu.news.title: Noticias
menu.stats.title: Statistiche
menu.stats.export: 'Strumento Excel'
menu.users.edit_profile: 'Modifica profilo'
form.label.delete: Cancellare
action.save: 'Risparmia ora'
common_areas.created_at: Creato
common_areas.updated_at: Aggiornato
common_areas.deleted_at: Aggiornato
common_areas.created_by: 'Creato da'
common_areas.updated_by: 'Aggiornato da'
common_areas.actions: Azioni
common_areas.basic_information: 'Informazioni di base'
common_areas.edit: Editar
common_areas.delete: Cancellare
common_areas.name: Numero
common_areas.image: Immagini
common_areas.state: Stato
common_areas.create: Crear
common_areas.save: Guardar
common_areas.back_list: 'Torna all''elenco'
course_category.label_in_singular: 'Categoria del corso'
course_category.label_in_plural: 'Categorie di corsi'
course_category.configureFields.category_name: 'Nome Categoria'
course_category.configureFields.category_order: 'Categoria dell''ordine'
course_category.configureFields.translations: Traduzioni
course.label_in_singular: Corso
course.label_in_plural: Corsi
course.back_to_course: 'Torna al corso'
course.configureFields.basic_information: 'Informazioni di base'
course.configureFields.code: Codice
course.configureFields.name: Numero
course.configureFields.description: Descrizione
course.configureFields.basic: Base
course.configureFields.access_level: 'Livello di accesso'
course.configureFields.clone: Clonar
course.configureFields.open: Abierto
course.configureFields.open_visible: 'Visibile su Open Campus'
course.configureFields.active: Attivo
course.configureFields.categories: Categorie
course.configureFields.profesional_categories: 'Categorie professionali'
course.configureFields.image: Immagini
course.configureFields.chapter: Capitolo
course.configureFields.translation: Traduzione
course.configureFields.general_information: 'Informazioni generali'
course.configureFields.segment: Segmento
course.configureFields.category: Categoria
course.configureFields.thumbnail_url: 'Url miniatura'
course.configureFields.locale: Posizione
course.configureFields.all_seasons: 'Tutte le stagioni'
course.configureFields.chapters: Capitoli
course.configureFields.seasons: 'Le stagioni'
course.configureFields.courses_translate: Traduzioni
course.configureFields.add_chapter: 'Capitolo aggregato'
course.configureFields.no_seasons: 'In stagione'
course.configureFields.add_seasons: 'Stagione aggregata'
course.configureFields.add_annuncement: 'Aggiungi chiamata'
course.configureFields.question_modal_translate: 'volete davvero tradurre questo corso?'
course.configureFields.content_modal_translate: 'Questa azione creerà una copia del corso da utilizzare come guida per la traduzione in un''altra lingua.'
course.configureFields.translate_already: 'Questo corso ha già una traduzione in questa lingua'
course.configureFields.tag_description: 'Separare le etichette premendo invio'
course.configureFields.new: Nuovo
course.configureFields.add_material: 'Aggiungi materiale'
course.configureFields.add_task: 'Agregar Tarea'
course.configureFields.task: Tareas
course.season_add: 'La stagione è stata aggiunta correttamente'
course.season_update: 'La stagione è stata aggiornata correttamente'
course.season_add_error: 'Si è verificato un errore durante l''aggiunta della stagione'
course.panel.class: 'Dettagli del corso'
chapter.label_in_plural: Capitoli
chapter.configureFields.title: Titolo
chapter.configureFields.course: Corso
chapter.configureFields.type: Tipo
chapter.configureFields.season: Stagione
chapter.configureFields.description: Descrizione
chapter.configureFields.image: Immagini
chapter.configureFields.image_file: 'Immagine d''archivio'
chapter_type.description.1: '<p>Il capitolo Scorm è molto interessante:</p><p>Consente di caricare un''ampia gamma di contenuti generati con altri strumenti, ad esempio documenti, contenuti interattivi e persino giochi.</p>'
chapter_type.description.2: '<p>Questo è uno dei capitoli più versatili.</p><p>Sulla sinistra sono riportati i titoli inseriti, che servono come indice per trovare rapidamente i contenuti e facilitare la lettura.</p>'
chapter_type.description.3: '<p>È un gioco a quiz che aggiunge una componente casuale, in quanto per superarlo bisogna completare i segmenti di una roulette.</p><p>Si basa sulla creazione di una batteria di domande per rafforzare le conoscenze apprese. È possibile inserire tutte le domande che si desidera e accompagnarle con immagini.</p><p>Per un corretto funzionamento del gioco, è consigliabile inserire un minimo di 10 domande.</p>'
chapter_type.description.4: '<p>Questo gioco prevede una serie di domande che includono un fattore di rischio aggiuntivo. Dopo ogni domanda, i partecipanti possono scegliere se rimanere in piedi e mantenere il punteggio attuale o rischiare di rispondere a un''altra domanda per ottenere più punti. Tuttavia, in caso di risposta errata, tutti i punti accumulati fino a quel momento vengono persi.</p>'
chapter_type.description.5: '<p>Questo è il più classico dei capitoli del gioco.</p><p>L''idea è quella di creare una batteria di domande per rafforzare le conoscenze apprese. È possibile inserire un numero illimitato di domande accompagnate da un''immagine e con una sola risposta corretta.</p>'
chapter_type.description.6: '<p>Sistemare e ruotare i pezzi finché non sono nella posizione e nell''orientamento corretti, altrimenti non si incastreranno tra loro.</p><p>In alto ci sono quattro segmenti che corrispondono al tempo a disposizione per completare il puzzle. Quando un segmento di tempo scade, viene posta una delle domande che abbiamo inserito. Azzeccando le domande si ottiene più tempo per risolvere il puzzle. Il punteggio finale dipenderà dalla combinazione del tempo impiegato per completare il puzzle stesso, dal numero di domande corrette e dal numero di domande mancate.</p>'
chapter_type.description.7: '<p>Questo gioco propone un puzzle basato su un''immagine. Per risolverlo bisogna selezionare con cura le lettere giuste prima che scada il tempo.</p>'
chapter_type.description.8: '<p>Dal momento che il formato PDF è ampiamente utilizzato per diversi tipi di contenuti, come protocolli o manuali, i capitoli PDF sono molto interessanti, in quanto ci permettono di riutilizzare materiale già pubblicato.</p>'
chapter_type.description.9: '<p>Le risorse audiovisive hanno un grande potenziale pedagogico, attirano, catturano l''attenzione e suscitano curiosità.</p><p>La piattaforma ci permette di scegliere come introdurre il video tramite "url", oppure selezionando un file che si trova sul nostro computer. In quest''ultimo caso, possiamo allegare un file di sottotitoli.</p>'
chapter_type.description.10: '<p>Capitolo di tipo slider con immagini.</p>'
chapter_type.description.11: '<p>Il gioco consiste nell''abbinare le parole, ognuna delle quali corrisponde a una lettera della ruota. A volte la soluzione sarà una parola che inizia con la lettera, altre volte conterrà semplicemente la lettera.</p>'
chapter_type.description.12: '<p>In questo gioco vi verrà posta una serie di domande sotto forma di testo, immagine o una combinazione di entrambi sotto forma di affermazioni. Ci sono due possibili risposte "Vero" o "Falso" e solo una è corretta. Il tempo a disposizione per risolvere il gioco è limitato.</p>'
chapter_type.description.13: '<p>In questo gioco dovrete risolvere un indovinello prima che scada il tempo. L''indizio sarà nascosto dietro un''immagine sfocata che verrà gradualmente messa a fuoco man mano che si procede nel gioco. Oltre all''immagine, vi è anche un aiuto supplementare sotto forma di testo.</p>'
chapter_type.description.14: '<p>In questo gioco classico, bisogna disporre gli elementi trascinando i blocchi nell''ordine corretto. La varietà di possibilità lo rende ideale per esercizi di matematica e altre sfide educative. Ideale per creare un test che metta alla prova le capacità di ragionamento e di ordinamento.</p>'
chapter_type.description.15: '<p>Questo gioco è ideale per allenare la memoria e la concentrazione. L''obiettivo è trovare tutte le coppie di carte uguali. La posizione delle carte è creata in modo casuale, quindi ogni partita sarà diversa.</p>'
chapter_type.description.16: '<p>In questo gioco vi verrà presentata una serie di parole, frasi o concetti che dovrete associare alla famiglia o al gruppo corrispondente indicato di seguito. Metterete alla prova le vostre capacità di associazione e la vostra rapidità mentale correndo contro il tempo.</p>'
chapter_type.description.17: '<p>In questo gioco di grammatica e apprendimento, lo scopo è quello di riempire gli spazi vuoti nelle frasi con le parole giuste per mettere alla prova le vostre abilità linguistiche e grammaticali. Ma non è tutto! Questo gioco è versatile e può essere utilizzato per molti altri scopi educativi.</p>'
chapter_type.description.18: '<p>In questo gioco, vi verrà presentata una domanda o un indovinello da risolvere. Il compito sarà quello di esaminare attentamente l''indovinello e utilizzare le lettere fornite per trovare la parola corretta. Ma attenzione, perché il tempo a disposizione è limitato, il che significa che dovrete essere veloci e precisi per vincere.</p>'
chapter_type.description.19: '<p>In questo gioco dovrete indovinare una parola nascosta in un massimo di sei tentativi. Ogni tentativo consisterà nell''inserire una parola valida e, dopo ogni tentativo, il colore delle caselle cambierà per mostrare quali lettere sono corrette e quali sono anche nella posizione corretta.</p>'
chapter_type.description.20: '<p>Questo gioco consiste nel trovare parole nascoste in una ricerca di parole. Lo scopo è quello di contrassegnare una sequenza di lettere in orizzontale, verticale o diagonale. Le parole possono essere trovate in entrambe le direzioni, da sinistra a destra o da destra a sinistra. Se la sequenza fa parte di una parola nascosta, sarà considerata una risposta corretta.</p>'
chapter_type.description.21: '<p>Durante la riproduzione di un video, vengono inserite domande interattive che richiedono allo spettatore di prestare attenzione al contenuto del video per rispondere correttamente. In breve, combinando la potenza del video con l''interattività del quiz si ottiene un''esperienza di apprendimento efficace e coinvolgente.</p>'
chapter_type.add.1: 'Agregar scorm'
chapter_type.add.2: 'Aggiungi contenuto'
chapter_type.add.3: 'Crea gioco'
chapter_type.add.4: 'Crea gioco'
chapter_type.add.5: 'Crea quiz'
chapter_type.add.6: 'Crea gioco'
chapter_type.add.7: 'Crea gioco'
chapter_type.add.8: 'Aggregato PDF'
chapter_type.add.9: 'Aggiungi video'
chapter_type.add.10: 'Cursore di registrazione'
chapter_type.add.11: 'Crea gioco'
chapter_type.add.12: 'Crea gioco'
chapter_type.add.13: 'Crea gioco'
chapter_type.add.14: 'Crea gioco'
chapter_type.add.15: 'Crea gioco'
chapter_type.add.16: 'Crea gioco'
chapter_type.add.17: 'Crea gioco'
chapter_type.add.18: 'Crea gioco'
chapter_type.add.19: 'Crea gioco'
chapter_type.add.20: 'Crea gioco'
chapter_type.add.21: 'Creare video quiz'
chapter_type.all: Tutti
chapter_type.content: Teoria
chapter_type.games_test: Valutazione
chapter_type.description_test: 'Descrizione del test'
chapter_type.type: 'Tipo di capitolo'
chapter.add_pdf: 'Aggiungi il pdf'
chapter.chapter.show_video: 'Guarda il video'
chapter.message_pdf_success: 'Il pdf è stato aggiunto con successo'
chapter.message_pdf_error: 'Si è verificato un errore durante il salvataggio del pdf'
chapter.chapter.materials: 'I materiali'
chapter.chapter.show_pdf: 'Visualizza il pdf'
announcements.label_in_singular: Convocatoria
announcements.label_in_plural: Convocatorias
announcements.configureFields.courses: Corsi
announcements.configureFields.start_at: Inizio
announcements.configureFields.finish_at: Fine
announcements.configureFields.called: chiamato
announcements.configureFields.subsidized: Sovvenzionato
announcements.configureFields.subsidizer: Tecnico
announcements.configureFields.subsidizer_entity: 'Ente sovvenzionatore'
announcements.configureFields.subsidized_announcement: 'Bando per le sovvenzioni'
announcements.configureFields.max_users: 'Utenti massimi'
announcements.configureFields.formative_action_type: 'Tipo di azione formativa'
announcements.configureFields.format: Formato
announcements.configureFields.total_hours: 'Ore totali'
announcements.configureFields.place: Luogo
announcements.configureFields.training_center: 'Centro di formazione'
announcements.configureFields.training_center_address: 'Indirizzo del centro di formazione'
announcements.configureFields.training_center_nif: 'Centro di formazione NIF'
announcements.configureFields.training_center_phone: 'Numero di telefono del centro di formazione'
announcements.configureFields.training_center_email: 'E-mail del centro di formazione'
announcements.configureFields.training_center_teacher_dni: 'Centro di formazione per insegnanti DNI'
announcements.configureFields.called_user: 'Gli utenti hanno chiamato'
announcements.configureFields.search: Ricerca
announcements.configureFields.announcement_for: 'Convocatoria para'
announcements.configureFields.search_user_title: 'Ricerca di utenti da invitare a candidarsi'
announcements.configureFields.placeholder_search_user: 'Ricerca di utenti'
announcements.configureFields.placeholder_search_category: 'Ricerca per categoria'
announcements.configureFields.placeholder_search_department: 'Ricerca per reparto'
announcements.configureFields.placeholder_search_center: 'Ricerca per centro'
announcements.configureFields.placeholder_search_country: 'Ricerca per paese'
announcements.configureFields.placeholder_search_division: 'Ricerca per divisione'
announcements.configureFields.result_found: 'Risultati trovati'
announcements.configureFields.clear_result: 'Risultati chiari'
announcements.configureFields.error_already_called_user: 'Errore: l''utente è già stato chiamato!'
announcements.configureFields.error_already_called_user_date: 'Errore: l''utente è già stato chiamato in un intervallo di date simile!'
announcements.configureFields.notified: Notificato
announcements.configureFields.content_course: 'Contenuto del corso'
announcements.configureFields.report: Rapporto
announcements.configureFields.title_report: 'Rapporto degli studenti'
announcements.configureFields.direction: Indirizzo
announcements.configureFields.telephone: Telefono
announcements.configureFields.nif: NIF
announcements.configureFields.tutor: Tutor
announcements.configureFields.apt: Apto
announcements.configureFields.time_total: 'Tempo totale'
question.label_in_singular: Pregunta
question.label_in_plural: Preguntas
question.configureFields.question: Pregunta
question.configureFields.random: Aleatorio
question.configureFields.answers: Risposte
question.configureFields.image_file: 'Immagine del file'
question.configureFields.question_for: 'Domande per'
question.configureFields.image_for: 'Immagine per'
question.configureFields.add_image_puzzle: 'Aggiungere l''immagine del puzzle'
question.configureFields.add_question: 'Creare una domanda'
question.configureFields.see_image: 'Visualizza immagine'
content.label_in_singular: Contenuto
content.label_in_plural: Contenuti
content.configureFields.title: Titolo
content.configureFields.content: Contenuto
content.configureFields.position: Posizione
content.configureFields.add_content: 'Aggiungi contenuto'
content.configureFields.content_for: 'Contenuto per'
question_nps.label_in_singular: 'Pregunta NPS'
question_nps.label_in_plural: 'Preguntas NPS'
question_nps.configureFields.type: Tipo
question_nps.configureFields.position: Posizione
question_nps.configureFields.question: Pregunta
question_nps.configureFields.course: Corso
question_nps.configureFields.name_question: 'Domanda sul nome'
question_nps.configureFields.translations: Traduzioni
opinions.label_in_singular: Opinione
opinions.label_in_plural: Opiniones
opinions.configureFields.course: Corso
opinions.configureFields.question: Pregunta
opinions.configureFields.to_post: pubblicare
opinions.configureFields.value: Valore
opinions.configureFields.valoration: Valutazione
help_category.label_in_singular: 'Aiuto di categoria'
help_category.label_in_plural: 'Categorie di aiuto'
help_category.configureFields.category_name: 'Nome della categoria'
help_category.configureFields.translations: Traduzioni
help_text_content.label_in_singular: 'Aiuto per i contenuti'
help_text_content.label_in_plural: 'Aiuto per i contenuti'
help_text_content.configureFields.category: Categoria
help_text_content.configureFields.title: Titolo
help_text_content.configureFields.text: Testo
help_text_content.configureFields.translations: Traduzioni
user.label_in_singular: Utente
user.label_in_plural: Utenti
user.configureFields.division: Divisione
user.configureFields.country: Paese
user.configureFields.category: Categoria
user.configureFields.departament: Dipartimento
user.configureFields.center: Centro
user.configureFields.gender: Genere
user.configureFields.first_name: Numero
user.configureFields.last_name: Apellido
user.configureFields.code: Codice
user.configureFields.password: Password
user.configureFields.change_password: 'Modifica della password'
user.configureFields.courses: Corsi
user.configureFields.extra: Extra
user.configureFields.announcements: Convocatorias
user.configureFields.extra_fields: 'Campi aggiuntivi'
user.configureFields.avatar_image: 'Immagine del profilo'
user.configureFields.new_password: 'Nuova password'
user.configureFields.birthdate: 'Data di nascita'
user.configureFields.edit_user: 'Modifica utente'
user.configureFields.user_data: 'Dati utente'
user.configureFields.stats: Statistiche
user.configureFields.chapter: Capitoli
user.configureFields.ratio_course: 'Rapporto Corsi/Persone'
user.configureFields.avg_stars: 'Media delle stelle'
user.configureFields.time: 'Il tempo'
user.configureFields.chapter_time: 'Tempo impiegato per capitolo'
user.configureFields.available: disponibile
user.configureFields.messages: Messaggi
user.configureFields.login_history: 'Cronologia di accesso'
user.configureFields.started_at: Inizio
user.configureFields.finished_at: Fine
user.configureFields.time_spent: 'Tempo impiegato'
user.configureFields.content_viewed: 'Contenuti visualizzati'
user.configureFields.interaction_with_teacher: 'Interazioni con l''insegnante'
user.configureFields.course_content: 'Contenuto del corso'
user.configureFields.content_type: 'Tipo di contenuto'
user.configureFields.finished: Completato
user.configureFields.teacher_interaction: 'Interazioni con gli insegnanti'
user.configureFields.date: Data
user.configureFields.sender: Remitente
user.configureFields.recipient: Destinatario
user.configureFields.subject: Oggetto
user.configureFields.questions: Preguntas
user.configureFields.chapter_type: 'Tipo di capitoli'
user.configureFields.finished_chapter_types: 'Tipi di capitoli completati'
user.configureFields.button_validate: Validar
user.configureFields.open: 'Campus Abierto'
user.configureFields.computer: Computer
user.configureFields.mobile: Mobile
user.configureFields.tablet: Tavoletta
user.manage.assign_data: 'Assegnare i dati'
user.gender.m: Uomo
user.gender.f: Donna
user.configureFields.time_title: 'Dedizione al giorno'
user.configureFields.interaction_in_forum: 'Interazioni nel forum'
user.configureFields.email: Email
user.configureFields.fullname: 'Nome e cognome'
user.filtersRequired: 'Selezionare almeno un filtro'
stats.general_stats: 'Statistiche generali'
stats.total_times_spent: 'Tempo totale impiegato'
stats.users_activity: 'Attività dell''utente'
stats.users_active_last_30: 'Attività degli ultimi 30 giorni'
stats.users_inactive_last_30: 'Nessuna attività negli ultimi 30 giorni'
stats.users_never_login: 'Non sono mai entrati'
stats.daily_chapter: 'Capitoli giornalieri completati'
stats.finished_chapters: 'Capitoli completati'
stats.daily_course: 'Corsi giornalieri completati'
stats.finished_courses: 'Corsi completati'
stats.daily_login: 'Accesso giornaliero'
stats.daily_login_tooltip: Accesso
stats.all_courses: 'Tutti i corsi'
stats.all_countries: 'Tutti i paesi'
stats.all_centers: 'Tutti i centri'
stats.all_categories: 'Tutte le categorie'
stats.all_departament: 'Tutti i dipartimenti'
stats.all_gender: 'Tutti i generi'
stats.all_divisions: 'Tutte le divisioni'
stats.filters: Filtros
stats.filter_by: 'Filtrare per'
stats.modal_close: Cerrar
stats.clear_filters: 'Limpiar filtros'
stats.apply_filters: 'Applicazione dei filtri'
stats.export_title: 'Esportazione dei dati'
stats.export.start_date: 'Data di inizio'
stats.export.end_date: 'Data finale'
stats.export.filename: 'Nome del file'
stats.export.request_date: 'Data della domanda'
stats.export.available_until: 'Disponibile fino a'
stats.export.loading_data: 'Caricamento dei dati'
stats.export.no_data: 'Nessun dato disponibile'
stats.export.download_file: 'Scarica il file'
stats.export.abort_export_request: 'Annullamento dell''applicazione di esportazione'
stats.export.view_details: 'Visualizza dettagli'
stats.export.reset_form: 'Azzeramento dei campi'
stats.export.error_start_date: 'La data di inizio non può essere successiva alla data di fine.'
stats.export.export_error: 'Si è verificato un errore nella generazione del rapporto.'
stats.export.export_success: 'Il vostro rapporto è stato aggiunto con successo alla coda di download.'
stats.export.export_dir: 'Statistiche / Strumento Excel'
stats.devices_login: 'Accesso al dispositivo'
stats.distribution_ages: 'Distribuzione per età'
stats.generation_babyboom: BabyBoom
stats.generation_x: 'Generazione X'
stats.generacion_milenials: Millennials
stats.generacion_z: 'Generazione Z'
stats.title_information_user: 'Informazioni sull''utente'
stats.title_information_content: 'Informazioni sul contenuto'
stats.title_information_courses: 'Informazioni sul corso'
stats.title_information_chapter: 'Informazioni sul capitolo'
stats.distribution_country: 'Distribuzione per paese'
stats.title_finish_m: finito
stats.title_made: realizzato
stats.title_made_f: realizzato
stats.chapter_day: giorni
stats.chaper_hours: ore
stats.chapter_minutes: minuti
stats.chapter_total: TOTALE
stats.chapter_media: MEDIA
stats.content_active: patrimonio
stats.content_active_f: attivo
stats.totalLogin: Totale
stats.access: Accesso
stats.uniqueLogin: Unico
stats.at_least_one_course_finished: 'Utenti formati'
stats.top_rated_courses: 'I corsi più votati'
stats.lowest_rated_courses: 'I corsi con le valutazioni più basse'
stats.most_completed_courses: 'Corsi più completati'
stats.users_more_actives: 'Utenti più attivi'
stats.users_less_actives: 'Utenti meno attivi'
stats.accumulative.title: 'Evolutivo e cumulativo'
stats.accumulative.trained: 'Utenti singoli addestrati'
stats.accumulative.new: Nuovo
stats.accumulative.accumulated: Accumulato
stats.accumulative.chart: Grafico
stats.accumulative.logins: Accesso
stats.accumulative.courses: Corsi
stats.accumulative.courses_started: 'Corsi iniziati'
stats.accumulative.courses_finished: 'Corsi completati'
stats.accumulative.ratings: Valutazioni
stats.accumulative.time: 'Tempo investito (in ore)'
stats.accumulative.filters: 'Distribuzione dei filtri'
'stats. daily_posts': 'Messaggi quotidiani sul forum'
stats.most_active_threads: 'Le discussioni più attive'
stats.most_active_users: 'Utenti più attivi'
stats.forum_post_messages_count: Messaggi
stats.forum_post_title: Titolo
task.status.pending: 'In attesa'
task.status.in_progress: 'A Progreso'
task.status.success: Completato
task.status.failure: Errore
security.login_button_login: Entrare
security.login_button_create_account: 'Creare un account'
security.login_title: 'Inserisci i tuoi dati'
security.login_remember_me: Registrare
security.login_question_password: 'avete dimenticato la password?'
security.button_register: Registro
security.button_exist_accoutn: 'Ho già un account'
security.button_account: 'Accettare le condizioni'
security.first_name: Numero
security.last_name: Apellido
security.password: Password
security.repeat_password: 'Ripetere la password'
security.register: Registro
security.remembered_the_password: 'avete ricordato la vostra password?'
security.button_send_email: 'Invia e-mail'
security.reset_your_password: 'Reimpostare la password'
security.text_reset_password: 'Inserite il vostro indirizzo e-mail e vi invieremo un link per reimpostare la vostra password.'
course_level.label_in_singular: Livello
course_level.label_in_plural: Livelli
component_video.add_package_video: 'Aggiungi pacchetto video'
component_video.edit_package_video: 'Modifica del pacchetto video'
component_video.type: Tipo
component_video.url_video: 'Video url'
component_video.file_subtitle: 'File dei sottotitoli'
component_video.button_save: Guardar
component_video.text_content_subtitle_video: 'Questo video ha già un sottotitolo. Se si aggiunge un nuovo sottotitolo, questo verrà sostituito da quello vecchio.'
component_video.upload_file_video: 'Selezionare il file video'
component_video.preparing_file: 'Aspettate a preparare il file'
component_video.package_video: Video
component_video.optimizing_video: 'Il video è in fase di ottimizzazione e sarà disponibile a breve'
component_video.text_good: Bene
filter_category.label_in_singular: 'Categoria del filtro'
filter_category.label_in_plural: 'Filtrare le categorie'
filter_category.configureFields.name: Numero
filter.label_in_singular: Filtro
filter.label_in_plural: Filtros
filter.configureFields.name: Numero
filter.configureFields.action_add: 'Aggiungi filtro'
filter.extras.no_filters: 'Nessun filtro assegnato'
filter.extras.loadings: Cargando...
filter.extras.no_filter_selected: 'Nessun filtro selezionato'
filter.extras.no_filter_assigned: 'Nessun filtro da assegnare'
news.form.title: Titolo
news.form.text: Testo
help.pdf.general: ADMIN_ES
help.video.general: '549279910'
segment_category.label_in_singular: 'Segmento di categoria'
segment_category.label_in_plural: 'Segmento categorie'
segment_category.configureFields.name: Numero
course_segmente.label_in_singular: Segmento
course_segmente.label_in_plural: Segmenti
course_segmente.configureFields.name: Numero
course_segmente.configureFields.action_add: 'Segmento aggregato'
documentation.label: Documentazione
documentation.title: Titolo
documentation.description: Descrizione
documentation.type: Tipo
documentation.file: Archivio
documentation.locale: Lingua
pdf.downloadable: Scaricabile
itinerary.label_in_singular: Itinerario
itinerary.label_in_plural: Itinerari
itinerary.name: Numero
itinerary.description: Descrizione
itinerary.tab.courses: Corsi
itinerary.tab.users: Utenti
itinerary.no_courses: 'Nessun corso è stato aggiunto all''itinerario'
itinerary.no_users: 'Nessun utente è stato aggiunto all''itinerario'
itinerary.saving_courses: 'Salvataggio dei corsi'
itinerary.find_available_courses: 'Ricerca dei corsi disponibili'
itinerary.find_selected_courses: 'Cerca tra i corsi selezionati'
itinerary.course.position_updated: 'Posizione del corso aggiornata'
itinerary.course.update_warning: 'I corsi di formazione saranno aggiornati'
itinerary.user.add_success: 'Utente aggiunto con successo'
itinerary.user.remove_success: 'Utente eliminato con successo'
itinerary.user.confirm_delete: 'L''utente perderà l''accesso all''itinerario'
itinerary.user.confirm_delete_all: 'Gli utenti perderanno l''accesso all''itinerario'
itinerary.manager.add_success: 'Manager aggiunto con successo'
itinerary.manager.remove_success: 'Manager rimosso con successo'
itinerary.manager.edit_manager: 'Manager editoriali'
itinerary.manager.find_managers: Manager
itinerary.manager.confirm_delete: 'Il gestore perderà l''accesso all''itinerario'
itinerary.manager.confirm_delete_all: 'I gestori perderanno l''accesso all''itinerario'
itinerary.filter.added: 'Filtro aggiunto all''itinerario'
itinerary.filter.removed: 'Filtro rimosso dall''itinerario'
itinerary.total_courses: 'Totale corsi'
common_areas.cancel: Annullamento
common_areas.add_all: 'Aggiungi tutti'
common_areas.remove_all: 'Cancellare tutti'
user_filter.modify_users: 'Modificare gli utenti'
user_filter.find_by: 'Ricerca di'
common_areas.total: Totale
common_areas.confirm_delete: "<p style=\"font-size: 14px;\">&lt;p&gt;&lt;b&gt;<span>Si vuole davvero eliminare questo elemento?</span>&lt;/b&gt;&lt;br&gt;</p>\n<p style=\"font-size: 14px;\"><span>Questa azione non può essere annullata.</span>&lt;/p&gt;</p>?"
common_areas.confirm_save: 'volete davvero risparmiare?'
challenges: Duelli
challenges.random: Aleatorio
challenges.question: Pregunta
challenges.correct: Corretto
challenges.answer1: 'Risposta 1'
challenges.answer2: 'Risposta 2'
challenges.answer3: 'Risposta 3'
challenges.answer4: 'Risposta 4'
challenges.answer5: 'Risposta 5'
challenges.answer6: 'Risposta 6'
material_course.configureFields.type: 'Tipo di file'
material_course.configureFields.save: 'Il materiale è stato conservato correttamente'
material_course.configureFields.type_1: Pdf
material_course.configureFields.type_2: Video
material_course.configureFields.type_3: Zip/Rar
material_course.configureFields.type_4: Immagini
material_course.configureFields.type_5: 'Pacchetti per l''ufficio'
material_course.configureFields.type_6: 'Blocco di note'
material_course.configureFields.file: Archivio
material_course.configureFields.no_material: 'Nessun materiale aggiunto'
material_course.configureFields.question_delete: 'volete davvero cancellare questo materiale?'
material_course.configureFields.question_decition: 'Questa azione non può essere annullata in seguito'
material_course.configureFields.delete: 'Materiale per le sbavature'
material_course.placeholder.file: 'Selezionare il file'
material_course.download: Scaricare
taskCourse.configureFields.noFile: 'Nessun file aggiunto'
taskCourse.configureFields.question_delete: 'volete davvero cancellare questo file?'
taskCourse.labelInSingular: 'Compiti a casa'
taskCourse.labelInPlural: Tareas
taskCourse.configureFields.dateDelivery: 'Data di consegna'
taskCourse.configureFields.startDate: 'Data di inizio'
taskCourse.configureFields.visible: Visibile
taskCourse.configureFields.senTask: 'Il compito è stato inviato'
taskCourse.configureFields.senTaskUser: 'Inviato a'
taskCourse.configureFields.addFile: 'Aggiungi file'
taskCourse.configureFields.state_0: 'In attesa'
taskCourse.configureFields.state_1: 'Consegnato a'
taskCourse.configureFields.state_2: 'In fase di revisione'
taskCourse.configureFields.state_3: Rifiutato
taskCourse.configureFields.state_4: Approvato
taskCourse.configureFields.files_attachment: Allegati
taskCourse.configureFields.sendComment: 'Il commento è stato inviato'
taskCourse.configureFields.stateTask: 'L''attività ha cambiato stato'
taskCourse.configureFields.history: Storico
component_game.true_or_false: 'Vero/falso o Categorizzare'
component_game.adivina_imagen: 'Indovina l''immagine'
component_game.ordenar_menorMayor: 'Ordine dal più piccolo al più grande'
component_game.parejas: 'In coppia'
component_game.rouletteWheel: 'Ruota di lettere'
component_game.categorized: Categorizzare
component_game.fillgaps: 'Colmare le lacune'
component_game.guessword: 'Lettere d''ordine'
component_game.wordle: 'Parola segreta'
component_game.lettersoup: 'Zuppa di lettere'
component_game.videoquiz: 'Video Quiz'
games.letterwheel: 'Ruota di lettere'
games.opciones: 'Scegliere un''opzione'
games.categorize: Categorizzare
games.optiones_empty: 'Per la categorizzazione è necessario aggiungere almeno due opzioni.'
games.validate_add_categorize: 'È necessario modificare il campo dell''affermazione, selezionare una risposta corretta o selezionare un''immagine'
games.add_category: 'Aggiungi opzione'
games.add_categories: 'Aggiungi gruppo o famiglia'
games.add_word: 'Aggiungi parola'
games.words: Parole
games.edit_option: 'Opzione di modifica'
games.text_common.answer: Risposta
'games.text_common:correct': Corretto
games.text_common.time: 'Il tempo'
games.text_common.word: Parola
games.text_common.no_questions: 'Nessuna domanda'
games.text_common.text_question: 'Testo della domanda'
games.text_common.word_question: 'Parola della domanda'
games.text_common.message_guess_word_question: 'È necessario inserire il testo della domanda'
games.text_common.message_guess_word_word: 'È necessario digitare la parola della domanda'
games.text_common.message_guess_word_time: 'È necessario inserire l''ora della domanda'
games.text_common.message_guess_word_answer: 'La risposta deve contenere una sola parola'
games.text_common.select_image: 'Selezionare l''immagine'
games.text_common.ilustre_category: 'Immagine per illustrare la categoria'
games.text_common.ilustre_question: 'Immagine per illustrare la domanda'
games.text_common.message_higher_lower: 'Creare le parole che si desidera far apparire nel gioco e ordinarle a piacere. È possibile modificare l''ordine trascinando le parole.'
games.validate_memory_match: 'Un titolo o un''immagine devono ancora essere aggiunti'
games.help: Aiuto
games.validate_hidden_image: 'La domanda, la soluzione e l''immagine sono obbligatorie'
games.fillgap.title: 'come costruire il gioco?'
games.fillgap.message: 'Nel campo "Aggiungi frase o lacuna" è possibile progettare la struttura del gioco e decidere se il testo sarà parte della frase o una lacuna. Quando si aggiunge una lacuna, questa viene visualizzata in blu, in modo da poterla identificare facilmente.'
games.fillgap.result_question: 'Risultato della partita'
games.fillgap.word: 'Aggiungere una frase o una lacuna'
games.fillgap.add_filler: 'Aggiungi frase'
games.fillgap.add_gap: 'Aggiungi gap'
games.fillgap.new_option: 'Nuova opzione'
games.fillgap.validate_save: 'È necessario aggiungere almeno una frase, una lacuna e due scelte'
games.videoquiz.message_validate_answer: 'È necessario aggiungere almeno due risposte e la risposta corretta non deve essere vuota'
games.videoquiz.time_video: 'Tempo di video'
games.videoquiz.savell_all_changes: 'Salvare tutte le modifiche'
games.videoquiz.validate_to_add_question: 'È necessario avere almeno una domanda per poter salvare le modifiche'
games.videoquiz.validate_letter_soup: 'Sembra che manchi il titolo o le parole'
chapter_type.1: Scorm
chapter_type.2: Contenuto
chapter_type.3: Ruleta
chapter_type.4: 'Doble o nada'
chapter_type.5: Quiz
chapter_type.6: Puzzle
chapter_type.7: 'Parola segreta'
chapter_type.8: Pdf
chapter_type.9: Video
chapter_type.10: Cursore
chapter_type.11: 'Ruote da lettera'
chapter_type.12: 'Verdadero o Falso'
chapter_type.13: Adivinanza
chapter_type.14: 'Dal più alto al più basso'
chapter_type.15: Parejas
chapter_type.16: 'dove si colloca?'
chapter_type.17: 'Colma le lacune'
chapter_type.18: 'Lettere d''ordine'
chapter_type.19: Enigma
chapter_type.20: 'Zuppa alfabetica'
chapter_type.21: 'Video Quiz'
menu.users.exit_impersonate: 'Uscire da impersonare'
menu.forum: Foro
course.export: 'Esportazione di corsi'
course.export.confirm: 'volete davvero esportare i corsi?'
announcements.configureFields.opinions: Opiniones
announcements.configureFields.no_messages: 'Nessun messaggio'
announcements.configureFields.info_max_users: 'Il numero massimo di utenti che possono essere chiamati alla chiamata è:'
announcements.configureFields.annoucement_all: 'Convocare tutti'
question_nps.configureFields.source: 'Candidarsi a'
user.actions.impersonate: Impersonare
user.show_cv: 'Visualizza il CV'
user.delete_cv: 'Cancellare il CV'
stats.export.download_file_pdf: 'Scaricare PDF'
stats.export.download_file_xlsx: 'Scarica Excel'
stats.segmented.title: 'Statistiche segmentate'
filter.removed_filter: 'Il filtro %s è stato rimosso con successo.'
filter.added_filter: 'Il filtro %s è stato aggiunto con successo.'
filter.all_removed: 'I filtri sono stati rimossi'
filter.all_added: 'Sono stati aggiunti dei filtri'
itinerary.chart.users: 'le persone hanno completato l''itinerario'
itinerary.chart.users_process: 'in corso'
itinerary.chart.users_incomplete: 'senza iniziare'
itinerary.chart.users_title: ' persone assegnate da'
itinerary.chart.total_time: 'Tempo totale cumulativo'
itinerary.chart.avg_time: 'Tempo medio per persona'
itinerary.chart.by_country: 'Itinerari per paese'
itinerary.chart.by_hotel: 'Itinerari per centro'
itinerary.chart.by_department: 'Itinerari per dipartimento'
itinerary.chart.by_grouping: 'Itinerari per raggruppamento'
itinerary.users_assign: 'Alle persone è stato assegnato questo itinerario'
itinerary.users.progress: 'Progressi nell''itinerario'
itinerary.users.download_user: 'Scarica Excel'
itinerary.courses.selected: 'Corsi selezionati'
itinerary.status.completed: Completato
itinerary.status.started: 'In corso'
itinerary.status.unstarted: 'Nessun inizio'
segmented_stats.title1: 'Persone formate'
segmented_stats.title2: Orario
segmented_stats.title3: Corsi
segmented_stats.title4: Accesso
segmented_stats.distribution_by_country: 'Distribuzione per paese'
segmented_stats.structure: Struttura
segmented_stats.hotel: Hotel
segmented_stats.by_department: 'A cura del Dipartimento'
segmented_stats.by_school: 'Da parte della scuola'
segmented_stats.total_hours: 'Ore totali'
segmented_stats.total_avg: 'Ore medie'
segmented_stats.structure_avg: 'Struttura media'
segmented_stats.structure_total: 'Struttura totale'
segmented_stats.hotel_avg: 'Hotel medio'
segmented_stats.hotel_total: 'Totale hotel'
segmented_stats.avg: Media
segmented_stats.courses_started: 'Corsi iniziati'
segmented_stats.courses_finished: 'Corsi completati'
segmented_stats.total_courses_started: 'Totale corsi avviati'
segmented_stats.total_courses_finished: 'Totale corsi completati'
segmented_stats.access_totals: 'Totale accessi'
segmented_stats.access_uniques: 'Accesso singolo'
segmented_stats.certificates: Diplomi
segmented_stats.total_certificates: 'Totale diplomi rilasciati'
library.createdAtView: 'Creato da {email} il {date} all''{time}'
library.no_text_provided: 'Nessun testo inserito'
library.maximum_allowed_size_exceeded: '%s: È stato superato il numero massimo di caratteri consentito.'
library.category.created: 'La categoria è stata creata con successo'
library.category.updated: 'La categoria è stata aggiornata con successo'
library.category.deleted: 'La categoria è stata eliminata con successo'
library.category.activated: 'La categoria è stata attivata con successo'
library.category.deactivated: 'La categoria è stata disattivata con successo'
library.library.updated: 'La libreria è stata aggiornata con successo'
library.library.created: 'La libreria è stata creata con successo'
library.library.deleted: 'La libreria è stata rimossa con successo'
library.library.name_required: 'Il nome è obbligatorio e deve essere inferiore a 100 caratteri.'
library.library.type_required: 'Il campo "tipo" è obbligatorio'
library.library.link_required: 'Se il tipo è ''LINK'', è necessario specificare un URL valido.'
forum.configureFields.thread: Hilo
forum.configureFields.message: Messaggio
forum.configureFields.comment: 'Comentario Informe'
forum.configureFields.title_modal_add: 'Aggiungi il Foro'
forum.configureFields.title_modal_edit: 'Modifica del forum'
course_press.label_in_singular: 'Corso in aula'
course_press.label_in_plural: 'Corsi in loco'
menu.courses_managment.course_sections: Sezioni
common.write: 'Scrivere qualcosa'
common_areas.accept: Accettare
common_results: risultati
games.answers: 'Aggiungi risposta'
games.text_common.order_ramdom: 'Ordinamento casuale'
games.puzzle.description_cropper: 'Quindi selezionare l''area dell''immagine che verrà utilizzata per creare il puzzle.'
games.validation_truefalse.question_or_image: 'È necessario digitare la domanda o selezionare un''immagine'
games.help.write_question: 'Scrivi la domanda'
games.help.write_word: 'Scrivi la parola'
games.help.write_title: 'Scrivere un titolo'
games.help.write_answer: 'Scrivi una risposta'
games.true: Verdadero
games.false: Falso
games.edit_video_quiz: 'Visualizzare e modificare i quiz video'
games.delete_video_quiz: 'Rimuovere il quiz video'
game.feedback.title: 'Attivare il feedback'
game.feedback.title_positive: 'In caso di successo'
game.feedback.title_negative: 'In caso di guasto (opzionale)'
announcements.common.group: Gruppo
announcements.common.action_denomination: 'Titolo Azione'
announcements.common.modality: Modalità
announcements.common.place_of_instruction: 'Luogo di consegna'
announcements.common.collaboration_type: 'Tipo di collaborazione'
announcements.common.provider: Fornitore
announcements.common.provider_cif: 'Numero di partita IVA Fornitore'
announcements.observations.costs: Costi
announcements.observations.course_status: 'Stato del corso'
announcements.observations.comunicado_fundae: 'Comunicato della FUNDAE'
announcements.observations.comunicado_abilitia: 'Annuncio a ABILITIA'
announcements.observations.economic_module: 'Modulo economico'
announcements.observations.travel_and_maintenance: 'Spostamento e manutenzione'
announcements.observations.provider_cost: 'Fornitore di costi'
announcements.observations.hedima_management_cost: 'Costo di gestione HEDIMA (10%)'
announcements.observations.travel_and_maintenance_cost: 'Spese di viaggio e di soggiorno'
announcements.observations.total_cost: 'Costo total'
announcements.observations.final_pax: 'Finale PAX'
announcements.observations.maximum_bonus: 'Bonus massimo (PAX finale)'
announcements.observations.subsidized_amount: 'Importazione bonus'
announcements.observations.private_amount: 'Importazione privata'
announcements.observations.provider_invoice_number: 'Numero di fattura Fornitore'
announcements.observations.hedima_management_invoice_number: 'Numero di fattura HEDIMA Management'
announcements.observations.invoice_status: 'Fattura di Stato'
announcements.observations.observations: Osservazioni
announcements.observations.observation: Osservazione
announcements.course.no_chapter: 'Questo corso non ha capitoli, perché è un corso in aula'
announcements.formativeActionTypes.intern: Internazionale
announcements.formativeActionTypes.extern: Esterno
announcements.formativeActionTypes.session_congress: Esterno
common_areas.confirm_file_upload: 'Siete sicuri di voler caricare i documenti?'
common_areas.confirm_file_delete: 'Sei sicuro di voler cancellare?'
course_section.label_in_singular: Sezione
course_section.label_in_plural: Sezioni
course_section.configureFields.name: Numero
course_section.configureFields.description: Descrizione
course_section.configureFields.active: Activa
course_section.configureFields.sort: Ordine
course_section.configureFields.translations: Traduzioni
course_section.configureFields.section_name: 'Nome della sezione'
course_section.configureFields.categories: Categorie
user.roles.administrator: Amministratore
user.roles.user: Utente
user.roles.tutor: Tutor
user.roles.subsidizer: Ispettore
user.roles.manager: Direttore
user.roles.manager_editor: 'Manager - Editore'
user.roles.team_manager: 'Responsabile di squadra'
survey.label_in_plural: Sondaggi
course.configureFields.is_main: 'Questo corso utilizzerà solo le proprie domande per la valutazione'
global.error: 'Si è verificato un errore. Riprovare più tardi'
quiz.configureFields.title_creation: 'Creazione di domande'
quiz.configureFields.question: 'Enunciazione della domanda'
quiz.configureFields.question_placeholder: 'Scrivere la domanda'
quiz.configureFields.question_delete: 'volete davvero eliminare questa domanda?'
rouletteWord.configureFields.statement: Dichiarazione
rouletteWord.configureFields.answer: Risposta
rouletteWord.configureFields.type_0: 'Iniziare con la lettera'
rouletteWord.configureFields.type_1: 'Contiene la lettera'
rouletteWord.configureFields.error.statement.max: 'La dichiarazione non può superare i ${max} caratteri.'
rouletteWord.configureFields.error.statement.empty: 'La dichiarazione non può essere vuota'
rouletteWord.configureFields.error.answer.max: 'La risposta non può superare i {max} caratteri'
rouletteWord.configureFields.error.answer.empty: 'La risposta non può essere vuota'
rouletteWord.configureFields.error.answer.starts: 'La risposta deve iniziare con la lettera'
rouletteWord.configureFields.error.answer.includes: 'La risposta deve contenere la lettera'
rouletteWord.response.update_letter: 'I dati sono stati aggiornati correttamente'
rouletteWord.response.delete_letter: 'I dati sono stati cancellati con successo'
trueorFalse.configureFields.true: Corretto
trueorFalse.configureFields.false: Falso
enigma.configureFields.title_creation: 'Creazione di enigmi'
puzzle.configureFields.save_image: 'Immagine salvata con successo'
puzzle.configureFields.select_correct_answer: 'È necessario selezionare una sola risposta corretta'
puzzle.configureFields.recomendation: Raccomandazione
puzzle.configureFields.recomendation_dimentions: '<p>Si consigliano le dimensioni <span class="text-primary"><b>minimo 1024 pixel per lato</b></span> y <span class="text-primary"><b>massimo di 2000 pixel per lato</b></span></p>'
puzzle.configureFields.recomendation_description: '<p>Il formato del puzzle è <span class="text-primary"><b>quadrato</b></span>,quindi, se si seleziona un''immagine con un rapporto di aspetto diverso, ad esempio un''immagine di paesaggio, lo strumento consente di ritagliare selezionando l''area desiderata.</p>'
hiddenword.configureFields.title: 'Creazione di una parola nascosta'
hiddenword.configureFields.answers_title: 'Parola nascosta'
hiddenword.configureFields.answers_placeholder: 'Scrivere la parola nascosta'
categorize.configureFields.title_group: 'Gruppi o famiglie'
fillgaps.configureFields.title: 'Creazione di frasi'
fillgaps.configureFields.fillgap: Cava
fillgaps.configureFields.fillgaps: Fori
fillgaps.configureFields.type_list: Lista
fillgaps.configureFields.type_drag: Trascinamento
guesword.configureFields.word_title: 'Parola non ordinata'
guesword.configureFields.word_title_placeholder: 'Scrivere una parola che apparirà non ordinata'
guesword.configureFields.solution: Soluzione
guesword.configureFields.solution_placeholder: 'Scrivere la soluzione del gioco'
guesword.configureFields.help_placeholder: 'Scrivere la guida del gioco'
pairs.configureFields.title: 'Creazione del gioco'
pairs.configureFields.placeholder_title: 'Scrivere la dichiarazione'
pairs.configureFields.create_game: 'Creazione del gioco'
chapter_type.description.22: '<p>La soluzione ideale per creare contenuti dinamici e attraenti nei vostri corsi o pillole formative, presentando le informazioni in modo visivo e con un''ampia varietà di interazioni basate su testo, immagini, video, audio, link multimediali, schede interattive, scene collegate, ecc.</p>'
chapter_type.add.22: 'Creare contenuti interattivi'
games.videoquiz_exist_question: 'C''è già una domanda creata nella stessa fascia oraria.'
chapter_type.22: VCMS
video.configureFields.title: 'Creazione di video quiz'
video.configureFields.add_question: 'Aggiungi domanda'
Next: Avanti
hours: Ore
minutes: Minuti
seconds: Secondi
field_required: 'Campo richiesto'
field_invalid: 'Campo non valido'
field_invalid_format: 'Formato non valido'
remaining_characters: 'Caratteri rimanenti'
minimiun_characters: 'Caratteri minimi'
menu.home.title: 'Vai all''area utente'
course.season.type.sequential: 'Navigazione sequenziale'
course.season.type.free: 'Navigazione libera'
course.season.type.exam: 'Modalità esame'
games.fillgap.add_fillgap: 'Aggiungere spazi vuoti cliccando su determinate parole'
course_section.configureFields.hideCategoryName: 'Nascondere il nome della categoria'
user.roles.super_administrator: Superamministratore
settings.menu.label: Configurazione
settings.header.title: Configurazione
setting.menu.general: Generale
setting.menu.catalog: Cataloghi
share: Condividi
report.announcement.participants: Partecipanti
report.announcement.groupCode: 'Codice gruppo'
report.announcement.enterpriseProfile: 'Profilo aziendale'
report.announcement.file: File
report.announcement.totalStudents: 'Totale studenti'
report.announcement.enterpriseCIF: 'Numero di partita IVA dell''azienda'
report.announcement.advisor: Tutor
course.stats.started: Avviato
course.stats.ended: Completato
course.stats.total_time: 'Tempo totale'
course.stats.avg_time: 'Tempo medio'
course.stats.minutes: minuti
course.stats.minute: minuto
course.stats.hours: ore
course.stats.hour: tempo
course.stats.second: secondo
course.stats.seconds: secondi
question.configureFields.quantity_max_question: 'Numero massimo di domande da visualizzare:'
user.configureFields.available_courses: 'Corsi disponibili'
user.configureFields.available_chapter: 'Capitolo disponibile'
user.configureFields.courses_stats.finished: completato
user.configureFields.courses_stats.started: iniziato
user.configureFields.courses_stats.available: disponibile
user.configureFields.courses_stats.sent_messages: 'inviato a'
user.configureFields.courses_stats.received_messages: ricevuto
user.configureFields.courses_stats.others: Altro
user.configureFields.permissions: Permessi
course.configureFields.segments: Segmenti
stats.roles: Ruoli
course.configureFields.language: Lingua
game.feedback.wrong: 'Esempio: Wow!'
chapter_type.description.23: '<p>Il gioco di ruolo è un''attività in cui i partecipanti assumono e agiscono come personaggi di fantasia, spesso all''interno di un''ambientazione o di un contesto specifico. Durante il gioco di ruolo, i partecipanti adottano temporaneamente la personalità, le caratteristiche e i comportamenti dei personaggi che rappresentano, interagendo tra loro in base alle circostanze e all''ambiente immaginario stabilito. Questa pratica viene utilizzata in una varietà di contesti, come giochi, terapie, simulazioni educative e attività ricreative, con lo scopo di promuovere la creatività, l''empatia, la risoluzione di problemi e l''esplorazione di situazioni ipotetiche.</p>'
password.uppercase: 'Richiesto 1 o più caratteri in maiuscolo'
password.number: 'Sono richieste 1 o più cifre numeriche'
password.minimum: 'La password deve essere composta da almeno %s caratteri.'
password.disable_3_consecutive_chars: 'Non è consentito ripetere un carattere per più di 3 volte di seguito.'
password.lowercase: 'Sono richiesti 1 o più caratteri minuscoli'
chapter_type.23: 'Gioco di ruolo'
chapter_type.add.23: 'Creare un gioco di ruolo'
password.special_characters: '1 o più caratteri speciali richiesti'
roleplay.status.failure: 'Avete fallito'
roleplay.status.success: 'Avete superato'
user.configureFields.locale: Lingua
course.created: 'Il corso è stato salvato con successo'
question.configureFields.do_all_questions: 'Usa tutte le domande'
announcements.news.start_announcement: 'Il corso %course% è dietro l''angolo!'
announcements.news.finish_announcement: 'Il corso %course% è quasi finito!'
user.configureFields.dni: DNI
course_section.configureFields.section_aditional: 'Formazione aggiuntiva'
report.announcement.time_conexion: 'Tempo di connessione'
report.announcement.init_finish: 'Inizio e fine'
report.annnouncement.conexions: Connessioni
report.annnouncement.chat_tutor: 'Conversazione con il tutor'
report.annnouncement.first_conexion: 'Prima connessione'
report.annnouncement.last_conexion: 'Ultima connessione'
generic_token.assistance.success: 'La sua presenza è stata registrata con successo'
generic_token.assistance.user_not_in_group: 'L''utente non appartiene al gruppo di sessione'
chat.notification.number_of_messages: 'Hai %s messaggi non letti'
certificate.notification.available: 'Il diploma di convocazione del corso %s è disponibile per il download'
user_fields_fundae.title: 'Campi aggiuntivi FUNDAE'
user_fields_fundae.social_security_number: 'Numero di previdenza sociale'
user_fields_fundae.gender: Genere
user_fields_fundae.email_work: 'Posta di lavoro'
user_fields_fundae.birthdate: 'Data di nascita'
user_fields_fundae.dni: DNI
user_fields_fundae.contribution_account: 'Conto contributivo'
user_fields_fundae.incapacity: Incapacità
user_fields_fundae.victim_of_terrorism: 'Vittima del terrorismo'
user_fields_fundae.gender_violence: 'Vittima di violenza di genere'
fundae_assistance_template.main_title: 'CONTROLLO DELLE PRESENZE ALLA FORMAZIONE'
fundae_assistance_template.action_type: 'NOME DELL''AZIONE FORMATIVA'
fundae_assistance_template.action_code: 'CODICE DI AZIONE'
fundae_assistance_template.group: GRUPPO
fundae_assistance_template.start_at: 'DATA DI INIZIO'
fundae_assistance_template.finish_at: 'DATA FINE'
fundae_assistance_template.main_formation_teacher: 'FORMATORE/RESPONSABILE DELLA FORMAZIONE'
fundae_assistance_template.session_number: 'SESSIONE N.'
fundae_assistance_template.date: DATA
fundae_assistance_template.morning_afternoon: DOMANI/POMERIGGIO
fundae_assistance_template.signed: Firmato
fundae_assistance_template.info_signed_person: 'Formatore/Rappresentante di formazione.'
fundae_assistance_template.assistance_data: 'Dati dei partecipanti'
fundae_assistance_template.signatures: FIRME
fundae_assistance_template.observations: RIFERIMENTI
fundae_catalogs.main_page.title: 'Cataloghi FUNDAE'
fundae_catalogs.user_company.label_in_plural: Aziende
fundae_catalogs.user_company.label_in_singular: Azienda
fundae_catalogs.user_professional_category.label_in_plural: 'Categorie professionali'
fundae_catalogs.user_professional_category.label_in_singular: 'Categoria professionale'
fundae_catalogs.user_study_level.label_in_plural: 'Livelli di studio'
fundae_catalogs.user_study_level.label_in_singular: 'Livello di studio'
fundae_catalogs.user_work_center.label_in_plural: 'Luoghi di lavoro'
fundae_catalogs.user_work_center.label_in_singular: 'Posto di lavoro'
fundae_catalogs.user_work_department.label_in_plural: 'Reparti di lavoro'
fundae_catalogs.user_work_department.label_in_singular: 'Dipartimento del Lavoro'
fundae_catalogs.fields.state.title: Stato
fundae_catalogs.fields.state.active: Attivo
fundae_catalogs.fields.state.inactive: Inattivo
excel.userAnnouncement.sheet1.title: 'Informazioni generali'
excel.userAnnouncement.sheet1.colum1: 'Numero di itinerari'
excel.userAnnouncement.sheet1.colum2: 'Numero di utenti che hanno itinerari'
excel.userAnnouncement.sheet1.colum3: 'Numero di corsi nei percorsi'
excel.userAnnouncement.sheet2.title: 'Itinerari del catalogo'
excel.userAnnouncement.sheet2.colum1: Id
excel.userAnnouncement.sheet2.colum2: 'Itinerari dei nomi'
excel.userAnnouncement.sheet2.colum3: Divisione
excel.userAnnouncement.sheet2.colum4: Categoria
excel.userAnnouncement.sheet2.colum5: 'Corsi assegnati'
excel.userAnnouncement.sheet2.colum6: 'Persone assegnate'
excel.userAnnouncement.sheet2.colum7: 'Le persone hanno completato l''itinerario'
excel.userAnnouncement.sheet2.colum8: 'Persone nel processo'
excel.userAnnouncement.sheet2.colum9: 'Non partenti'
excel.userAnnouncement.sheet2.colum10: 'TEMPO TOTALE ACCUMULATO'
excel.userAnnouncement.sheet2.colum11: 'TEMPO MEDIO DELLA PERSONA'
excel.userAnnouncement.sheet3.title: 'Itinerario Corsi'
excel.userAnnouncement.sheet3.colum1: Id
excel.userAnnouncement.sheet3.colum2: 'Itinerari dei nomi'
excel.userAnnouncement.sheet3.colum3: 'Nome Corso'
excel.userAnnouncement.sheet3.colum4: Completato
excel.userAnnouncement.sheet3.colum5: 'In corso'
excel.userAnnouncement.sheet3.colum6: 'Nessun inizio'
course.message_saved: 'Corso salvato'
chapter.configureFields.create_chapter: 'Creare un capitolo'
user_filter.assign_manual: 'Assegnare manualmente'
user_filter.assign_filters: 'Assegnazione per filtri'
user.configureFields.configureLocale: 'Impostazioni della lingua'
user.configureFields.configureLocaleAdmin: 'Configurare la lingua del pannello di amministrazione'
user.configureFields.configureLocaleCampus: 'Impostare la lingua del campus'
stats.export.configsheet.title: Configurazione
stats.export.configsheet.content_title: 'Rapporto sulle statistiche generali'
stats.export.configsheet.content_period: 'Periodo coperto (data di fine)'
stats.export.configsheet.content_filters: 'Filtri attivi'
stats.export.configsheet.content_period_from: Da
stats.export.configsheet.content_period_to: A
stats.export.datasheet.title: Dati
stats.export.filter.category: Categoria
stats.export.filter.departament: Dipartimento
stats.export.filter.gender: Genere
stats.export.filter.activeUsers: Utenti
stats.export.filter.activeUsers_val_yes: Attività
stats.export.filter.activeUsers_val_no: Inattivo
stats.export.filter.course_full_title: '100% corso'
stats.export.filter.course_full_val_yes: Sì
stats.export.filter.course_full_val_no: 'No'
stats.export.filter.course_full_descr: '(Includere solo i corsi che sono stati completati nel periodo indicato)'
stats.export.filter.course_intime_title: 'Durata del corso nel periodo'
stats.export.filter.course_intime_val_yes: Sì
stats.export.filter.course_intime_val_no: 'No'
stats.export.filter.course_intime_descr: '(Includere solo i corsi iniziati e terminati nel periodo indicato)'
stats.export.filter.course_started_in_period_title: 'Corso iniziato nell''intervallo di date'
stats.export.filter.course_started_in_period_val_yes: Sì
stats.export.filter.course_started_in_period_val_no: 'No'
stats.export.filter.course_finished_in_period_title: 'Corso completato nell''intervallo di date'
stats.export.filter.course_finished_in_period_val_yes: Sì
stats.export.filter.course_finished_in_period_val_no: 'No'
stats.export.filter.customFilters: Personalizzato
stats.content_allusers: 'Tutti gli utenti'
stats.content_inactive: Inattivo
stats.content_inactive_f: Inattivo
itinerary.user.assign_manual: 'Assegnare manualmente'
itinerary.user.assign_filter: ' Assegnazione per filtri'
itinerary.user.modify_users: 'Assegnare le persone manualmente'
itinerary.user.filter_find_by: 'Ricerca per'
common_areas.close: Chiudere
itinerary.chart.avg_time_active: '(Totale attivo)'
itinerary.chart.avg_time_all: '(Totale assegnato)'
itinerary.courses.modify: 'Assegnare corsi'
itinerary.courses.appliedfilter: ' corso/i visualizzato/i (filtrato/i da'
itinerary.users.appliedfilter: ' persona/e mostrata/e (filtrata/e da'
itinerary.courses.available: 'Corsi disponibili'
excel.userAnnouncement.sheet1.colum2b: 'Numero di utenti unici che dispongono di itinerari'
excel.userAnnouncement.sheet1.colum3b: 'Numero di corsi unici nei percorsi'
common_areas.select_choice: 'Selezionare un''opzione'
chapter_type.24: LTI
chapter_type.description.24: LTI
lti_chapter.title: 'Capitolo LTI'
lti_chapter.add: 'Aggiungere il capitolo LTI'
lti_chapter.edit: 'Modifica del capitolo LTI'
lti_chapter.identifier: 'Identificatore LTI'
lti_chapter.identifier_required: 'Identificatore LTI richiesto'
chapter_type.add.24: 'Aggiungere il capitolo LTI'
categoryFilter.label: 'Filtri di categoria'
categoryFilter.title: 'Filtrare le categorie'
user.configureFields.localeCampus: 'Lingua del campus'
global.bulk.sheetValidation.error_tab_1: 'La prima scheda non si chiama "Elenco delle formazioni".'
global.bulk.sheetValidation.error_tab_2: 'La seconda scheda non si chiama "Partecipanti".'
stats.export.user_creation: 'Filtrare gli utenti per data di creazione'
stats.export.users_export_title: 'Statistiche degli utenti'
chapter_type.validation_course: "\nIl capitolo non può essere cancellato perché alcuni utenti vi hanno già registrato delle attività."
user.configureFields.courses_stats.notstarted: 'non avviato'
course.configureFields.created_at: 'Data di creazione'
course.configureFields.translate: Tradurre
messages.configureFields.timezone: 'Fuseau horaire'
user.email: 'Indirizzo e-mail'
course.diploma.index: 'Indice dei contenuti'
menu.stats.reports.diplomas: 'Rapporti e diplomi'
itinerary.succes.download: 'Il rapporto sul percorso è in fase di elaborazione e si trova alla voce "Rapporti e diplomi"'
user.diploma.generate: 'Generare diplomi'
filters.placeholder: 'Ricerca per tipo'
filters.remove_all: 'Rimuovi tutto'
filters.add_all: 'Aggiungi tutti'
announcement.report_group_resume_individual: 'Sintesi individuale'
announcement.report_downloaded_diploma: 'Diploma scaricato'
announcements.configureFields.code: 'Nome della chiamata'
task.status.review: 'In fase di revisione'
task.status.error: 'Errore di sistema'
email.error.subject: 'Errore in %contesto% (ID: %id%) - Ambiente: %appName% - Errore in %contesto% (ID: %id%) - Ambiente: %appName%'
email.error.subject_no_id: 'Errore in %context% - Ambiente: %appName%'
email.error.title: 'Errore nell''esecuzione dell''attività'
email.error.environment: Ambiente
email.error.context: Contesto
email.error.task_id: 'ID compito'
email.error.error_details: 'Dettagli dell''errore'
email.error.error_message: 'Messaggio di errore'
email.error.error_line: Linea
email.error.error_file: Archivio
email.error.additional_info: 'Informazioni aggiuntive'
email.error.regards: 'Cordiali saluti'
email.error.team: 'Il team %appName%'
email.zombie.subject: 'Attività in stato ZOMBIE in %context% (ID: %id%) - Ambiente: %appName% (ID: %id%) - Ambiente: %appName% (ID: %id%)'
email.zombie.title: 'Notifica dell''attività in stato ZOMBIE'
email.zombie.environment: Ambiente
email.zombie.context: Contesto
email.zombie.task_id: 'ID compito'
email.zombie.marked_as: 'è stato contrassegnato come'
email.zombie.zombie_status: ZOMBIE
email.zombie.timeout_reason: 'perché ha superato il tempo di esecuzione consentito'
email.zombie.check_details: 'Controllare il pannello di amministrazione o la console per maggiori dettagli e prendere le misure appropriate'
email.zombie.additional_info: 'Informazioni aggiuntive'
email.zombie.regards: 'Cordiali saluti'
email.zombie.team: 'Il team %appName%'
season.delete: 'Non è possibile cancellare questa stagione, che attualmente ha episodi collegati'
delete.season.chapters.users: "Impossibile eliminare la stagione %seasonName%,\n                     i capitoli (%CapitoliTitoli%) hanno attività da parte dell'utente"
delete.season.danger: 'La stagione non può essere eliminata'
stats.task.queued: 'Richiesta di attività incollata'
itinerary.delete.confirm.validation: 'Attualmente l''itinerario è attivo, per procedere con l''azione è necessario disabilitare l''itinerario'
itinerary.delete.confirm.title: 'volete davvero cancellare l''itinerario?'
course.publish.message.active: 'Corso pubblicato'
course.publish.message.unactive: 'Corso contrassegnato come non pubblicato'
itinerary.delete.error: 'L''itinerario non può essere cancellato'
courser.chaperts.orders.succes: 'Ordine del capitolo aggiornato con successo'
course.publish.message.unactive.chapters: 'Non può essere pubblicato perché il corso è incompleto'
course.undelete.message: 'Il corso non può essere cancellato perché ha dei contenuti assegnati'
user.roles.creator: Creatore
