message_api.content.scorm_correctly: 'Scorm behar bezala hasita'
message_api.content.token_not_send: 'Token-a ez da bidali '
message_api.content.error_get_content: 'Errorea gertatu da kapitulua hasten saiatzean:'
message_api.content.scorm_user_not_access: "Erabiltzaileak ez du ikastaro honetarako sarbiderik\n"
message_api.content.scorm_not_content: "Kapitulua ez da eduki-sorta bat\n"
message_api.base.credentials_no_correct: "Sartutako kredentzialak ez dira zuzenak.\n"
message_api.base.authorized_access: 'Sarbidea ukatuta'
message_api.base.user_successfully: "Erabiltzaileak saioa ongi itxi du\n"
message_api.base.error_login: 'Errorea gertatu da erabiltzailearen saioa hasten saiatzean - Errorea:'
message_api.base.authorized_token_fail: 'Ezin izan da SSOren bidez sartu. Erabiltzailea oraindik plataforman erregistratu eta aktibatu ez bada, SSO motako sarbidea gaitu ahal izateko zure erabiltzailea erregistratu eta aktibatu beharko duzu.'
message_api.base.user_not_exists_in_ad: 'Kredentzial okerrak edo erabiltzailea ez dago direktorio aktiboan.'
message_api.controller.error_get_course: 'Errorea gertatu da erabiltzailea erregistratzen saiatzean - Errorea:'
message_api.controller.chapter_init: "Kapitulua behar bezala hasi da\n"
message_api.controller.error_chapter_init: 'Errorea gertatu da kapitulua hasten saiatzean:'
message_api.controller.error_chapter_update: "Kapitulua behar bezala eguneratu da\n"
message_api.controller.help_text: "Laguntza. Testuak ondo berreskuratzen dira\n"
message_api.controller.course_not_found: "Ez da ikastarorik aurkitu\n"
message_api.controller.user_not_access: "Erabiltzaileak ez du ikastaro honetarako sarbiderik\n"
message_api.controller.evaluation_course: 'Ebaluazioa bidalita'
message_api.controller.training_itinerary: 'Zure prestakuntza-ibilbidea'
message_api.controller.continue_training: "Jarraitu prestatzen\n"
message_api.message.not_found: "Mezua ez da aurkitu\n"
message_api.message.not_access: 'Sarbidea ukatuta'
message_api.scorm.init: 'Scorm behar bezala hasita'
message_api.scorm.not_package: "Kapitulua ez da SCORM pakete bat\n"
message_api.scorm.button_add_scorm: 'Gehitu scorm paketea'
message_api.scorm.button_delete_scorm: 'Scorm paketea ezabatu'
message_api.scorm.title_package: "Scorm paketea\n"
message_api.scorm.upload_file_scorm: '.zip artxiboa hautatu'
message_api.scorm.info_upload_scorm: "Scorm paketea kargatu behar duzu\n"
message_api.notification.not_found: "Jakinarazpena ez da aurkitu\n"
message_api.player.no_content: "Kapitulu honek ez du edukirik\n"
message_api.alert.minimal_question: "Joko honek gutxienez %number% galdera ditu\n"
message_api.alert.image_puzzle: 'Puzzleak irudi bat izan behar du'
message_api.alert.question_correct: 'Galdera horri erantzun zuzen bat gehitu behar diozu'
message_api.alert.chapter_content: 'Kapitulu honi edukia behar zaio'
message_api.alert.pdf: 'Kapitulu honetan pdf bat behar da'
message_api.alert.video: 'Kapitulu honi edukia behar zaio'
message_api.alert.slider: 'Kapitulu honek gutxienez %number% irudi ditu'
message_api.alert.scorm: 'Kapitulu honetan scorm paketea behar da'
message_api.alert.question_hidden: "Galdera horrek erantzun bakarra izan behar du, eta zuzena izan behar du\n"
message_api.alert.minimal_pair: 'Kapitulu honetan gutxienez %number% bikote behar ditu.'
message_api.announcement.call_for_aplications: "Deialdia\n"
message_api.alert.course_content_incomplete: "Kapituluetan informazioa falta da; mesedez, berrikusi eta gehitu edukia, deialdia sortzen jarraitu ahal izateko.\n"
message_api.diploma.supered: "Gaindituta izateagatik\n"
message_api.diploma.granted: "Nori emana:\n"
message_api.diploma.date: Data
message_api.diploma.diploma: Diploma
message_api.diploma.trato: Jn./And.
message_api.diploma.nif: "IFZrekin\n"
message_api.diploma.services: 'Enpresan zerbitzuak ematen dituena'
message_api.diploma.cif: "IFKrekin\n"
message_api.diploma.evaluation: 'Ebaluazio positiboarekin gainditu duzu Prestakuntza Ekintza'
message_api.diploma.code: "AF/TALDE kodea\n"
message_api.diploma.between: "Egunetan\n"
message_api.diploma.duration: "Guztizko iraupena:\n"
message_api.diploma.fomartionType: "prestakuntza modalitateko orduak\n"
message_api.diploma.courseContent: "Ikastaroaren edukiak hurrengo orrialdean\n"
message_api.diploma.signatureSeal: "Trebakuntza emateko ardura duen erakundearen<br> sinadura eta zigilua\n"
message_api.diploma.signatureDate: 'Emate data'
message_api.diploma.acreditation: "Egiaztagiria\n"
message_api.diploma.signature: "Parte-hartzailearen sinadura\n"
message.api.diploma.type_course_default: Teleprestakuntza
message.api.diploma.dates: 'Data: %dateFrom%-tik %dateTo%-ra'
message.api.diploma.hours: '{0} %count% orduak |{1} %count% ordua|]1,Inf] %count% orduak'
message.api.diploma.duration: Iraupena
message.api.diploma.with_dni: '%nameIdentification% %dni%-rekin'
message_api.announcement.cancelation_announcement: '%course% deialdia bertan behera utzita'
