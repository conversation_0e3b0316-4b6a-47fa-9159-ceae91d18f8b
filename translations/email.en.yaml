fields.division: Division
fields.collective: Collective
fields.country: Country
fields.center: Centre
country.4: Spain
country.12: 'Dominican Republic'
country.18: Hungary
country.22: 'United States of America'
country.26: Brazil
country.32: 'Cape Verde'
country.42: Jamaica
country.72: Montenegro
country.87: Cuba
country.92: Portugal
country.151: Morocco
country.289: Tunisia
country.308: Italy
country.355: Mexico
country.369: Peru
country.376: Aruba
email.subject: 'Iberostar Campus: Incident in the register'
email.description: 'Incident report on registration to Iberostar Campus'
email.name: Name
email.email: 'E-mail address'
email.key: 'ID number, DNI or Passport number'
email.division: Division
email.collective: Collective
email.country: Country
email.center: Centre
email.text: 'Description of occurrence'
email.sent: '<p>Your support request has been sent successfully.</p>'
email.messages_to_user.register: '<p>Congratulations!</p><p>Your user has been successfully registered and an email has been sent so that you can activate your account.</p><p>If you do not find it in your inbox, please check your spam folder.</p>'
email.messages_to_user.registre_free: '<p>Congratulations! </p><p>Your registration request has been sent, check your email for activation instructions</p>'
email.messages_to_user.activated: 'This user is already activated'
email.messages_to_user.does_not_exist: 'Does not exist in database'
email.messages_to_user.error_register: 'An error occurred while registering the user:'
email.messages_to_user.error_reset: 'An error occurred while sending the information:'
email.messages_to_user.key_not_found: 'The key does not exist in the database'
email.messages_to_user.email_not_found: 'Email does not exist in database'
email.messages_to_user.send_email: 'An email has been sent to your account'
email.messages_to_user.email_not_send: 'Could not send the email, the account is deactivated.'
email.messages_to_user.not_user_email: 'There is no user with this email'
email.messages_to_user.change_pasword: 'Your password has been changed'
email.messages_to_user.password_does_not_match: 'Current password does not match'
email.messages_to_user.error_change_password: 'An error occurred while changing the password:'
email.messages_to_user.change_avatar: 'The avatar was changed'
email.messages_to_user.error_avatar: 'An error occurred while changing the avatar:'
email.messages_to_user.modified_information: 'Your data has been modified'
email.messages_to_user.error_change_information: 'An error occurred while changing the information:'
email.messages_to_user.exist_email: 'The %email% account is in the process of validation'
email.template_email.greet: Hello
email.template_email.email_register: 'You have created your user with this email:'
email.template_email.message_active_account: 'To complete the registration you must open this link'
email.template_email.active_account: 'Activate user'
email.template_email.password_request: 'You have requested to generate a new password, click on the following button to access the form.'
email.template_email.change-password: 'Change password'
email.template_email.recover_password: 'Password recovery'
email.template_email.incident: '<p>The user <strong> %name% </strong> has sent you the following notification: </p>'
email.template_email.email_contact: '<p>Contact email: <strong> %email% </strong> <p>'
email.template_email.consultation: 'Your inquiry has been sent'
email.template_email.valued_user: Dear
email.template_email.content_register_user: '<p class="content">We have received your registration request. Once we verify <br>the submitted data we will send you an email with instructions <br>to complete the activation of the account</p>'
email.template_email.best_regards: 'Best regards'
email.template_email.your_registration_request: 'Your registration request has been received'
email.template_email.content_register_admin: '<p class="content">The user <b>%user%</b>, has sent you a request <br>to activate the account on the <strong> %platform% </strong>, <br>please check the <a href="%url%%route%">administration panel </a> to approve the subscription.</p>'
email.template_email.your_registration_admin_request: 'Application for registration'
email.template_email.approved_application: '<p class="content">Congratulations! Your application has been accepted. <br>You can now activate your account by clicking on the button below</p>'
email.template_email.content_announcement: '<p>We would like to inform you that you have been invited to the course <strong> %course% </strong> <br><br> <strong>Start date:</strong> %dateStart% <br><br> <strong>End date:</strong> %dateFinish% <br><br> Please click here to access <a href="%url%%route%"> %course% </a> and continue with your learning process</p>'
email.active_account.hello_new: '<p class="text-center">Hello again, %name% <br>  Click on the <b>Activate user</b> <br> button and it will be activated.</p>'
email.active_account.page_nof_found: '404 Page not found'
email.reset_password.write_password: 'Enter your new password'
email.template_email.content_announcement_fundae: '<p>My name is <strong> %nameTutor%</strong> and I will lead the online training tutorial:<strong> %announcement%</strong> <br><br><br><strong>Start Date:</strong> %dateStart% <br><br><br><strong> <strong>End Date:</strong> %dateFinish% <br><br><br>The course will be delivered through the platform: <a href="%url%%%route%"> %platform% </a> <br><br><br>You will be able to login using your email address and password to access the platform. <br><br>To access the course directly click<a href="%url%%%linkCourse%"> here</a> </p> <p>'
email.template_email.active_account_fundae: '<p>If this is your first time accessing the platform, to activate your password click on this <a href="%url%%%path%"> link</a> and follow the steps.</p>'
email.template_email.content_announcement_fundae_press: '<p>My name is <strong> %nameTutor%</strong> and I will lead the training tutorial:<strong> %announcement%</strong> <br><br><br><strong>Start Date:</strong> %dateStart%<br><br><br><strong>End Date:</strong> %dateFinish%<br><br><br><strong> <strong>Duration:</strong> %duration% hours<br><br><br> <strong>Location:</strong> %place%<br><br></p>'
email.template_email.valoration_announcement_subject: 'Course evaluation %course%'
email.template_email.valoration_announcement_body: '<p>We are contacting you to request your feedback on the <strong> %course%</strong> course you have recently completed. Your feedback is very important to us and will help us improve our courses. <br><br> Please take a few minutes to complete our rating survey. The link can be found here: <a href="%url%%%route%"> %platform% </a> <br><br> Thank you for your cooperation! <br><br> Sincerely, %nameTutor%</p>'
email.template_email.confirmation_assistance: 'excellent! Your participation in the call has been successfully registered.'
email.template_email.no_assistance: 'notification! We have received information that you will not be participating in the call.'
email.template_email.close: Exit
email.template_email.go_campus: 'Enter the platform'
email.template_email.question_assitance: 'Please confirm your attendance'
email.template_email.question_assitance_yes: 'Yes'
email.template_email.question_assitance_no: 'No'
email.template_email.cancelation_event: '<p>Dear <strong>%name%</strong>,</p><p>We are writing to you regarding the <strong>%course%</strong>, scheduled to run from <strong>%dateStart%</strong> to <strong>%dateFinish%</strong>.</p><p>We understand that this may cause inconvenience and would like to express our apologies for any inconvenience this may cause.</p><p>We are committed to providing you with quality educational experiences, and are evaluating alternative options for rescheduling the event in the future or providing relevant content in other ways.</p><p>We appreciate your understanding and patience as we work to manage this situation.<br><br>We appreciate your interest in participating in our training events, and look forward to having you on future occasions</p><br>'
email.active_account.hello_activate_user: '<p class="text-center"><b>You have activaded your user</b><br>Congratulations %name%, you can log in now!</p>'
email.active_account.ok: Ok
