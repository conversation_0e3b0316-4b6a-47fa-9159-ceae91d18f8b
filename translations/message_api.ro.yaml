message_api.content.scorm_correctly: 'Scorm a pornit corect'
message_api.content.token_not_send: 'Tokenul nu a fost trimis'
message_api.content.error_get_content: 'A apărut o eroare în timpul încercării de a începe capitolul:'
message_api.content.scorm_user_not_access: 'Utilizatorul nu are acces la acest curs'
message_api.content.scorm_not_content: 'Capitolul nu este un pachet de conținut'
message_api.base.credentials_no_correct: 'Datele de identificare introduse nu sunt corecte.'
message_api.base.authorized_access: 'Acces neautorizat'
message_api.base.user_successfully: 'Utilizatorul s-a deconectat cu succes'
message_api.base.error_login: 'A apărut o eroare în timpul încercării de logare a utilizatorului - Eroare:'
message_api.base.authorized_token_fail: 'Nu ați reușit să accesați prin SSO. Dacă nu v-ați înregistrat și activat încă utilizatorul pe platformă, trebuie să faceți acest lucru pentru a activa acest tip de acces SSO.'
message_api.base.user_not_exists_in_ad: 'Acreditive incorecte sau utilizatorul nu există în Active Directory.'
message_api.controller.error_get_course: 'A apărut o eroare în timpul încercării de înregistrare a utilizatorului - Eroare:'
message_api.controller.chapter_init: 'Capitolul a început în mod corespunzător'
message_api.controller.error_chapter_init: 'A apărut o eroare în timpul încercării de a începe capitolul:'
message_api.controller.error_chapter_update: 'Capitolul a fost actualizat corect'
message_api.controller.help_text: 'Textele de ajutor sunt recuperate cu succes'
message_api.controller.course_not_found: 'Nu s-au găsit cursuri'
message_api.controller.user_not_access: 'Utilizatorul nu are acces la acest curs'
message_api.controller.evaluation_course: 'Evaluare depusă'
message_api.controller.training_itinerary: 'Traseul dumneavoastră de formare'
message_api.controller.continue_training: 'Formare continuă'
message_api.message.not_found: 'Mesajul nu a fost găsit'
message_api.message.not_access: 'Accesul nu este permis'
message_api.scorm.init: 'Scorm a pornit corect'
message_api.scorm.not_package: 'Capitolul nu este un pachet SCORM'
message_api.scorm.button_add_scorm: 'Adăugați pachetul scorm'
message_api.scorm.button_delete_scorm: 'Eliminați pachetul scorm'
message_api.scorm.title_package: 'Pachetul Scorm'
message_api.scorm.upload_file_scorm: 'Selectați fișierul .zip'
message_api.scorm.info_upload_scorm: 'Trebuie să încărcați pachetul scorm'
message_api.notification.not_found: 'Notificarea nu a fost găsită'
message_api.player.no_content: 'Acest capitol nu are conținut'
message_api.alert.minimal_question: 'Acest joc necesită un minim de întrebări de %number%.'
message_api.alert.image_puzzle: 'Puzzle-ul trebuie să aibă o imagine'
message_api.alert.question_correct: 'La această întrebare trebuie să adăugați un răspuns corect'
message_api.alert.chapter_content: 'Acest capitol este lipsit de conținut'
message_api.alert.pdf: 'Acest capitol are nevoie de un pdf'
message_api.alert.video: 'Acest capitol este lipsit de conținut'
message_api.alert.slider: 'Acest capitol are un număr minim de %number% de imagini.'
message_api.alert.scorm: 'Acest capitol are nevoie de un pachet scorm'
message_api.alert.question_hidden: 'Această întrebare trebuie să conțină un singur răspuns și acesta trebuie să fie corect.'
message_api.alert.minimal_pair: 'Acest capitol necesită un minim de perechi %number%.'
message_api.announcement.call_for_aplications: 'Cerere de candidaturi'
message_api.alert.course_content_incomplete: 'Există informații lipsă în capitole, vă rugăm să revizuiți și să adăugați conținut pentru a continua crearea cererii de propuneri.'
message_api.diploma.supered: 'Pentru depășirea'
message_api.diploma.granted: 'Acordat la'
message_api.diploma.date: Data
message_api.diploma.diploma: Diplomă
message_api.diploma.trato: domnule/doamne
message_api.diploma.nif: 'cu număr de identificare fiscală'
message_api.diploma.services: 'care lucrează pentru societate'
message_api.diploma.cif: 'cu număr de TVA'
message_api.diploma.evaluation: 'A absolvit acțiunea de formare cu o evaluare pozitivă.'
message_api.diploma.code: 'Cod AF/GROUP'
message_api.diploma.between: 'În timpul zilelor'
message_api.diploma.duration: 'cu o durată totală de'
message_api.diploma.fomartionType: 'ore în modul de antrenament'
message_api.diploma.courseContent: 'Conținutul cursului pe pagina următoare'
message_api.diploma.signatureSeal: 'Semnătura și ștampila entității responsabile <br> pentru furnizarea formării.'
message_api.diploma.signatureDate: 'Data eliberării'
message_api.diploma.acreditation: Acreditare
message_api.diploma.signature: 'Semnătura participantului'
message.api.diploma.type_course_default: E-learning
message.api.diploma.dates: 'Data de la %dateFrom% la %dateTo%'
message.api.diploma.hours: '{0} %count% ore|{1} %count% oră|]1,Inf] %count% ore'
message.api.diploma.duration: Durată
message.api.diploma.with_dni: 'cu %nameIdentification% %dni%'
message_api.announcement.cancelation_announcement: 'Anularea apelului %course%'
message_api.help_category.delete_error: 'Nu se poate șterge categoria %categoryName%, aceasta are conținut legat'
