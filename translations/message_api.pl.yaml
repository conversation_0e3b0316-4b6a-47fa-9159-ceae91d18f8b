message_api.content.scorm_correctly: 'Scorm został prawidłowo uruchomiony'
message_api.content.token_not_send: 'Token nie został wysłany'
message_api.content.error_get_content: 'W<PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas próby uruchomienia rozdziału:'
message_api.content.scorm_user_not_access: 'Użytkownik nie ma dostępu do tego kursu'
message_api.content.scorm_not_content: 'Rozd<PERSON>ł nie jest pakietem treści'
message_api.base.credentials_no_correct: 'Wprowadzone dane uwierzytelniające są nieprawidłowe.'
message_api.base.authorized_access: 'Odmowa dostępu'
message_api.base.user_successfully: 'Użytkownik został pomyślnie wylogowany'
message_api.base.error_login: 'Wystąpił błąd podczas próby zalogowania użytkownika - Error:'
message_api.base.authorized_token_fail: 'Nie udało Ci się uzyskać dostępu przez SSO. Jeśli jeszcze nie zarejestrowałeś i nie aktywowałeś swojego użytkownika na platformie, musisz to zrobić, aby włączyć ten rodzaj dostępu SSO.'
message_api.base.user_not_exists_in_ad: 'Nieprawidłowe poświadczenia lub użytkownik nie istnieje w usłudze Active Directory.'
message_api.controller.error_get_course: 'Wystąpił błąd podczas próby zarejestrowania użytkownika - Error:'
message_api.controller.chapter_init: 'Rozdział prawidłowo rozpoczęty'
message_api.controller.error_chapter_init: 'Wystąpił błąd podczas próby uruchomienia rozdziału:'
message_api.controller.error_chapter_update: 'Rozdział został poprawnie zaktualizowany'
message_api.controller.help_text: 'Pomoc. Teksty zostały pomyślnie pobrane'
message_api.controller.course_not_found: 'Nie znaleziono kursów'
message_api.controller.user_not_access: 'Użytkownik nie ma dostępu do tego kursu'
message_api.controller.evaluation_course: 'Przesłana ocena'
message_api.controller.training_itinerary: 'Plan treningowy'
message_api.controller.continue_training: 'Dalsze szkolenie'
message_api.message.not_found: 'Nie znaleziono wiadomości'
message_api.message.not_access: 'Odmowa dostępu'
message_api.scorm.init: 'Scorm został prawidłowo uruchomiony'
message_api.scorm.not_package: 'Rozdział nie jest pakietem SCORM'
message_api.scorm.button_add_scorm: 'Dodaj pakiet scorm'
message_api.scorm.button_delete_scorm: 'Usuń pakiet scorm'
message_api.scorm.title_package: 'Pakiet Scorm'
message_api.scorm.upload_file_scorm: 'Wybierz plik .zip'
message_api.scorm.info_upload_scorm: 'Należy załadować pakiet scorm'
message_api.notification.not_found: 'Nie znaleziono powiadomienia'
message_api.player.no_content: 'Ten rozdział nie zawiera treści'
message_api.alert.minimal_question: 'Ta gra wymaga minimum %number% pytań.'
message_api.alert.image_puzzle: 'Układanka musi mieć obraz'
message_api.alert.question_correct: 'Do tego pytania należy dodać poprawną odpowiedź'
message_api.alert.chapter_content: 'W tym rozdziale brakuje treści'
message_api.alert.pdf: 'Ten rozdział wymaga pliku pdf'
message_api.alert.video: 'W tym rozdziale brakuje treści'
message_api.alert.slider: 'Ten rozdział zawiera co najmniej %number% obrazów.'
message_api.alert.scorm: 'Ten rozdział wymaga pakietu scorm'
message_api.alert.question_hidden: 'To pytanie musi zawierać tylko jedną poprawną odpowiedź'
message_api.alert.minimal_pair: 'Ten rozdział wymaga co najmniej %number% par.'
message_api.announcement.call_for_aplications: Konwokacja
message_api.alert.course_content_incomplete: 'W rozdziałach brakuje informacji, prosimy o poprawienie i dodanie treści w celu kontynuowania tworzenia zaproszenia do składania wniosków.'
message_api.diploma.supered: 'Za pokonanie'
message_api.diploma.granted: Przyznano
message_api.diploma.date: Data
message_api.diploma.diploma: Dyplom
message_api.diploma.trato: D./Dña
message_api.diploma.nif: 'z numerem identyfikacji podatkowej'
message_api.diploma.services: 'który świadczy usługi na rzecz Spółki'
message_api.diploma.cif: 'z numerem VAT'
message_api.diploma.evaluation: 'Akcja szkoleniowa została zakończona pozytywną oceną'
message_api.diploma.code: 'Kod AF/GRUPO'
message_api.diploma.between: 'W dniach'
message_api.diploma.duration: 'o łącznym czasie trwania'
message_api.diploma.fomartionType: 'godzin w trybie treningowym'
message_api.diploma.courseContent: 'Zawartość kursu na następnej stronie'
message_api.diploma.signatureSeal: 'Podpis i pieczęć podmiotu odpowiedzialnego za przeprowadzenie szkolenia.'
message_api.diploma.signatureDate: 'Data wydania'
message_api.diploma.acreditation: Akredytacyjny
message_api.diploma.signature: 'Podpis uczestnika'
message.api.diploma.type_course_default: E-learning
message.api.diploma.dates: 'Data od %dateFrom% do %dateTo%'
message.api.diploma.hours: '{0} %count% godziny|{1} %count% godzina|]1,Inf] %count% godziny'
message.api.diploma.duration: 'Czas trwania'
message.api.diploma.with_dni: 'z %nameIdentification% %dni% %dni%'
message_api.announcement.cancelation_announcement: 'Anulowanie połączenia %course%'
message_api.help_category.delete_error: 'Nie można usunąć kategorii %categoryName%, ma ona powiązaną zawartość'
