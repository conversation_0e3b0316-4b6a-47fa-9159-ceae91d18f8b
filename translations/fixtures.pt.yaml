nps_question.text.description: 'Dê-nos a sua opinião'
nps_question.nps.description: 'Avaliação do curso'
type_course.teleformacion.name: 'Educação eletrónica'
type_course.teleformacion.description: 'Para cursos de aprendizagem eletrónica'
type_course.presencial.name: 'No local'
type_course.presencial.description: 'Cursos no local'
type_course.mixto.name: Misto
type_course.mixto.description: 'It is a combination of e-learning and face-to-face training.'
type_course.aula_virtual.name: 'Sala de aula virtual'
type_course.aula_virtual.description: 'As aulas são dadas por videoconferência.'
alert_type_tutor.1.name: 'A pessoa convocada não acedeu ao curso'
alert_type_tutor.1.description: 'Será enviado um alerta ao tutor se a pessoa convocada não tiver acedido à disciplina.'
alert_type_tutor.2.name: '50% do tempo da chamada já passou e 25% do conteúdo não foi concluído'
alert_type_tutor.2.description: 'Será enviado um alerta ao tutor se tiverem decorrido 50% do tempo da chamada e 25% do conteúdo não tiver sido concluído.'
alert_type_tutor.3.name: '80% do tempo da chamada já passou e 50% do conteúdo não foi concluído.'
alert_type_tutor.3.description: 'Será enviado um alerta ao tutor se tiverem decorrido 80% do tempo da chamada e 50% dos conteúdos não tiverem sido concluídos.'
alert_type_tutor.4.name: 'Faltam apenas alguns dias para o final do convite e o curso ainda não está concluído'
alert_type_tutor.4.description: 'É preciso avaliar o número de dias que são considerados poucos dias.'
alert_type_tutor.5.name: 'A pessoa convocada concluiu o curso mas não respondeu ao inquérito.'
alert_type_tutor.5.description: 'Se a plataforma tiver inquéritos, será enviado um alerta ao tutor se a pessoa chamada tiver concluído o curso mas não tiver respondido ao inquérito.'
alert_type_tutor.6.name: 'A pessoa concluiu o curso mas não descarregou o diploma'
alert_type_tutor.6.description: 'Será enviado um alerta ao tutor se a pessoa convocada tiver concluído o curso mas não tiver descarregado o diploma.'
announcement_configuration_type.temporalizacion.name: Calendário
announcement_configuration_type.temporalizacion.description: 'Para facilitar o acompanhamento do curso, atribuiremos um tempo a cada bloco de conteúdos e actividades, podendo assim detetar quais os participantes que estão a trabalhar a um ritmo adequado ou que estão atrasados no processo de formação.'
announcement_configuration_type.curso_bonificado.name: 'Curso subsidiado'
announcement_configuration_type.curso_bonificado.description: 'Os cursos subsidiados são os realizados através da Fundación Tripartita e financiados pelas empresas através das contribuições para a Segurança Social.'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'O Chat é uma ferramenta de comunicação síncrona que permite aos participantes da disciplina interagir em tempo real através de mensagens de texto.'
announcement_configuration_type.notificaciones.name: Notificações
announcement_configuration_type.notificaciones.description: 'As notificações são mensagens enviadas aos participantes da disciplina para os informar de notícias ou eventos importantes.'
announcement_configuration_type.mensajeria.name: Mensagens
announcement_configuration_type.mensajeria.description: 'Um sistema de mensagens é um sistema de comunicação que permite aos participantes da disciplina enviar e receber mensagens privadas.'
announcement_configuration_type.foros.name: Fóruns
announcement_configuration_type.foros.description: 'Um fórum é um espaço de comunicação assíncrono que permite aos participantes da disciplina trocar mensagens sobre um determinado tópico.'
announcement_configuration_type.diploma.name: Diploma
announcement_configuration_type.diploma.description: 'Os diplomas são certificados entregues aos participantes num curso para certificar a conclusão do mesmo.'
announcement_configuration_type.tutor_alerts.name: 'Ativar alertas do tutor'
announcement_configuration_type.tutor_alerts.description: 'Os alertas são mensagens enviadas ao tutor de uma disciplina para o informar de notícias ou acontecimentos importantes.'
announcement_configuration_type.encuesta_satisfaccion.name: 'Inquérito de satisfação'
announcement_configuration_type.encuesta_satisfaccion.description: 'Os inquéritos de satisfação são questionários enviados aos participantes nos cursos para saber a sua opinião sobre o curso.'
announcement_configuration_type.finalizar_convocatoria.name: 'O curso permanecerá ativo no final da chamada.'
announcement_configuration_type.finalizar_convocatoria.description: 'O utilizador poderá aceder ao conteúdo da disciplina após a conclusão da mesma.'
announcement_configuration_type.firma_digital.name: 'Assinatura digital'
announcement_configuration_type.firma_digital.description: 'A assinatura digital é necessária para assinar a participação num curso presencial.'
announcement_configuration_type.gestion_costes.name: 'Gestão de custos'
announcement_configuration_type.gestion_costes.description: 'A gestão dos custos permite aos grupos indicar o custo da chamada.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Ativar as notificações por correio eletrónico.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Ativar as notificações normais'
announcement_configuration_type.objetivos_contenidos.name: 'Objectivos e conteúdos'
announcement_configuration_type.objetivos_contenidos.description: 'Os objectivos e o conteúdo do curso serão incluídos no diploma.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'A identificação do aluno será incluída no diploma.'
announcement_configuration_type.template_excel.name: 'Modelo de registro em Excel'
announcement_configuration_type.template_excel.description: 'Este modelo será utilizado para a inscrição de estudantes, utilizando o código HRBP, em vez do DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Permitir que o tutor descarregue relatórios de grupo em formato ZIP'
announcement_criteria.1.name: 'Número mínimo de capítulos a completar'
announcement_criteria.1.description: 'A nota mínima pode ser, por exemplo, 70 em 100.'
announcement_criteria.2.name: 'Concluir tarefas'
announcement_criteria.2.description: 'Controlos de avaliação'
announcement_criteria.3.name: 'Tempo máximo de inatividade'
announcement_criteria.3.description: 'Por exemplo, o utilizador não pode estar inativo durante mais de 10 minutos.'
announcement_criteria.4.name: 'Actividades completas'
announcement_criteria.4.description: 'O utilizador deve realizar as actividades propostas'
announcement_criteria.5.name: 'Horas de formação concluídas'
announcement_criteria.5.description: 'Por exemplo, se o programa tiver 20 horas, o utilizador deve completar as 20 horas.'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Primeira etapa da elaboração do convite à apresentação de propostas'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Segunda etapa da criação do convite, onde são preenchidas as informações gerais.'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Esta etapa depende de o cliente ter ou não ativado o bónus.'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'É aqui que os alunos são adicionados à chamada, que podem ser atribuídos a um grupo.'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'Nesta etapa, os grupos de alunos criados na etapa anterior são configurados.'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'Nesta etapa, é configurado o inquérito a enviar aos alunos.'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'Nesta etapa, é configurado o inquérito a enviar aos alunos.'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'Nesta etapa, são apresentados os diplomas disponíveis na plataforma para que o cliente possa selecionar o que pretende.'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Estes alertas são alertas especiais que informam o tutor sobre os acontecimentos da chamada.'
class_room_virtual.zoom.name: Zoom
class_room_virtual.zoom.description: 'Plataforma de conferência Web em linha, permite chamadas de vídeo em alta definição, com a funcionalidade de partilhar o ambiente de trabalho, quadro branco, chat, gravar a conferência, partilhar documentos e poder aceder a partir de qualquer lugar, uma vez que está disponível para dispositivos móveis.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'A plataforma do ClickMeeting é uma das interfaces de webinar mais fáceis de usar no mercado e oferece uma infinidade de opções flexíveis de personalização.'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Solução de código aberto para videoconferências com ligações encriptadas e disponível para vários sistemas operativos.'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Software de videoconferência de código aberto fácil de integrar e altamente personalizável'
configuration_cliente_announcement.COMMUNICATION.description: 'Isto permite ativar as comunicações no âmbito da chamada.'
configuration_cliente_announcement.CERTIFICATE.description: 'Isto permite descarregar os diplomas no âmbito do convite à apresentação de candidaturas.'
configuration_cliente_announcement.SURVEY.description: 'Isto permite que as sondagens sejam activadas e herdadas na chamada.'
configuration_cliente_announcement.ALERT.description: 'Isto permite-lhe permitir que os alertas sejam herdados na secção de alertas do tutor.'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Permite a temporalização dos capítulos de um convite à apresentação de propostas.'
configuration_cliente_announcement.BONIFICATION.description: 'Bónus do convite, especialmente para o convite da fundação tripartida'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Permite o acesso ao conteúdo da chamada depois de esta ter sido finalizada'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Permite a assinatura digital no convite à apresentação de candidaturas, nomeadamente nos cursos presenciais'
configuration_cliente_announcement.COST.description: 'Permite-lhe permitir que os clientes apliquem custos no convite à apresentação de propostas'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Notificações após a ativação da chamada (e-mail, notificação frontal)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Esta configuração foi especialmente concebida para os clientes Iberostar, de modo a não afetar o fluxo de fundos'
configuration_cliente_announcement.REPORT.description: 'Permitir a apresentação de relatórios nos convites à apresentação de propostas'
type_course_announcement_step_creation.seleccionar_curso.name: 'Selecionar curso'
type_course_announcement_step_creation.seleccionar_curso.description: 'Selecionar o curso para o qual a chamada deve ser criada'
type_course_announcement_step_creation.convocatoria.name: 'Convite à apresentação de candidaturas'
type_course_announcement_step_creation.convocatoria.description: 'Informações sobre o convite à apresentação de propostas'
type_course_announcement_step_creation.bonificacion.name: Bónus
type_course_announcement_step_creation.bonificacion.description: 'Informações sobre o convite à apresentação de propostas'
type_course_announcement_step_creation.alumnado.name: Estudantes
type_course_announcement_step_creation.alumnado.description: 'Os alunos são adicionados à disciplina'
type_course_announcement_step_creation.grupos.name: Grupos
type_course_announcement_step_creation.grupos.description: 'As informações sobre o grupo são pormenorizadas e o tutor também é adicionado.'
type_course_announcement_step_creation.comunicacion.name: Comunicação
type_course_announcement_step_creation.comunicacion.description: 'Este passo dependerá do facto de o cliente ter ou não a comunicação activada.'
type_course_announcement_step_creation.encuesta.name: Inquérito
type_course_announcement_step_creation.encuesta.description: 'Este passo dependerá do facto de o cliente ter ou não o inquérito ativado.'
type_course_announcement_step_creation.diploma.name: Diploma
type_course_announcement_step_creation.diploma.description: 'Esta etapa dependerá do facto de o cliente ter ou não ativado o diploma.'
type_course_announcement_step_creation.alertas.name: Alertas
type_course_announcement_step_creation.alertas.description: 'Isto pode depender da configuração do cliente.'
type_diploma.easylearning.name: Easylearning
type_diploma.easylearning.description: 'É o diploma da empresa cliente'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'É o diploma da Fundae'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'É o diploma Hobetuz'
type_money.euro.name: Euro
type_money.euro.country: Espanha
type_money.dolar_estadounidense.name: 'Dólar americano'
type_money.dolar_estadounidense.country: 'Estados Unidos da América'
section_default_front.mi_formacion.name: 'A minha formação'
section_default_front.mi_formacion.description: 'Na formação atribuída, pode encontrar todos os cursos que lhe foram atribuídos.'
section_default_front.formacion_adicional.name: 'Formação complementar'
section_default_front.formacion_adicional.description: 'Nesta secção, encontrará todos os cursos de campus aberto.'
section_default_front.formacion_asignada.name: 'Formação atribuída'
section_default_front.formacion_asignada.description: 'Nesta secção, pode encontrar todas as disciplinas que lhe foram atribuídas.'
setting.multi_idioma.name: Multilingue
setting.multi_idioma.description: 'Oferece uma interface multilingue'
setting.default_lenguage.name: 'Língua predefinida'
setting.default_lenguage.description: 'língua predefinida da aplicação'
setting.languages.name: Língua
setting.languages.description: 'Línguas disponíveis na aplicação'
setting.registro_libre.name: 'Registo gratuito de utilizadores'
setting.registro_libre.description: 'Registo gratuito de utilizadores'
setting.opinion_plataforma.name: 'Pareceres sobre a plataforma'
setting.opinion_plataforma.description: 'Pareceres sobre a plataforma'
setting.validacion_automatica.name: 'Validação automática do registo do utilizador'
setting.validacion_automatica.description: 'Validação automática do registo do utilizador'
setting.filtros_plataforma.name: 'Filtros na plataforma'
setting.filtros_plataforma.description: 'Permite ativar ou desativar os filtros da plataforma.'
setting.itinearios_plataforma.name: 'Itinerários na plataforma'
setting.itinearios_plataforma.description: 'Permite ativar ou desativar os itinerários na plataforma.'
setting.seccion_cursos.name: 'Secções do curso [FRONT]'
setting.seccion_cursos.description: 'Secções do curso [FRONT],'
setting.set_points_course.name: 'Estabelecer pontos para o curso'
setting.set_points_course.description: 'É utilizado para atribuir pontos aos cursos quando os cria ou edita, especialmente para o curso de aprendizagem eletrónica.'
setting.default_points_course.name: 'Pontos por defeito '
setting.default_points_course.description: 'Este valor será adotado como valor por defeito na atribuição de notas para a conclusão de um curso. Funciona dividindo 50% entre os capítulos teóricos e 50% entre os capítulos avaliativos.'
setting.documentation_course.name: 'Informações gerais'
setting.documentation_course.description: 'Ativado ativa o campo de texto "Informações gerais" ao criar uma disciplina'
setting.open_course.name: 'Campus aberto'
setting.open_course.description: 'Habilitado, aparece a possibilidade de ativar/desativar o campus aberto.'
setting.client_id.name: 'ID do cliente vimeo'
setting.client_id.description: 'É o identificador do cliente Vimeo Gestionet.'
setting.client_secret.name: 'Cliente secreto do vimeo'
setting.client_secret.description: 'Cliente secreto do vimeo Gestionet'
setting.access_token.name: 'Token de acesso'
setting.access_token.description: 'Token de acesso Gestionet'
setting.user_id.name: 'ID do cliente'
setting.user_id.description: 'Id do usuário cadastrado no vimeo Gestionet'
setting.project_id.name: 'Pasta de capítulos de vídeo'
setting.project_id.description: 'É o identificador onde estão alojados os recursos dos capítulos de vídeo.'
setting.project_id_resource_course.name: 'Kit de recursos materiais (convite à apresentação de propostas)'
setting.project_id_resource_course.description: 'É o identificador da pasta onde estão guardados os vídeos relacionados com os materiais do curso e com a chamada.'
setting.project_id_task_course.name: 'Pasta de recursos da tarefa'
setting.project_id_task_course.description: 'É o identificador da pasta onde estão guardados os vídeos relacionados com as tarefas e a chamada da disciplina.'
setting.project_id_video_Quiz.name: 'Pasta de recursos do Videoquiz'
setting.project_id_video_Quiz.description: 'É o identificador da pasta onde estão guardados os vídeos relacionados com o jogo de perguntas e respostas.'
setting.project_id_Roleplay.name: 'Kit de recursos para jogos de representação'
setting.project_id_Roleplay.description: 'Identificado para recursos de tipo vídeo no jogo de papéis'
setting.upload_sudomain.name: 'Carregar para o subdomínio'
setting.upload_sudomain.description: 'Esta variável é usada para fazer upload de vídeos e arquivos SCORM, permitindo superar as restrições de 100 Mb da Cloudflare.'
setting.from_email.name: 'E-mail do remetente'
setting.from_email.description: 'É a origem das mensagens de correio eletrónico enviadas a partir da plataforma.'
setting.from_name.name: 'Nome do remetente'
setting.from_name.description: 'O nome do remetente exibido nos e-mails'
setting.from_cif.name: CIF
setting.from_cif.description: 'Número de identificação fiscal da empresa'
setting.email_support.name: 'Apoio por correio eletrónico'
setting.email_support.description: 'Estas mensagens de correio eletrónico são utilizadas para enviar notificações de apoio.'
setting.email_support_register.name: 'Administrador da receção de correio eletrónico'
setting.email_support_register.description: 'Este e-mail é utilizado para receber solicitações de cadastro na plataforma'
setting.news.name: Notícias
setting.news.description: 'Habilitado ativa o módulo “Notícias” na plataforma'
setting.foro.name: Fórum
setting.foro.description: 'Habilitado ativa o módulo "Fórum" na plataforma'
setting.desafios.name: Duelos
setting.desafios.description: 'Habilitado ativa o módulo "Duelos" na plataforma'
setting.secciones.name: Secções
setting.secciones.description: 'As seções habilitadas são exibidas na frente'
setting.encuestas.name: Inquéritos
setting.encuestas.description: 'Habilitado ativa o módulo “Pesquisas” na plataforma'
setting.active_cron_exports.name: 'Compilação de relatório aguardando'
setting.active_cron_exports.description: 'Habilitado, o relatório é gerado e colocado em fila de espera, e desabilitado, o download é direto.'
setting.gender_excel.name: 'Campo "Gênero" em xls'
setting.gender_excel.description: 'Ativado ativa a coluna "Gênero" nos relatórios xls de download'
setting.code.name: 'Campo "Código" em xls'
setting.code.description: 'Habilitado ativa a coluna "Código" nos relatórios xls de download'
setting.finished_chapters.name: 'Capítulos concluídos'
setting.finished_chapters.description: 'Habilitado ativa "Capítulos Concluídos" em relatórios de download xls'
setting.zoom_cliente_id.name: 'ID do cliente Zoom'
setting.zoom_cliente_id.description: 'ID do cliente Zoom - necessário para usar a API do Zoom'
setting.zoom_cliente_secret.name: 'Segredo do cliente de Zoom'
setting.zoom_cliente_secret.description: 'Chave do cliente Zoom - necessária para usar a API de zoom'
setting.zoom_account_id.name: 'ID da conta Zoom'
setting.zoom_account_id.description: 'Número da conta do cliente Zoom - necessário para usar a API do Zoom'
setting.zoom_email.name: 'Zoom Email'
setting.zoom_email.description: 'E-mail do cliente Zoom - necessário para usar a API do Zoom'
setting.clickmeeting_api_key.name: 'Chave da API do ClickMeeting'
setting.clickmeeting_api_key.description: 'ID do cliente ClickMeeting - necessário para usar a API do ClickMeeting'
setting.clikmeeting_dirbase.name: 'Diretório base do ClickMeeting'
setting.clikmeeting_dirbase.description: 'Endereço do servidor ClickMeeting'
setting.clikmeeting_events_paralel.name: 'Eventos paralelos do ClickMeeting'
setting.clikmeeting_events_paralel.description: 'Número de eventos secundários contratados'
setting.plugnmeet_serverurl.name: 'Url do servidor plugNmeet'
setting.plugnmeet_serverurl.description: 'endereço do servidor plugNmeet'
setting.plugnmeet_api_key.name: 'Chave API do plugNmeet'
setting.plugnmeet_api_key.description: 'ID do cliente plugNmeet'
setting.plugnmeet_secret.name: 'chave secreta do plugNmeet'
setting.plugnmeet_secret.description: 'chave de cliente plugNmeet'
setting.plugnmeet_analyticsurl.name: 'Análise de URL do plugNmeet'
setting.plugnmeet_analyticsurl.description: 'Endereço do servidor plugNmeet para análise'
setting.zoom_urlreports.name: 'Url do relatório de zoom'
setting.zoom_urlreports.description: 'Endereço onde são armazenados os relatórios de zoom'
setting.plugnmeet_urlreports.name: 'url de relatório do plugNmeet'
setting.plugnmeet_urlreports.description: 'Endereço onde são armazenados os relatórios plugNmeet'
setting.clickmeeting_urlreports.name: 'URL de relatório do ClickMeeting'
setting.clickmeeting_urlreports.description: 'Endereço onde os relatórios do ClickMeeting são armazenados'
setting.library_enabled.name: 'Arquivo de jornais'
setting.library_enabled.description: 'Habilitado ativa o módulo “Biblioteca de Jornais” na plataforma'
setting.library_audio_local.name: 'Áudio local'
setting.library_audio_local.description: 'Áudio  local'
setting.library_audio_path.name: 'Caminho de áudio '
setting.library_audio_path.description: 'Caminho de áudio '
setting.library_file_path.name: 'Caminho do arquivo'
setting.library_file_path.description: 'Caminho do ficheiro '
setting.library_data_page_size.name: 'Tamanho da página de dados'
setting.library_data_page_size.description: 'Tamanho da página de dados'
setting.library_comments.name: Comentários
setting.library_comments.description: 'Comentários '
setting.challenge_loops.name: 'Número de perguntas por duelo'
setting.challenge_loops.description: 'Número padrão de perguntas que fazem parte de um duelo.'
setting.points_for_win.name: 'Pontos de vitória'
setting.points_for_win.description: 'Pontos concedidos por vencer um duelo'
setting.points_for_lose.name: 'Pontos por derrota'
setting.points_for_lose.description: 'Pontos subtraídos por perder um duelo'
setting.points_fortie.name: 'Pontos por empate'
setting.points_fortie.description: 'Pontos concedidos por empatar um duelo com sucessos'
setting.points_corrects.name: 'Pontos por empate zero'
setting.points_corrects.description: 'Pontos concedidos por empatar um duelo com zero acertos'
setting.points_for_left.name: 'Pontos por abandono'
setting.points_for_left.description: 'Pontos concedidos por deixar o duelo'
setting.total_duels.name: 'Número máximo de duelos'
setting.total_duels.description: 'Número máximo de duelos disponíveis para cada participante.'
setting.seconds_per_question.name: 'Segundos disponíveis por pergunta'
setting.seconds_per_question.description: 'Tempo expresso em segundos, disponível para responder cada uma das questões.'
setting.user_dni.name: 'ID do usuário'
setting.user_dni.description: 'Habilitado na criação/modificação de usuário, aparece o campo “DNI”'
setting.edit_code.name: 'Código de usuário'
setting.edit_code.description: 'Habilitado na criação/modificação do usuário o campo "Código" aparece'
setting.stats_acumulative.name: 'Estatísticas acumuladas'
setting.stats_acumulative.description: 'Isto é para o caso de querer que as estatísticas sejam cumulativas.'
setting.maximo_fechas.name: 'Intervalo máximo de datas'
setting.maximo_fechas.description: 'Número máximo de dias permitidos para consulta de dados'
setting.maximo_horas.name: 'Máximo de pedidos por hora'
setting.maximo_horas.description: 'Máximo de solicitações permitidas por hora'
setting.maximo_dia.name: 'Número máximo de pedidos por dia'
setting.maximo_dia.description: 'Máximo de solicitações permitidas por dia'
setting.fundae.name: Fundae
setting.fundae.description: 'Se esta opção for activada, quando um convite é publicado, os utilizadores têm de preencher todos os campos necessários da tabela users_extra_fundae.'
setting.margen_entrada.name: 'Margem de entrada por defeito'
setting.margen_entrada.description: 'Margem de entrada predefinida, utilizada no código QR'
setting.margen_salida.name: 'Margem de saída por defeito'
setting.margen_salida.description: 'Margem de saída predefinida, utilizada no código QR'
setting.registrar_qr.name: 'Registar com a sessão QR'
setting.registrar_qr.description: 'Se ativado, as sessões serão registadas com QR.'
setting.maximo_alumnos.name: 'Número máximo de alunos por grupo'
setting.maximo_alumnos.description: 'Número máximo de alunos por grupo'
setting.min_score.name: 'Nota mínima de aprovação'
setting.min_score.description: 'Nota mínima de aprovação'
setting.types_action.name: 'Tipos de ação'
setting.types_action.description: 'Tipos de ação'
setting.materiales_convocatoria.name: 'Permitir a criação de materiais num convite à apresentação de documentos'
setting.materiales_convocatoria.description: 'Permitir a criação de materiais num convite à apresentação de documentos'
setting.tareas_convocatoria.name: 'Permitir a criação de tarefas num convite à apresentação de propostas'
setting.tareas_convocatoria.description: 'Permitir a criação de tarefas num convite à apresentação de propostas'
setting.minimo_minutos.name: 'Tempo mínimo de inatividade em minutos'
setting.minimo_minutos.description: 'Tempo mínimo de inatividade em minutos, aplicável aos utilizadores que estão na plataforma.'
setting.timezones.name: 'Fusos horários permitidos no convite à apresentação de propostas'
setting.timezones.description: 'Fuso horário que pode ser configurado no convite à apresentação de candidaturas'
catalog.1.name: 'Tipos de capítulos'
catalog.1.description: 'Configuração dos tipos de capítulos que estarão disponíveis na plataforma'
catalog.2.name: 'Tipos de cursos'
catalog.2.description: 'Configuração dos tipos de cursos que estarão disponíveis na plataforma'
catalog.3.name: 'Critérios de aprovação'
catalog.3.description: 'Configuração dos critérios de aprovação, que estarão disponíveis na plataforma'
catalog.4.name: 'Alertas do tutor'
catalog.4.description: 'Configuração de alertas para os tutores, que estarão disponíveis na plataforma'
catalog.5.name: 'Tipos de diplomas'
catalog.5.description: 'Configuração dos tipos de diplomas que estarão disponíveis na plataforma'
catalog.6.name: 'Configuração do cliente em convocação'
catalog.6.description: 'Configuração das etapas a apresentar no convite à apresentação de propostas'
catalog.7.name: 'Tipos de moeda'
catalog.7.description: 'Configuração dos tipos de moedas que estarão disponíveis na plataforma'
catalog.8.name: 'Grupo de configurações'
catalog.8.description: 'Grupo de configurações, que estarão disponíveis na plataforma'
catalog.9.name: Configurações
catalog.9.description: 'Configurações por grupo, que estarão disponíveis na plataforma'
catalog.10.name: Empresa
catalog.10.description: 'Empresas de utilizadores, que estarão disponíveis na plataforma'
catalog.11.name: 'Categoria profissional'
catalog.11.description: 'Categorias profissionais dos utilizadores, que estarão disponíveis na plataforma'
catalog.12.name: 'Centro de trabalho do utilizador'
catalog.12.description: 'Centros de trabalho do utilizador, que estarão disponíveis na plataforma'
catalog.13.name: 'Serviço de Trabalho do Utilizador'
catalog.13.description: 'Departamentos de trabalho dos utilizadores, que estarão disponíveis na plataforma'
catalog.14.name: 'Nível de estudo do utilizador'
catalog.14.description: 'Níveis de estudo do utilizador, que estarão disponíveis na plataforma'
catalog.15.name: 'Etapas para os diferentes tipos de cursos'
catalog.15.description: 'Configuração dos passos para os diferentes tipos de cursos, que estarão disponíveis na plataforma'
catalog.16.name: 'Tipos de salas de aula virtuais'
catalog.16.description: 'Fusos horários permitidos no convite à apresentação de propostas'
catalog.17.name: 'Tipos de identificação'
catalog.17.description: 'Tipos de identificação disponíveis na plataforma'
nps_question.text.name: Texto
nps_question.text.descripction: 'Dê-nos a sua opinião'
setting.help.user.name: 'Incluir pdf de ajuda no menu do utilizador'
setting.help.user.description: 'Esta ajuda foi criada especialmente para iberostar.'
catalog.18.name: 'Modalidades de chamadas no local'
catalog.18.description: 'Esta é uma necessidade especial da Iberostar'
setting.userPolicies_plataforma.name: 'Política de privacidade'
setting.userPolicies_plataforma.description: 'Esta variável é utilizada para ativar um modal no front-end quando o utilizador não aceita a política de privacidade.'
setting.course.tab.person.name: 'Estatísticas do curso por pessoa'
setting.course.tab.stats.name: "Estatísticas Gerais do Curso\n"
setting.course.tab.opinions.name: "Opiniões por curso\n"
setting.documentation.name: Tutoriais
setting.documentation.description: 'Habilitado ativa o módulo “Tutoriais” da plataforma onde você pode fazer upload de informações de interesse e associá-las a perfis de administração.'
setting.user_company.name: Empresas
setting.user_company.description: 'Habilitado ativa o módulo “Empresas” na plataforma permitindo que você crie esse filtro e seja atribuído a um tutor na chamada.'
setting.pages.name: Rodapé
setting.pages.description: 'Ative o rodapé no campus'
setting.lite_formation.name: 'Grupo de treino de estatística geral'
setting.lite_formation.description: 'Grupo de treino de estatística geral'
setting.lite_formation.formationHours.name: 'Horas de formação'
setting.lite_formation.formationHours.description: 'Total de horas de formação e média de horas de formação por pessoa'
setting.lite_formation.peopleWithCourses.name: 'Pessoas com curso'
setting.lite_formation.peopleWithCourses.description: 'As pessoas atualmente em formação e as pessoas que concluíram pelo menos um curso'
setting.lite_formation.courseStartedAndFinished.name: 'Cursos iniciados, em curso e concluídos'
setting.lite_formation.courseStartedAndFinished.description: 'Número de cursos iniciados, em curso e concluídos'
setting.lite_formation.requiredCourses.name: 'Cursos obrigatórios'
setting.lite_formation.requiredCourses.description: 'Cursos obrigatórios atribuídos a uma chamada ou itinerário'
setting.lite_formation.general.name: 'Em geral'
setting.lite_formation.general.description: 'Em geral'
setting.lite_formation.openedCourses.name: 'Cursos abertos'
setting.lite_formation.openedCourses.description: 'Cursos voluntários'
setting.lite_formation.educativeStatus.name: 'Nível educacional'
setting.lite_formation.educativeStatus.description: 'Estado do treino por níveis de pontos'
setting.lite_formation.gamifiedPills.name: 'Pílulas gamificadas'
setting.lite_formation.gamifiedPills.description: 'Número de capítulos gamificados, falhas e sucessos nos testes gamificados'
setting.lite_formation.gamifiedTest.name: 'Pílulas de teste'
setting.lite_formation.gamifiedTest.description: 'Testes gamificados utilizados e sucessos e falhas por tipo de teste'
setting.lite_formation.peoplePerformance.name: 'Desempenho das pessoas'
setting.lite_formation.peoplePerformance.description: 'Desempenho das pessoas'
setting.lite_formation.coursesByStars.name: 'Cursos por pontuação'
setting.lite_formation.coursesByStars.description: 'Classificação por estrelas dos cursos'
setting.lite_formation.structureAndHotel.name: 'Departamentos e hotéis'
setting.lite_formation.structureAndHotel.description: 'Percentagem por grupo'
setting.lite_formation.schoolFinishedAndProgress.name: 'Escola concluída e em curso'
setting.lite_formation.schoolFinishedAndProgress.description: 'Escola com maior participação, cursos em curso e concluídos'
setting.lite_formation.coursesBySchool.name: 'Cursos por escola'
setting.lite_formation.coursesBySchool.description: 'Número de cursos por categoria'
setting.lite_formation.coursesByDepartment.name: 'Cursos por departamento'
setting.lite_formation.coursesByDepartment.description: 'Criação de cursos por departamento'
setting.lite_formation.usersMoreActivesByCourses.name: 'Utilizadores mais ativos por cursos'
setting.lite_formation.usersMoreActivesByCourses.description: 'Pessoas mais e menos ativas de cursos concluídos'
setting.lite_evolution.name: 'Grupo de Evolução em Estatísticas Gerais'
setting.lite_evolution.description: 'Grupo de Evolução em Estatísticas Gerais'
setting.lite_evolution.trainedPerson.name: 'Pessoas treinadas'
setting.lite_evolution.trainedPerson.description: 'As pessoas que concluíram pelo menos um curso'
setting.lite_evolution.startedCourses.name: 'Cursos iniciados'
setting.lite_evolution.startedCourses.description: 'Cursos iniciados'
setting.lite_evolution.proccessCourses.name: 'Curso em curso'
setting.lite_evolution.proccessCourses.description: 'Curso em curso'
setting.lite_evolution.finishedCourses.name: 'Cursos concluídos'
setting.lite_evolution.finishedCourses.description: 'Cursos concluídos'
setting.lite_evolution.segmentedHours.name: 'Segmentação de horas'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Os novos utilizadores que concluíram um curso'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'As pessoas novas na plataforma que concluíram pelo menos um curso'
setting.lite_demography.name: 'Grupo Demográfico em Estatísticas Gerais'
setting.lite_demography.description: 'Grupo Demográfico em Estatísticas Gerais'
setting.lite_demography.usersBySexAndAge.name: 'Usuários por sexo e idade'
setting.lite_demography.usersBySexAndAge.description: 'Usuários por sexo e idade'
setting.lite_demography.ageDistribution.name: 'Distribuição por idade'
setting.lite_demography.ageDistribution.description: 'Distribuição por idade'
setting.lite_demography.deviceDistribution.name: 'Distribuição por dispositivo'
setting.lite_demography.deviceDistribution.description: 'Distribuição por dispositivos'
setting.lite_demography.usersByCountries.name: 'Distribuição por país'
setting.lite_demography.usersByCountries.description: 'Distribuição por país'
setting.lite_activity.name: 'Grupo de atividades de estatísticas gerais'
setting.lite_activity.description: 'Grupo de atividades de estatísticas gerais'
setting.lite_activity.activityInfo.name: 'Informações de atividade'
setting.lite_activity.activityInfo.description: 'Pessoas ativas no portal, pessoas cadastradas, pessoas que acessaram pelo menos uma vez nos últimos 30 dias, pessoas desativadas e pessoas que nunca entraram na plataforma'
setting.lite_activity.accessDays.name: 'Acesso por dias'
setting.lite_activity.accessDays.description: 'Dias de acesso'
setting.lite_activity.platformAccessByHours.name: 'Acesso por plataforma e horário'
setting.lite_activity.platformAccessByHours.description: 'Horários de acesso à plataforma por dia e hora (mapa de calor)'
setting.lite_activity.courseStartTime.name: 'Distribuição por horários de início do curso'
setting.lite_activity.courseStartTime.description: 'Horários de início do curso'
setting.lite_activity.courseEndTime.name: 'Distribuição por horas de conclusão do curso'
setting.lite_activity.courseEndTime.description: 'Horas de conclusão do curso (mapa de calor)'
setting.lite_activity.coursesStartedVsFinished.name: 'Cursos iniciados versus cursos concluídos'
setting.lite_activity.coursesStartedVsFinished.description: 'Cursos iniciados versus cursos concluídos'
setting.lite_activity.usersMoreActivesByActivity.name: 'Usuários mais ativos'
setting.lite_activity.usersMoreActivesByActivity.description: 'Pessoas mais e menos ativas e seu tempo de uso na plataforma'
setting.lite_itinerary.name: 'Grupo Itinerário em Estatísticas Gerais'
setting.lite_itinerary.description: 'Grupo Itinerário em Estatísticas Gerais'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Itinerários iniciados e finalizados'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Itinerários iniciados e finalizados'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Itinerários concluídos por país'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Itinerários concluídos por país'
setting.survey.hide_empty_comment.name: 'Ocultar opiniões com comentário vazio'
setting.survey.hide_empty_comment.description: 'Ativado oculta opiniões sem comentários'
setting.survey.show_only_ratings.name: 'Mostrar apenas classificação'
setting.survey.show_only_ratings.description: 'Habilitado, mostra apenas a classificação por estrelas sem comentários no campus, independentemente de haver comentário por escrito ou não.'
app.survey.post_nps.enabled.name: Autopublicação
app.survey.post_nps.enabled.description: 'O comentário ativado é publicado automaticamente, desativado requer validação do administrador'
setting.lite_evolution.segmentedHours.description: Horas
setting.course.tab.person.description: 'Habilitada, a seção “Pessoas” é ativada no nível de detalhe de um curso.'
setting.course.showDeactivatedCourses.name: 'Visualizando cursos desativados (Campus)'
setting.course.showDeactivatedCourses.description: 'Cursos ativados e desativados (em cinza) são exibidos no campus'
catalog.19.name: 'Administrador de traduções'
catalog.19.description: 'Traduções do administrador'
setting.lenguage.platform: 'Traduções de Administrador'
setting.module.announcement.name: 'Convite à apresentação de candidaturas'
setting.module.announcement.description: 'Habilitado ativa o módulo “Chamadas” na plataforma'
course.diploma.index: 'Índice de conteúdos'
setting.zip.day_available_until.name: 'Dias disponíveis'
setting.zip.day_available_until.description: 'Número de dias disponíveis antes de o zip ser automaticamente eliminado.'
catalog.20.name: 'Chamada de campos extra'
course.diploma.filters: 'Ativar filtros adicionais no relatório do diploma'
setting.lenguage.platform.description: 'Línguas disponíveis no painel do administrador'
translations_admin.title1: 'Formação atribuída'
translations_admin.title2: 'Formação complementar'
translations_admin.title3: 'Cursos atribuídos'
translations_admin.title4: 'Cursos de voluntariado'
setting.course.tab.stats.description: 'Habilitada, a seção “Estatísticas” é ativada no nível de detalhe de um curso.'
setting.course.tab.options.description: 'Habilitada, a seção “Opiniões” é ativada no nível de detalhe de um curso.'
course.diploma.index.description: 'Habilitada, a seção "Diplomas" é ativada ao criar/modificar um curso'
setting.use.filter_in_ranking.name: 'Utilizar filtros na classificação dos utilizadores'
setting.use.filter_in_ranking.description: 'Permite-lhe selecionar no menu os filtros das categorias com que um utilizador pretende ser comparado. Se esta opção estiver desactivada, o utilizador será comparado, por defeito, com todos os filtros disponíveis na plataforma'
setting.use.include_only_first_category_name: 'Mostrar apenas a primeira categoria do filtro nas classificações'
setting.use.include_only_first_category_description: 'Se estiver ativa, só é apresentada a primeira ligação do utilizador. Caso contrário, são apresentadas todas as categorias associadas. Por exemplo, se a categoria for "país", o utilizador pode estar ligado tanto a Espanha como à Nicarágua.'
setting.email_support_error.name: 'Correio de suporte para erros'
setting.email_support_error.description: 'Correios electrónicos para os quais serão enviados os incidentes da plataforma'
setting.export.task.slot_quantity.name: 'Número de slots de tarefas por utilizador'
setting.export.task.slot_quantity.description: 'Número de faixas horárias disponíveis para o processamento de tarefas de exportação por utilizador.'
setting.export.task.long_running_type_tasks.name: 'Tipos de tarefas a longo prazo'
setting.export.task.long_running_type_tasks.description: 'Lista dos tipos de tarefas que são consideradas de longa duração para exportação.'
setting.export.zip_task.slot_quantity.name: 'Número de slots de tarefas zip por utilizador'
setting.export.zip_task.slot_quantity.description: 'Número de slots disponíveis para processar tarefas de compressão zip por utilizador.'
setting.export.zip_task.long_running_type_tasks.name: 'Tipos de tarefas zip de longa duração'
setting.export.zip_task.long_running_type_tasks.description: 'Lista dos tipos de tarefas de fecho de correr que são consideradas de longa duração.'
setting.export.task.user_pending_max_count_task.name: 'Número máximo de tarefas pendentes por utilizador'
setting.export.task.user_pending_max_count_task.description: 'Número máximo de tarefas pendentes que um utilizador pode ter em fila de espera.'
setting.export.task.timeout.name: 'Limite de tempo para as tarefas'
setting.export.task.timeout.description: 'Tempo máximo em segundos antes de uma tarefa de exportação ser considerada expirada.'
setting.export.zip_task.timeout.name: 'Limite de tempo para tarefas zip'
setting.export.zip_task.timeout.description: 'Tempo máximo em segundos antes de uma tarefa de compressão zip ser considerada expirada.'
setting.export.task.timeout_seconds.name: 'Tempo de espera para tarefas no estado TIMEOUT'
setting.export.task.timeout_seconds.description: 'Tempo máximo em segundos após o qual uma tarefa no estado TIMEOUT deixa de ser considerada em execução.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'É o diploma personalizado da Novomatic'
app.announcement.managers.sharing.name: 'Permitir a criação de tarefas num convite à apresentação de propostas'
app.announcement.managers.sharing.description: 'Permitir a criação de tarefas num convite à apresentação de propostas'
