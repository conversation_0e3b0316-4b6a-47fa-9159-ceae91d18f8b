chapter.quiz.description: '<p>You will then be asked a series of questions with several possible answers, only one of which is correct. You have a limited amount of time to answer all the questions.</p><p>The score you get will depend on the number of correct answers and the time you have left once you have answered all the questions in the quiz. Therefore, your goal is to choose the correct option as quickly as possible. </p><p>Go for it! </p>'
chapter.puzzle.description: "<p>Sort and rotate the pieces until they are in the correct position and orientation, otherwise they will not fit together.</p><p>At the top there are four segments that correspond to the time available to complete the puzzle. When a time segment runs out, one of the questions we have entered is asked. Getting the questions right gives you more time to solve the puzzle. The final score will depend on a combination of the time taken to complete the puzzle itself, the number of correct questions answered and the number of questions missed. </p>\n\n"
chapter.roulette.description: '<p>Spin the wheel and correctly answer the question in the corresponding box.</p><p>To pass the wheel you need to get a correct answer in each category at the same time, so even if you have already got a correct answer in one category, don''t relax! If you fall back into that section and fail the new question, you will have to start again.</p><p>The final score will depend on the number of questions you have had to answer to complete the roulette and the proportion of correct answers.</p><p>Go for it!</p>'
chapter.trueorfalse.description: '<p>A series of statements/items will then be displayed and you must decide whether the answer is true or false. </p><p>To do this, drag the card to the "True" or "False" box according to your choice.</p><p>Good luck!</p>'
chapter.adivinaImagen.description: "<p>In this game, you will have to test your skills to solve a riddle before time runs out. There is a clue hidden behind a blurred image that will gradually come into focus as you progress through the game. In addition to the image, there is also additional help in the form of text. You will have to be quick in giving your answer to get as many points as possible. Keep in mind that if you run out of time, you will lose the opportunity to answer.</p><p>Get playing!</p>\n"
chapter.ordenarMenorMayor.description: '<p>In this game you will have to arrange the elements, click on the words you want to interchange with each other and get the right position to complete the challenge.</p><p>Good luck!</p>'
chapter.Parejas.description: '<p>The objective is to find matching pairs. The location of the cards is randomized.</p>'
chapter.categorize.description: '<p>Demonstrate your classification skills in this brain teaser game. Match the words, phrases or concepts with the corresponding family or group. To do this drag the card to the correct option, but be careful, think carefully before you choose because mistakes carry a time penalty.</p><p>Play now and beat the challenge!</p>'
chapter.guessword.description: '<p>In this game you will be presented with a puzzle to solve. The task will be to examine the letters provided to sort them and figure out the correct word. But beware, time is limited, so you will need to be quick and accurate in order to win.</p><p>Go for it!</p>'
chapter.wordle.description: '<p>In this game, you will have to guess a hidden word in a maximum of 6 tries. After each attempt, the game will give you hints indicating how many letters in your word are correct and in the correct position. It will also tell you how many letters are correct but are not in the correct position. Watch the color changes in the boxes to identify the correct letters and their positions. </p><p>Good luck! </p>'
chapter.lettersoup.description: '<p>In this game, you will have to find all the words that appear in the list on the right. To do this, you will have to look for them in the alphabet soup both horizontally, vertically and diagonally. In addition, the words can be hidden in both directions. Time is limited, so you will have to be quick and accurate to find them all.remember that the sooner you find them, the more points you will get. </p><p>Are you ready for this challenge? Go ahead and good luck!</p>'
chapter.videoquiz.description: '<p>In this video quiz, you will have the opportunity to test your knowledge while enjoying interesting audiovisual content. As the video progresses, you will be presented with questions related to the content you have just watched. If you answer correctly, the video will continue to advance, but if you fail, the video will go backwards. Pay attention and select the answer option you think is correct to move forward. Enjoy the video while demonstrating what you know!</p>'
chapter.fillgaps.description: '<p>After that, a text will appear on the screen that you must read carefully. The text contains several gaps that you must fill in by selecting the most appropriate answer option for each one. Don''t stop, go ahead!</p>'
chapter.default.finish.ok: '<p><b>Congratulations! </b></p><p>You have met the challenge.</p>'
chapter.default.finish.ko: '<p><b>We''re sorry. </b></p><p>You have not passed the challenge. </p><p>Please try again!</p>'
chapter.hide-word.description: '<p>The game consists of discovering the hidden word that is hidden behind the image. To do this you must select the letters that make up the word before the time runs out, but be careful because if you choose an incorrect letter, you will be penalized with less time.</p><p>Go ahead!</p>'
chapter.x2.description: '<p>The game consists of discovering the hidden word that is hidden behind the image. To do this you must select the letters that make up the word before the time runs out, but be careful because if you choose an incorrect letter, you will be penalized with less time.</p><p>Go ahead!</p>'
chapter.label_in_plural: Chapters
chapter.configureFields.title: Title
chapter.configureFields.course: Course
chapter.configureFields.type: Type
chapter.configureFields.season: Season
chapter.configureFields.description: Description
chapter.configureFields.image: Image
chapter.configureFields.image_file: 'Image file'
chapter_type.description.10: '<p>Chapter of the slider type with images.</p>'
chapter_type.add.10: 'Add slider'
chapter_type.all: All
chapter_type.content: Theory
chapter_type.games_test: Assessment
chapter_type.description_test: 'Test description'
chapter_type.type: 'Chapter type'
chapter.add_pdf: 'Add PDF'
chapter.chapter.show_video: 'Show video'
chapter.message_pdf_success: 'The PDF has been added successfully'
chapter.message_pdf_error: 'An error occurred while saving the PDF'
chapter.chapter.materials: Materials
chapter.chapter.show_pdf: 'View PDF'
chapter_type.10: Slider
chapter.configureFields.create_chapter: 'Create chapter'
chapter_type.description.SCORM: '<p>The Scorm chapter is very interesting:</p><p>It allows us to upload very diverse content generated with other tools, such as documents, interactive content and even games.</p>'
chapter_type.description.CONTENT: '<p>This is one of the most versatile chapters.</p><p>On the left side are shown the titles entered, which serve as an index to find content quickly and facilitate reading.</p>'
chapter_type.description.ROULETTE: '<p>It is a quiz game that adds a random component, since you have to complete the segments of a roulette to pass it.</p><p>It is based on the creation of a battery of questions to reinforce the knowledge learned. You can enter as many questions as you want and also accompany them with images.</p><p>For the proper functioning of the game it is advisable to include a minimum of 10 questions.</p>'
chapter_type.description.DOUBLE_OR_NOTHING: '<p>This game features a series of questions that include an additional risk factor. After each question, participants have the option to stand and stay with the current score, or risk answering an additional question for more points. However, if answered incorrectly, all points accumulated up to that point are forfeited. Please note that this game does not include a risk factor.</p>'
chapter_type.description.QUIZ: '<p>This is the most classic game chapter.</p><p>The idea is to create a battery of questions to reinforce the knowledge learned. You can enter unlimited questions accompanied by a picture and with only one correct answer.</p>'
chapter_type.description.PUZZLE: '<p>Sort and rotate the pieces until they are in the correct position and orientation, otherwise they will not fit together.</p><p>At the top there are four segments that correspond to the time available to complete the puzzle. When a time segment runs out, one of the questions we have entered is asked. Getting the questions right gives you more time to solve the puzzle. The final score will depend on a combination of the time taken to complete the puzzle itself, the number of correct questions answered and the number of questions missed.</p>'
chapter_type.description.HIDE_WORDS: '<p>This game poses a puzzle based on a picture. To solve it you have to select the right letters carefully before the time runs out.</p>'
chapter_type.description.PDF: '<p>Since the PDF format is widely used for different types of content, such as protocols or manuals, PDF chapters are very interesting, since they allow us to reuse already published materials.</p>'
chapter_type.description.VIDEO: '<p>Audiovisual resources have a great pedagogical potential to attract, capture attention and arouse curiosity.</p><p>The platform allows us to choose how to enter the video via "url", or by selecting a file that is on our computer. In the latter case we can attach a subtitle file.</p>'
chapter_type.description.LETTERS_WHEEL: '<p>The game consists of matching the words, each of which corresponds to a letter on the wheel. Sometimes the solution will be a word that begins with the letter and sometimes it will simply contain the letter.</p>'
chapter_type.description.TRUE_OR_FALSE: '<p>In this game there will be a series of questions in text format, image or a combination of both posed as statements. There are two possible answers "True" or "False" and only one is correct. The time to solve the game is limited.</p>'
chapter_type.description.HIDDEN_PICTURE: '<p>In this game, you will have to solve a riddle before time runs out. The clue will be hidden behind a blurred image that will gradually come into focus as you progress through the game. In addition to the image, there is also additional help in the form of text. In addition to the image, there is also additional help in the form of text.</p>'
chapter_type.description.HIGHER_LOWER: '<p>In this classic game, you will have to arrange the elements by dragging the blocks to place them in the correct order. The diversity of possibilities makes it ideal for mathematical exercises and other educational challenges. Ideal for creating a test that challenges reasoning and sorting skills.</p>'
chapter_type.description.MEMORY_MATCH: '<p>This game is ideal for training memory and concentration. The goal is to find all pairs of matching cards. The location of the cards is created randomly, so each game will be different.</p>'
chapter_type.description.CATEGORIZED: '<p>In this game you will be presented with a series of words, phrases or concepts that you must associate with the corresponding family or group shown below. You will test your association skills and mental quickness while competing against the clock.</p>'
chapter_type.description.FILL_GAPS: '<p>In this grammar and learning game, the objective is to fill in the gaps in the sentences with the right words to test your language and grammar skills. But that''s not all! This game is versatile and can be used for many other didactic purposes.</p>'
chapter_type.description.GUESS_WORD: '<p>In this game, you will be presented with a question or riddle to solve. The task will be to carefully examine the riddle and use the letters provided to figure out the correct word. But be careful, because time is limited, which means that you will have to be quick and accurate in order to win.</p>'
chapter_type.description.WORDLE: '<p>In this game you will have to guess a hidden word in a maximum of six attempts. Each attempt will consist of entering a valid word, and after each attempt, the color of the squares will change to show which letters are correct and which are also in the correct position.</p>'
chapter_type.description.SEARCH_WORD: '<p>This game consists of finding hidden words in a word search. The goal is to mark a sequence of letters horizontally, vertically or diagonally. Words can be found in both directions, left to right or right to left. If the sequence is part of a hidden word, it will be considered as a correct answer.</p>'
chapter_type.description.VIDEO_QUIZ: '<p>During video playback, interactive questions are inserted that require the viewer to pay attention to the video content in order to answer correctly. In short, combining the power of the video with the interactivity of the quiz to deliver an effective and engaging learning experience.</p>'
chapter_type.add.SCORM: 'Add Scorm'
chapter_type.add.CONTENT: 'Adding content'
chapter_type.add.ROULETTE: 'Creating a game'
chapter_type.add.DOUBLE_OR_NOTHING: 'Creating a game'
chapter_type.add.QUIZ: 'Create a quiz'
chapter_type.add.PUZZLE: 'Creating a game'
chapter_type.add.HIDE_WORDS: 'Creating a game'
chapter_type.add.PDF: 'Add PDF'
chapter_type.add.VIDEO: 'Add video'
chapter_type.add.LETTERS_WHEEL: 'Creating a game'
chapter_type.add.TRUE_OR_FALSE: 'Creating a game'
chapter_type.add.HIDDEN_PICTURE: 'Creating a game'
chapter_type.add.HIGHER_LOWER: 'Creating a game'
chapter_type.add.MEMORY_MATCH: 'Creating a game'
chapter_type.add.CATEGORIZED: 'Creating a game'
chapter_type.add.FILL_GAPS: 'Creating a game'
chapter_type.add.GUESS_WORD: 'Creating a game'
chapter_type.add.WORDLE: 'Creating a game'
chapter_type.add.SEARCH_WORD: 'Creating a game'
chapter_type.add.VIDEO_QUIZ: 'Create quiz video'
chapter_type.SCORM: Scorm
chapter_type.CONTENT: Content
chapter_type.ROULETTE: Roulette
chapter_type.DOUBLE_OR_NOTHING: 'Double or nothing'
chapter_type.QUIZ: Quiz
chapter_type.PUZZLE: Puzzle
chapter_type.HIDE_WORDS: 'Secret word'
chapter_type.PDF: Pdf
chapter_type.VIDEO: Video
chapter_type.LETTERS_WHEEL: 'Letter wheels'
chapter_type.TRUE_OR_FALSE: 'True or False'
chapter_type.HIDDEN_PICTURE: Guess
chapter_type.HIGHER_LOWER: 'Highest to lowest'
chapter_type.MEMORY_MATCH: Couples
chapter_type.CATEGORIZED: 'Where does it fit?'
chapter_type.FILL_GAPS: 'Fills gaps'
chapter_type.GUESS_WORD: 'Sorting letters'
chapter_type.WORDLE: Enigma
chapter_type.SEARCH_WORD: 'Word Search'
chapter_type.VIDEO_QUIZ: 'Video Quiz'
chapter_type.description.VCMS: '<p>The ideal solution for creating dynamic and attractive content in your courses or training pills, presenting information visually and with a wide variety of interactions based on text, images, videos, audios, multimedia links, interactive cards, linked scenes, etc.</p> <p>The ideal solution for creating dynamic and attractive content in your courses or training pills.</p>'
chapter_type.add.VCMS: 'Creating VCMS'
chapter_type.VCMS: VCMS
chapter_type.description.ROLE_PLAY: '<p>Role-play is an activity in which participants assume and act as fictitious characters, often within a specific setting or context. During role-play, participants temporarily adopt the personality, characteristics and behaviors of the characters they represent, interacting with each other according to the circumstances and the imaginary environment established. This practice is used in a variety of contexts, such as games, therapy, educational simulations and recreational activities, with the purpose of fostering creativity, empathy, problem-solving and exploration of hypothetical situations.</p>'
chapter_type.ROLE_PLAY: Role-play
chapter_type.add.ROLE_PLAY: 'Creating role-play'
chapter_type.scorm.name: SCORM
chapter_type.scorm.description: "The Scorm chapter is very interesting:\n\nIt allows us to load very diverse content generated with other tools, for example documents, interactive content and even games."
chapter_type.contents.name: Content
chapter_type.contents.description: "This is one of the most versatile chapters.\n\nOn the left are shown the titles entered, which serve as an index to find contents quickly and facilitate reading."
chapter_type.roulette.name: Roulette
chapter_type.roulette.description: "It is a quiz game that adds a component of chance, since you have to complete the segments of a roulette wheel to pass it.\n\nIt is based on the creation of a battery of questions to reinforce the knowledge learned. You can enter as many questions as you want and also accompany them with images.\n\nFor the game to work properly, it is advisable to include a minimum of 10 questions."
chapter_type.double_nothing.name: 'Double or Nothing'
chapter_type.double_nothing.description: 'This game presents a series of questions that include an additional risk factor. After each question, participants have the option to stand and keep the current score, or risk answering an additional question for more points. However, if answered incorrectly, all points accumulated up to that point are lost.'
chapter_type.quiz.name: Quiz
chapter_type.quiz.description: "This is the most classic game chapter.\n\nThe idea is to create a battery of questions to reinforce the knowledge learned. You can enter unlimited questions accompanied by a picture and with only one correct answer."
chapter_type.puzzle.name: Puzzle
chapter_type.puzzle.description: "Arrange and rotate the pieces until they are in the correct position and orientation, otherwise they will not fit together.\n\nAt the top there are four segments that correspond to the time available to complete the puzzle. When a time segment runs out, one of the questions we have entered is asked. Getting the questions right gives you more time to solve the puzzle. The final score will depend on a combination of the time taken to complete the puzzle itself, the number of correct questions answered and the number of questions missed."
chapter_type.hidden_words.name: 'Secret Word'
chapter_type.hidden_words.description: 'This game poses a puzzle based on an image. To solve it you have to select the right letters carefully before the time runs out.'
chapter_type.pdf.name: PDF
chapter_type.pdf.description: 'Given that the PDF format is widely used for different types of content, such as protocols or manuals, PDF chapters are very interesting, since they allow us to reuse already published materials.'
chapter_type.video.name: Video
chapter_type.video.description: "Audiovisual resources have a great pedagogical potential, they attract, capture attention and awaken curiosity.\n\nThe platform allows us to choose how to introduce the video via \"url\", or by selecting a file that is on our device. In the latter case we can attach a subtitle file."
chapter_type.slider.name: Slider
chapter_type.slider.description: 'Slider type chapter with images'
chapter_type.letterswheel.name: 'Letter Wheels'
chapter_type.letterswheel.description: 'The game consists of matching the words, each of which corresponds to a letter on the wheel. Sometimes the solution will be a word beginning with the letter and sometimes it will simply contain the letter.'
chapter_type.true_false.name: 'True or FalseTrue or False'
chapter_type.true_false.description: 'In this game there will be a series of questions in text format, image or a combination of both posed as statements. There are two possible answers "True" or "False" and only one is correct. The time to solve the game is limited.'
chapter_type.hiddenpic.name: Riddle
chapter_type.hiddenpic.description: 'In this game, you will have to solve a riddle before time runs out. The clue will be hidden behind a blurred image that will gradually come into focus as you progress through the game. In addition to the image, there is also additional help in the form of text.'
chapter_type.higherlower.name: 'Highest to Lowest'
chapter_type.higherlower.description: 'In this classic game, you will have to arrange the elements by dragging the blocks to place them in the correct order. The diversity of possibilities makes it ideal for mathematical exercises and other educational challenges. Ideal for testing and challenging reasoning and sorting skills!'
chapter_type.memorymatch.name: Couples
chapter_type.memorymatch.description: 'This game is ideal to train memory and concentration. The goal is to find all pairs of matching cards. The location of the cards is created randomly, so each game will be different.'
chapter_type.categorized.name: 'Where does it fit?'
chapter_type.categorized.description: 'In this game you will be presented with a series of words, phrases or concepts that must be associated with the corresponding family or group shown below. Association skills and mental quickness will be tested while competing against the clock.'
chapter_type.fillGaps.name: 'Gap Filler'
chapter_type.fillGaps.description: 'In this grammar and learning game, the objective is to fill in the gaps in the sentences with the right words to test your language and grammar skills. But that''s not all! This game is versatile and can be used for many other teaching purposes.'
chapter_type.guessword.name: 'Sort Letters'
chapter_type.guessword.description: 'In this game, you will be presented with a question or riddle to solve. The task will be to carefully examine the riddle and use the letters provided to figure out the correct word. But be careful, because time is limited, which means that you will have to be quick and accurate in order to win.'
chapter_type.wordle.name: Enigma
chapter_type.wordle.description: 'In this game you will have to guess a hidden word in a maximum of six attempts. Each attempt will consist of entering a valid word, and after each attempt, the color of the squares will change to show which letters are correct and which are also in the correct position.'
chapter_type.searchword.name: 'Word Search'
chapter_type.searchword.description: 'This game consists of finding hidden words in a word search. The goal is to mark a sequence of letters horizontally, vertically or diagonally. Words can be found in both directions, left to right or right to left. If the sequence is part of a hidden word, it will be considered as a correct answer.'
chapter_type.videoquiz.name: 'Video Quiz'
chapter_type.videoquiz.description: 'During the playback of a video, interactive questions are inserted that require the viewer to pay attention to the video content in order to answer correctly. In short, combining the power of video with the interactivity of the quiz to deliver an effective and engaging learning experience.'
chapter_type.vcms.name: VCMS
chapter_type.vcms.description: 'The ideal solution to create dynamic and attractive content in your courses or training pills, presenting information in a visual way and with a wide variety of interactions based on text, images, videos, audios, multimedia links, interactive cards, linked scenes, etc.'
chapter_type.roleplay.name: Role-play
chapter_type.roleplay.description: 'Role-play is an activity in which participants assume and act as fictitious characters, often within a specific setting or context. During role-play, participants temporarily adopt the personality, characteristics and behaviors of the characters they represent, interacting with each other according to the circumstances and the imaginary environment established. This practice is used in a variety of contexts, such as games, therapy, educational simulations and recreational activities, with the purpose of fostering creativity, empathy, problem solving and exploration of hypothetical situations.'
chapter.chapter.show_ppt: 'View PTT'
chapter_type.ptt.description: 'Given that the PTT format is widely used for different types of content, such as protocols or manuals, PTT chapters are very interesting, since they allow us to reuse already published materials.'
chapter_type.add.PPT: 'Add PTT'
ppt.downloadable: Downloadable
chapter.message_ppt_success: 'The PTT  has been added successfully'
chapter_type.PPT: PPT
chapter.add_ppt: 'Add ppt'
chapter_type.description.PPT: '<p>Since the PPT format is widely used in different types of content, such as protocols or manuals, PPT chapters are very interesting, since they allow us to reuse already published materials'
chapter.message_ppt_error: 'Error adding PPT'
chapter.message_ppt_error_slides: 'An error occurred while processing the PPT slides.'
chapter.message_ppt_invalid_file: 'A valid file has not been received.'
chapter.message_ppt_upload_issue: 'There was a problem with the file upload.'
chapter.message_ppt_not_readable: 'The temporary file does not exist or is not readable.'
chapter.message_ppt_error_mime: 'Invalid file. Please check the allowed file types.'
chapter.message_ppt_error_cannot_open: 'The PPTX file could not be opened.'
chapter.message_ppt_success_delete: 'The PPT was correctly eliminated'
chapter_type.LTI: LTI
chapter_type.description.LTI: 'LTI description'
