report.upload.error.label_formations: 'Invalid sheet title for formaciones.'
report.upload.error.label_participants: 'Invalid sheet title for participantes.'
report.excel.headers.announcement.cod_form: 'COD. FORM'
' report.excel.headers.announcement.category': CATEGORY
report.excel.headers.announcement.category: CATEGORY
report.excel.headers.announcement.name: 'NAME OF THE TRAINING'
report.excel.headers.announcement.language: LANGUAGE
report.excel.headers.announcement.country: COUNTRY
report.excel.headers.announcement.location: 'PLACE OF DELIVERY'
report.excel.headers.announcement.start_date: 'Start date'
report.excel.headers.announcement.end_date: 'End date'
report.excel.headers.announcement.hours: HOURS
report.excel.headers.announcement.course_type: 'COURSE MODALITY'
report.excel.headers.announcement.session_type: 'SESSION MODE'
report.excel.headers.announcement.ext_int: EXT/INT
report.excel.headers.announcement.obligatory: MANDATORY
report.excel.headers.announcement.tutor: SUPPLIER/PROVIDER
report.excel.headers.announcement.tutor_email: 'E-MAIL SUPPLIER/GUARDIAN'
report.excel.headers.announcement.local_cost: 'COST LOCAL CURRENCY'
report.excel.headers.announcement.euro_cost: COST
report.excel.headers.announcement.society: SOCIETY
report.excel.headers.announcement.status: State
report.excel.headers.announcement.total_hours: 'H. TOTAL'
report.excel.headers.announcement.men: HOM.
report.excel.headers.announcement.women: WOMEN.
report.excel.headers.announcement.gender_desc: 'GENDER DESC.'
report.excel.headers.announcement.total: TOTAL
report.excel.headers.announcement.pb: PB
report.excel.headers.announcement.mi: MY
report.excel.headers.announcement.di: DI
report.excel.headers.participants.course_code: 'Course coding'
report.excel.headers.participants.employee_code: 'Cod employee'
report.excel.headers.participants.first_name: Name
report.excel.headers.participants.last_name1: Surname1
report.excel.headers.participants.last_name2: Surname2
report.excel.headers.participants.email: 'E-mail address'
report.excel.headers.participants.birth_date: 'Date of birth'
report.excel.headers.participants.attendance: '% Attendance'
report.excel.headers.participants.gender: Genre
report.excel.headers.participants.workplace: 'Work center'
report.excel.headers.participants.society: Society
report.excel.headers.participants.department: Department
report.excel.headers.participants.hours: Hs
report.excel.headers.participants.collective: Collective
report.excel.course_details: 'course details'
report.excel.course_details.title: 'Config Stats'
report.excel.course_details.config: 'Config Stats'
report.excel.course_details.config.title_config: 'Statistics Settings'
report.excel.course_details.config.period: 'Period covered'
report.excel.course_details.config.from: From
report.excel.course_details.config.until: To
report.excel.course_details.config.filter: 'Active Filters'
report.excel.course_details.general: 'General Information'
report.excel.course_details.general.id: 'Course ID'
report.excel.course_details.general.name: 'Course name'
report.excel.course_details.general.activities: 'Number of chapters'
report.excel.course_details.general.total_people: 'Total people'
report.excel.course_details.general.not_started: 'Without starting'
report.excel.course_details.general.in_progress: 'In process'
report.excel.course_details.general.finalized: Completed
report.excel.course_details.general.total_time: 'Total time'
report.excel.course_details.general.estimed: 'Estimated end time'
report.excel.course_details.general.average: 'Average rating'
report.excel.course_details.general.authorship: Authorship
report.excel.course_details.general.create_date: 'Creation date'
report.excel.course_details.chapters: 'List of chapters'
report.excel.course_details.chapters.activity: 'Chapter Id'
report.excel.course_details.chapters.name: 'Chapter name'
report.excel.course_details.chapters.type: 'Chapter Type'
report.excel.course_details.chapters.class: 'Chapter class'
report.excel.course_details.general.ending: 'of Completion'
report.excel.course_details.general.spent: 'Total time spent'
report.excel.course_details.course_statics: 'Course Statistics'
report.excel.course_details.course_statics.id_user: 'user id'
report.excel.course_details.course_statics.code: 'User code'
report.excel.course_details.course_statics.names: Names
report.excel.course_details.course_statics.surnames: Surnames
report.excel.course_details.course_statics.languages_course: 'Course language'
report.excel.course_details.training_activity: 'Training activity'
report.excel.course_details.training_activity.time_spent: 'Time spent'
report.excel.course_details.course_statics.points: Points
report.excel.course_details.course_statics.pending: 'Not started'
report.excel.course_details.course_statics.finished: Finished
report.excel.course_details.course_statics.in_progess: 'In progress'
report.excel.course_details.course_statics.completed: Completed
report.excel.course_details.course_statics.yes: 'Yes'
report.excel.course_details.course_statics.no: 'No'
report.excel.chapter_detail.evaluation_detail: 'Evaluation detail'
report.excel.chapter_detail.evaluation_detail.idquestion: 'ID question'
report.excel.chapter_detail.evaluation_detail.question: Statement
report.excel.chapter_detail.evaluation_detail.correct_answer: 'Correct answer'
report.excel.chapter_detail.evaluation_detail.success: 'of successes'
report.excel.chapter_detail.evaluation_detail.answers: Answer
report.excel.chapter_detail: 'Chapter detail'
report.excel.chapter_detail.general_info: 'General information'
report.excel.chapter_detail.general_info.items: 'Number of items'
report.excel.chapter_detail.general_info.overcoming: 'of overcoming'
report.excel.chapter_detail.evaluation_activity: 'Evaluation activity'
report.excel.chapter_detail.evaluation_activity.attemptId: 'Attempt ID'
report.excel.chapter_detail.evaluation_activity.right: Right
report.excel.course_details.config.range_date_start: 'Course started in date range'
report.excel.course_details.config.end_range_date: 'Course completed within date range'
report.excel.headers.attemps: 'Number of attempts'
report.excel.headers.announcement.COUNTRY: COUNTRY
report.excel.headers.announcement.DEMO: DEMO
report.excel.headers.announcement.timezone: 'TIME ZONE'
report.excel.chapter_detail_user: 'Chapter details'
reports.headers.itinerary_general: 'General info Itinerary'
reports.headers.id_itinerary: 'Itinerary Id'
reports.headers.itinerary_name: 'Itinerary name'
reports.headers.course_number: 'Number of courses'
reports.headers.total_person: 'Total people'
report.headers.average_time_person: 'Average time per person'
reports.headers.active: Active
report.opinions.id_survey: 'Survey ID'
report.opinions.name_survey: 'Survey name'
report.opinions.link_course_id: 'linked course id'
report.opinions.creator_survey: 'Survey creator'
report.opinions.active: Activated
report.opinions.survey_info: 'Survey information'
report.opinions.type: 'Type of survey'
report.opinions.registration_date: 'Registration date'
report.opinions.survey_activity: 'Survey Activity'
