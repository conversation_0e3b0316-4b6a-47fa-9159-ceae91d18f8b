challenges.challenge_title: 'Titre du défi'
challenges.no_images: 'Pas d''image'
challenges.import_questions_to_excel: 'Importer des questions sur Excel'
challenges.import_from_excel: 'Importer depuis Excel'
challenges.guest: Invité
challenges.add_user: 'Ajouter un utilisateur'
challenges.performance: Performance
challenges.points: Points
challenges.downloadtemplate: 'Télécharger le modèle'
challenges.import: Importation
observations.addfiles: 'Ajouter un fichier'
challenges.challenges: Défi
questions.manageresponse: 'Gérer les réponses'
questions.see_answers: 'Voir les réponses'
clone_course.title: 'Voulez-vous vraiment reproduire ce cours ?'
clone_course.content: 'Cette action crée une copie du contenu du cours.'
clone_course.clonarcourse: 'Cours de clonage'
fundae_catalogs.user_professional_category.not_label_in_plural: 'Pas de catégories professionnelles'
menu.users_managment.nomanagers: 'Aucun gestionnaire n''a été désigné'
message_api.forcingdisplaymenu: 'Forcer l''affichage du menu'
messages.notcontainchapter: 'Ce chapitre n''a pas de contenu'
messages.nologinuser: 'Utilisateur non connecté'
messages.seesummary: 'voir le résumé'
messages.e_learning: E-learning
messages.editchallenges: 'Editer le défi'
messages.challengeindex: 'Index des défis'
messages.createnew: 'Créer un nouveau'
messages.createnewchallenge: 'Créer un nouveau défi'
messages.infgroup: 'Informations sur le groupe'
message_api.scorm.score_system_desc: 'Par défaut, l''action de formation sera considérée comme réussie si l''utilisateur "termine ou réussit" toute l''activité. Si l''action de formation dispose d''un système de notation pour être réussie, activez l''option suivante :'
message_api.scorm.score_system_label: 'Score minimum (entre 0 et 100)'
message_api.scorm.allow_reset_desc: 'souhaitez-vous pouvoir consulter à nouveau l''action de formation une fois qu''elle est terminée ?'
message_api.scorm.allow_reset_label: 'Autoriser la réinitialisation'
message_api.scorm.config_label: 'Configuration :'
message_api.scorm.config_desc: 'L''activité est terminée lorsque'
