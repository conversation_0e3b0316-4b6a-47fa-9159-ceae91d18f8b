notification.itinerary.subject: 'Your personalized training itinerary is now available at %itinerary%!'
notification.itinerary.title: 'Dear %user%.'
notification.itinerary.button: 'Access your itinerary'
notification.itinerary.body: '<p>We are pleased to inform you that we have set up a personalized training path for you at [<strong>%s</strong>].<br />The [<strong>%s</strong>] path has been designed with your interests and training needs in mind to help you achieve your learning objectives.<br /><br />To access your pathway, click on the link below and follow these simple steps:</p><ol><li><li>Log in to [<strong>%s</strong>] using your login credentials.</li><li>Direct to the &quot;Itineraries&quot; section from the&nbsp;top of the&nbsp;menu&quot;.</li><li><li>&iexcl;Start exploring and enjoying your selected courses!</li</ol>'
notification.itinerary.footer: 'If you have any questions or need assistance, do not hesitate to contact our support team from the "Help" section. <br><br> <b>Make the most of your training path and success in your learning journey!'
notification.announcement.notification_user_title: 'New course announcement'
notification.announcement.notification_user_message: 'You have received an invitation to participate in the course %course% that starts on the date %startAt% and ends %endAt%.'
notification.announcement.task_approved_title: 'Task approved'
notification.announcement.task_approved_message: 'The %task% assignment of the %course% course has been approved by %user%.'
notification.announcement.task_reject_title: 'Task rejected'
notification.announcement.task_reject_message: 'The %task% assignment of the %course% course has been rejected by %user%. Check the details.'
notification.announcement.alertNoAccessCourse_title: 'Access to the course'
notification.announcement.alertNoAccessCourse_message: 'You have not yet accessed the %course% course that started on %startAt% at %startTime%'
notification.announcement.alertToFinish_title: 'Completion of the course'
notification.announcement.alertToFinish_message: 'Remember that the call for the %course% course will expire on %endAt% at %endTime% and you have not yet completed it.'
notification.announcement.alertCourseSurvery_title: 'Valuation survey'
notification.announcement.alertCourseSurvery_message: 'To finish the course %course% you must complete the evaluation survey.'
notification.announcement.alertCourseDiploma_title: 'Diploma available'
notification.announcement.alertCourseDiploma_message: 'The diploma of the %course% course is now available.'
notification.announcement.cancelation_announcement: 'Call canceled'
notification.announcement.cancelation_announcement_message: 'The %course% course with start date %startAt% and end date %endAt% has been cancelled.'
