challenges.challenge_title: 'Challenge title'
challenges.no_images: 'No Image'
challenges.import_questions_to_excel: 'Import Excel questions'
challenges.import_from_excel: 'Import from Excel'
challenges.guest: Guest
challenges.add_user: 'Add user'
challenges.performance: Performance
challenges.points: Points
challenges.downloadtemplate: 'Download template'
challenges.import: Import
observations.addfiles: 'Add file'
challenges.challenges: Challenge
questions.manageresponse: 'Manage responses'
questions.see_answers: 'See answers'
clone_course.title: 'Do you really want to duplicate this course?'
clone_course.content: 'This action will create a copy of the course contents.'
clone_course.clonarcourse: 'Clone course'
fundae_catalogs.user_professional_category.not_label_in_plural: 'No professional categories'
menu.users_managment.nomanagers: 'No managers assigned'
message_api.forcingdisplaymenu: 'Force menu display'
messages.notcontainchapter: 'This Chapter has no content'
messages.nologinuser: 'User not logged in'
messages.seesummary: 'see summary'
messages.e_learning: E-learning
messages.editchallenges: 'Edit challenge'
messages.challengeindex: 'Challenge Index'
messages.createnew: 'Create new'
messages.createnewchallenge: 'Create new Challenge'
messages.infgroup: 'Group information'
message_api.scorm.score_system_desc: 'By default the training action will be considered as passed if the user "Completes or "Passes" all the activity. If the training action has a scoring system to be passed, activate the following option:'
message_api.scorm.score_system_label: 'Minimum score (between 0 and 100)'
message_api.scorm.allow_reset_desc: 'do you want the training action to be available for consultation again once it has been passed?'
message_api.scorm.allow_reset_label: 'Allow reset'
message_api.scorm.config_label: 'Configuration:'
message_api.scorm.config_desc: 'The activity is completed when:'
