message_api.content.scorm_correctly: '<PERSON><PERSON> käynnistyi kunnolla'
message_api.content.token_not_send: 'Token<PERSON> ei ole lähe<PERSON>tty'
message_api.content.error_get_content: 'Luvun käynnistämisessä tapahtui virhe:'
message_api.content.scorm_user_not_access: '<PERSON><PERSON>yttäjällä ei ole pääsyä tälle kurssille'
message_api.content.scorm_not_content: 'Luku ei ole sisältöpaketti'
message_api.base.credentials_no_correct: 'Syötetyt tunnistetiedot eivät ole oikein.'
message_api.base.authorized_access: 'Luvaton pääsy'
message_api.base.user_successfully: '<PERSON><PERSON><PERSON>täj<PERSON> on kirjautunut onnistuneesti ulos'
message_api.base.error_login: '<PERSON><PERSON>yttäjän kirjautumisessa tapahtui virhe - Virhe:'
message_api.base.authorized_token_fail: 'Et ole päässyt sisään SSO:n kautta. Jo<PERSON> et ole vielä rekisteröinyt ja aktivoinut käyttäj<PERSON>si alustalla, sinun on tehtäv<PERSON> niin, jotta voit ottaa käyttöön tämäntyyppisen SSO-käytön.'
message_api.base.user_not_exists_in_ad: 'Väärät valtakirjat tai käyttäjää ei ole Active Directory -hakemistossa.'
message_api.controller.error_get_course: 'Käyttäjän rekisteröinnissä tapahtui virhe - Virhe:'
message_api.controller.chapter_init: 'Luku aloitettu kunnolla'
message_api.controller.error_chapter_init: 'Luvun käynnistämisessä tapahtui virhe:'
message_api.controller.error_chapter_update: 'Luku päivitetty oikein'
message_api.controller.help_text: 'Ohjetekstit on haettu onnistuneesti'
message_api.controller.course_not_found: 'Kursseja ei löytynyt'
message_api.controller.user_not_access: 'Käyttäjällä ei ole pääsyä tälle kurssille'
message_api.controller.evaluation_course: 'Arviointi toimitettu'
message_api.controller.training_itinerary: Koulutuspolkusi
message_api.controller.continue_training: Jatkokoulutus
message_api.message.not_found: 'Viestiä ei löydy'
message_api.message.not_access: 'Pääsy kielletty'
message_api.scorm.init: 'Scorm käynnistyi kunnolla'
message_api.scorm.not_package: 'Luku ei ole SCORM-paketti'
message_api.scorm.button_add_scorm: 'Lisää scorm-paketti'
message_api.scorm.button_delete_scorm: 'Poista scorm-paketti'
message_api.scorm.title_package: Scorm-paketti
message_api.scorm.upload_file_scorm: 'Valitse .zip-tiedosto'
message_api.scorm.info_upload_scorm: 'Sinun on ladattava scorm-paketti'
message_api.notification.not_found: 'Ilmoitusta ei löydy'
message_api.player.no_content: 'Tässä luvussa ei ole sisältöä'
message_api.alert.minimal_question: 'Tässä pelissä on vähintään %number% kysymyksiä.'
message_api.alert.image_puzzle: 'Palapelissä on oltava kuva'
message_api.alert.question_correct: 'Tähän kysymykseen on lisättävä oikea vastaus'
message_api.alert.chapter_content: 'Tästä luvusta puuttuu sisältö'
message_api.alert.pdf: 'Tämä luku tarvitsee pdf-tiedoston'
message_api.alert.video: 'Tästä luvusta puuttuu sisältö'
message_api.alert.slider: 'Tässä luvussa on vähintään %number% kuvia.'
message_api.alert.scorm: 'Tämä luku tarvitsee scorm-paketin'
message_api.alert.question_hidden: 'Tässä kysymyksessä saa olla vain yksi vastaus, ja sen on oltava oikea.'
message_api.alert.minimal_pair: 'Tässä luvussa edellytetään vähintään %number% pareja.'
message_api.announcement.call_for_aplications: Hakuilmoitus
message_api.alert.course_content_incomplete: 'Luvuista puuttuu tietoja, tarkistakaa ja lisätkää sisältöä, jotta ehdotuspyynnön laatimista voidaan jatkaa.'
message_api.diploma.supered: Voittaa
message_api.diploma.granted: Myönnetty
message_api.diploma.date: Päivämäärä
message_api.diploma.diploma: Tutkintotodistus
message_api.diploma.trato: Mr/Ms
message_api.diploma.nif: 'verotunnisteen kanssa'
message_api.diploma.services: 'joka työskentelee yhtiössä'
message_api.diploma.cif: alv-numero
message_api.diploma.evaluation: 'On läpäissyt koulutustoimen myönteisellä arvioinnilla.'
message_api.diploma.code: 'Koodi AF/RYHMÄ'
message_api.diploma.between: 'Päivien aikana'
message_api.diploma.duration: 'jonka kokonaiskesto on'
message_api.diploma.fomartionType: 'tuntia harjoitustilassa'
message_api.diploma.courseContent: 'Kurssin sisältö seuraavalla sivulla'
message_api.diploma.signatureSeal: 'Vastuullisen yksikön allekirjoitus ja leima <br> koulutuksen tarjoamisesta'
message_api.diploma.signatureDate: Myöntämispäivä
message_api.diploma.acreditation: Akkreditointi
message_api.diploma.signature: 'Osallistujan allekirjoitus'
message.api.diploma.type_course_default: Verkko-oppiminen
message.api.diploma.dates: 'Päivämäärä %dateFrom% - %dateTo%'
message.api.diploma.hours: '{0} %count% tuntia|{1} %count% aika|]1,Inf] %count% tuntia'
message.api.diploma.duration: Kesto
message.api.diploma.with_dni: 'kanssa %nameIdentification% %dni% %dni% %dni%'
message_api.announcement.cancelation_announcement: 'Puhelun peruuttaminen %course%'
message_api.help_category.delete_error: 'Kategoriaa %categoryName% ei voi poistaa, sillä siinä on linkitettyä sisältöä'
