nps_question.text.description: 'Danos tu opinión'
nps_question.nps.description: 'Valoración del curso'
type_course.teleformacion.name: Teleformación
type_course.teleformacion.description: 'Para los cursos de teleformación'
type_course.presencial.name: Presencial
type_course.presencial.description: 'Cursos presenciales'
type_course.mixto.name: Mixto
type_course.mixto.description: 'Es una combinación entre Teleformación y Presencial'
type_course.aula_virtual.name: 'Aula Virtual'
type_course.aula_virtual.description: 'Las clases se realizan a través de videoconferencia'
alert_type_tutor.1.name: 'La persona convocada no ha accedido al curso'
alert_type_tutor.1.description: 'Se enviará una alerta al tutor si la persona convocada no ha accedido al curso'
alert_type_tutor.2.name: 'Ha transcurrido el 50% de la convocatoria y no se ha completado el 25% de los contenidos'
alert_type_tutor.2.description: 'Se enviará una alerta al tutor si ha transcurrido el 50% de la convocatoria y no se ha completado el 25% de los contenidos'
alert_type_tutor.3.name: 'Ha transcurrido el 80% de la convocatoria y no se ha completado el 50% de los contenidos'
alert_type_tutor.3.description: 'Se enviará una alerta al tutor si ha transcurrido el 80% de la convocatoria y no se ha completado el 50% de los contenidos'
alert_type_tutor.4.name: 'Quedan pocos días para el final de la convocatoria y no se ha completado el curso'
alert_type_tutor.4.description: 'Se tienes que valorar la cantidad de días que se considera pocos días'
alert_type_tutor.5.name: 'La persona convocada ha completado el curso pero no ha respondido la encuesta.'
alert_type_tutor.5.description: 'Si la plataforma tiene encuestas, se enviará una alerta al tutor si la persona convocada ha completado el curso pero no ha respondido la encuesta'
alert_type_tutor.6.name: 'La persona convocada ha completado el curso pero no ha descargado el diploma'
alert_type_tutor.6.description: 'Se enviará una alerta al tutor si la persona convocada ha completado el curso pero no ha descargado el diploma.'
announcement_configuration_type.temporalizacion.name: Temporalización
announcement_configuration_type.temporalizacion.description: 'Para facilitar el seguimiento del curso, asignaremos un tiempo a cada bloque de contenidos y o actividades, pudiendo así detectar cuáles de los participante llevan un ritmo de trabajo adecuado o se están rezagando en el proceso de formación'
announcement_configuration_type.curso_bonificado.name: 'Curso bonificado'
announcement_configuration_type.curso_bonificado.description: 'Los cursos bonificados son aquellos que se realizan a través de la Fundación Tripartita y que son financiados por las empresas a través de las cotizaciones a la Seguridad Social.'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'El chat es una herramienta de comunicación síncrona que permite a los participantes de un curso interactuar en tiempo real, a través de mensajes de texto.'
announcement_configuration_type.notificaciones.name: Notificaciones
announcement_configuration_type.notificaciones.description: 'Las notificaciones son mensajes que se envían a los participantes de un curso para informarles de novedades o eventos importantes.'
announcement_configuration_type.mensajeria.name: Mensajería
announcement_configuration_type.mensajeria.description: 'Una mensajería es un sistema de comunicación que permite a los participantes de un curso enviar y recibir mensajes privados.'
announcement_configuration_type.foros.name: Foros
announcement_configuration_type.foros.description: 'Un foro es un espacio de comunicación asíncrona que permite a los participantes de un curso intercambiar mensajes sobre un tema determinado.'
announcement_configuration_type.diploma.name: Diploma
announcement_configuration_type.diploma.description: 'Los diplomas son certificados que se entregan a los participantes de un curso para acreditar su realización.'
announcement_configuration_type.tutor_alerts.name: 'Activar alertas para el tutor'
announcement_configuration_type.tutor_alerts.description: 'Las alertas son mensajes que se envían al tutor de un curso para informarle de novedades o eventos importantes.'
announcement_configuration_type.encuesta_satisfaccion.name: 'Encuesta de satisfacción'
announcement_configuration_type.encuesta_satisfaccion.description: 'Las encuestas de satisfacción son cuestionarios que se envían a los participantes de un curso para conocer su opinión sobre el mismo.'
announcement_configuration_type.finalizar_convocatoria.name: 'El curso permanecerá activo al finalizar la convocatoria'
announcement_configuration_type.finalizar_convocatoria.description: 'El usuario podrá acceder al contenido del curso una vez finalizada la convocatoria.'
announcement_configuration_type.firma_digital.name: 'Firma digital'
announcement_configuration_type.firma_digital.description: 'La firma digital es nesesaria para poder firmar la asistencia a un curso presencial.'
announcement_configuration_type.gestion_costes.name: 'Gestión de costes'
announcement_configuration_type.gestion_costes.description: 'La gestión de costes permite en los grupos indicar el coste de la convocatoria.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Habilitar notificaciones por correo.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Habilitar notificaciones normales'
announcement_configuration_type.objetivos_contenidos.name: 'Objetivos y contenidos'
announcement_configuration_type.objetivos_contenidos.description: 'Los objetivos y contenidos del curso se incluirán en el diploma.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'El dni del alumno se incluirá en el diploma'
announcement_configuration_type.template_excel.name: 'Plantilla de matriculación excel'
announcement_configuration_type.template_excel.description: 'Esta plantilla se utilizará para la matriculación del alumnado, usando el Código HRBP, en vez del DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Permitir que el tutor pueda descargar reportes de los grupos en formato ZIP'
announcement_criteria.1.name: 'Mínimo de capítulos a completar'
announcement_criteria.1.description: 'La nota minima puede ser por ejemplo el 70 sobre 100'
announcement_criteria.2.name: 'Completar tareas'
announcement_criteria.2.description: 'Controles de evaluación'
announcement_criteria.3.name: 'Tiempo máximo de inactividad'
announcement_criteria.3.description: 'Por ejemplo el usuario no puede estar más de 10 minutos sin actividad'
announcement_criteria.4.name: 'Completar actividades'
announcement_criteria.4.description: 'El usuario debe completar las actividades propuestas'
announcement_criteria.5.name: 'Horas de formación completadas'
announcement_criteria.5.description: 'Por ejemplo sí, el programa tiene 20 horas, el usuario debe completar las 20 horas'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Primer paso de la creación de la convocatoria'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Segundo paso de la creación de la convocatoria, donde se rellena la información general'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Este paso depende si el cliente tiene activado la bonificación'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'En este se agregan los alumnos a la convocatoria, que pueden estar asignado a un grupo'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'En este paso se configura los grupos de alumnos que se han creado en el paso anterior'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'En este paso se configura la encuesta que se le enviará a los alumnos'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'En este paso se configura la encuesta que se le enviará a los alumnos'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'En este paso se muestras los diplomas disponibles en la plataforma para que el cliente seleccione el que desee'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Estas alertas son especiales para informar al tutor de los acontecimientos de la convocatoria'
class_room_virtual.zoom.name: Zoom
class_room_virtual.zoom.description: 'Plataforma online de conferencia web, permite realizar video-llamadas en alta definición, con la funcionalidad de compartir escritorio, pizarra, chat, grabar la conferencia, compartir documentos, y poder acceder desde cualquier lugar ya que está disponible para dispositivos móviles.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'La plataforma de ClickMeeting es una de las interfaces de webinars más fáciles de usar del mercado y ofrece infinidad de opciones de personalización flexibles'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Solución de Código Abierto para realizar videoconferencias con cifrado en sus conexiones y disponible para varios sistemas operativos'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Software de videoconferencia de código abierto fácil de integrar y altamente personalizable'
configuration_cliente_announcement.COMMUNICATION.description: 'Esto permite habilitar las comunicaciones dentro de la convocatoria'
configuration_cliente_announcement.CERTIFICATE.description: 'Esto permite habilitar la descarga de diplomas dentro de la convocatoria'
configuration_cliente_announcement.SURVEY.description: 'Esto permite habilitar las encuestas, para quedar disponibles después en la convocatoria'
configuration_cliente_announcement.ALERT.description: 'Esto permite habilitar las alertas, para ser heredadas en el apartado alertas del tutor'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Permite habilitar la temporalización de los capítulos dentro de una convocatoria'
configuration_cliente_announcement.BONIFICATION.description: 'Bonificación de la convocatoria, especial para la convocatoria de la fundación tripartita'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Permite habilitar el acceso al contenido de la convocatoria una vez finalizada'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Permite habilitar la firma digital en la convocatoria, sobre todo en cursos presenciales'
configuration_cliente_announcement.COST.description: 'Permite habilitar que los clientes imputar costes a la convocatoria.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Notificaciones al activar convocatoria (email, notificación front)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Esta configuración es especial para el cliente Iberostar, para no afectar el flujo de fundae'
configuration_cliente_announcement.REPORT.description: 'Permitir generar reportes en las convocatorias'
type_course_announcement_step_creation.seleccionar_curso.name: 'Seleccionar curso'
type_course_announcement_step_creation.seleccionar_curso.description: 'Seleccionar el curso al que se le va a crear la convocatoria'
type_course_announcement_step_creation.convocatoria.name: Convocatoria
type_course_announcement_step_creation.convocatoria.description: 'Información de la convocatoria'
type_course_announcement_step_creation.bonificacion.name: Bonificación
type_course_announcement_step_creation.bonificacion.description: 'Información de la convocatoria'
type_course_announcement_step_creation.alumnado.name: Alumnado
type_course_announcement_step_creation.alumnado.description: 'Se agregan los alumnos al curso'
type_course_announcement_step_creation.grupos.name: Grupos
type_course_announcement_step_creation.grupos.description: 'Se detallan información de los grupos y tambien se agrega tutor'
type_course_announcement_step_creation.comunicacion.name: Comunicación
type_course_announcement_step_creation.comunicacion.description: 'Este paso va ir en dependencia, si el cliente tiene activado la comunicación o no.'
type_course_announcement_step_creation.encuesta.name: Encuesta
type_course_announcement_step_creation.encuesta.description: 'Este paso va ir en dependencia, si el cliente tiene activado la encuesta o no.'
type_course_announcement_step_creation.diploma.name: Diploma
type_course_announcement_step_creation.diploma.description: 'Este paso depende de si el cliente tiene activado el diploma o no.'
type_course_announcement_step_creation.alertas.name: Alertas
type_course_announcement_step_creation.alertas.description: 'Esto puede ir en dependencia de la configuración del cliente.'
type_diploma.easylearning.name: Default
type_diploma.easylearning.description: 'Es el diploma de la empresa cliente'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'Es el diploma de Fundae'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'Es el diploma Hobetuz'
type_money.euro.name: Euro
type_money.euro.country: España
type_money.dolar_estadounidense.name: 'Dólar estadounidense'
type_money.dolar_estadounidense.country: 'Estados Unidos'
section_default_front.mi_formacion.name: 'Mi Formación'
section_default_front.mi_formacion.description: 'Dentro la formación asignada, podrás encontrar todos los cursos que tienes asignados.'
section_default_front.formacion_adicional.name: 'Formación adicional'
section_default_front.formacion_adicional.description: 'Dentro de esta sección podrás encontrar todos los curso de campus abierto.'
section_default_front.formacion_asignada.name: 'Formación asignada'
section_default_front.formacion_asignada.description: 'Dentro de esta sección podrás encontrar todos los cursos que tienes asignados.'
setting.multi_idioma.name: Multi-idioma
setting.multi_idioma.description: 'Habilitado permite una interfaz en múltiples idiomas'
setting.default_lenguage.name: 'Idioma por defecto en campus'
setting.default_lenguage.description: 'Lenguaje de interfaz de usuario predeterminado en el campus'
setting.languages.name: 'Idiomas disponibles'
setting.languages.description: 'Idiomas disponibles en la plataforma'
setting.registro_libre.name: 'Automatriculación '
setting.registro_libre.description: 'Habilitado permite a los usuarios automatricularse en el campus.'
setting.opinion_plataforma.name: 'Visualización de encuestas en campus'
setting.opinion_plataforma.description: 'Habilitado permite visualizar la encuesta al finalizar un curso en el campus y deshabilitado se omite la encuesta y se genera el diploma.'
setting.validacion_automatica.name: 'Autovalidación de automatrícula'
setting.validacion_automatica.description: 'Habilitado el usuario no necesita que el administrador le valide con la automatriculación'
setting.filtros_plataforma.name: 'Asignación por filtros'
setting.filtros_plataforma.description: 'Habilitado se activa el funcionamiento del sistema de asignación por filtros'
setting.itinearios_plataforma.name: 'Módulo de itinerarios'
setting.itinearios_plataforma.description: 'Habilitado se activa el módulo "Itinerarios" y aparece la pestaña'
setting.seccion_cursos.name: 'Orden de apartados en un curso'
setting.seccion_cursos.description: 'La selección activa a nivel de campus estos apartados en curso'
setting.set_points_course.name: 'Puntos personalizados'
setting.set_points_course.description: 'Habilitado se activa el campo "Puntos" en cada curso de forma personalizada'
setting.default_points_course.name: 'Puntos por defecto '
setting.default_points_course.description: 'Este valor se tomará por defecto a la hora de otorgar la puntuación en la finalización de un curso. Funciona repartiendo el 50% entre los capítulos teóricos y 50% entre los capítulos evaluativos.'
setting.documentation_course.name: 'Información general '
setting.documentation_course.description: 'Habilitado activa el campo de texto "Información general" en la creación de un curso'
setting.open_course.name: 'Campus abierto'
setting.open_course.description: 'Habilitado aparece la posibilidad de activar/desactivar el campus abierto '
setting.client_id.name: 'Id cliente vimeo'
setting.client_id.description: 'Es el identificador del cliente de Vimeo Gestionet'
setting.client_secret.name: 'Cliente secreto de vimeo'
setting.client_secret.description: 'Cliente secreto de vimeo Gestionet'
setting.access_token.name: 'Token de acceso'
setting.access_token.description: 'Token de acceso Gestionet'
setting.user_id.name: 'Id cliente'
setting.user_id.description: 'Id del usuario registrado en vimeo'
setting.project_id.name: 'Carpeta de capítulos tipo vídeo'
setting.project_id.description: 'Es el identificador donde se alojan los recursos de los capítulos tipo videos'
setting.project_id_resource_course.name: 'Carpeta de recursos de materiales (convocatoria)'
setting.project_id_resource_course.description: 'Es el identificador de la carpeta donde se alojan aquellos videos relacionado con las materiales del curso y convocatoria'
setting.project_id_task_course.name: 'Carpeta de recursos de tareas'
setting.project_id_task_course.description: 'Es el identificador de la carpeta donde se alojan aquellos videos relacionado con las tareas del curso y convocatoria'
setting.project_id_video_Quiz.name: 'Carpeta de recursos de videoquiz'
setting.project_id_video_Quiz.description: 'Es el identificador de la carpeta donde se alojan aquellos videos relacionado con el juego videoquiz'
setting.project_id_Roleplay.name: 'Carepta de recursos de roleplay'
setting.project_id_Roleplay.description: 'Identificado para los recursos de tipo video en el roleplay'
setting.upload_sudomain.name: 'Cargar en subdominio'
setting.upload_sudomain.description: 'Esta variable se utiliza para cargar videos y archivos SCORM, permitiendo superar las restricciones de Cloudflare de 100 Mb'
setting.from_email.name: 'Correo electrónico remitente'
setting.from_email.description: 'Es el origen de los correos electrónicos que se envían desde la plataforma'
setting.from_name.name: 'Nombre del remitente'
setting.from_name.description: 'El nombre del remitente que se muestra en los correos electrónicos'
setting.from_cif.name: CIF
setting.from_cif.description: 'CIF de la empresa'
setting.email_support.name: 'Email de soporte'
setting.email_support.description: 'Estos correos se utiliza para enviar notificaciones de soporte'
setting.email_support_register.name: 'Recepción del administrador de correo electrónico'
setting.email_support_register.description: 'Este correo se utiliza para recibir las solicitudes de registro en la plataforma'
setting.news.name: Noticias
setting.news.description: 'Habilitado activa el modulo de "Noticias" en la plataforma '
setting.foro.name: Foro
setting.foro.description: 'Habilitado activa el modulo de "Foro" en la plataforma '
setting.desafios.name: Duelos
setting.desafios.description: 'Habilitado activa el modulo de "Duelos" en la plataforma '
setting.secciones.name: Secciones
setting.secciones.description: 'Habilitado se muestran las secciones en el front'
setting.encuestas.name: Encuestas
setting.encuestas.description: 'Habilitado activa el modulo de "Encuestas" en la plataforma '
setting.active_cron_exports.name: 'Compilación de informe en espera'
setting.active_cron_exports.description: 'Habilitado el informe se genera y se pone en cola de espera, y deshabilitado la descarga es directa.'
setting.gender_excel.name: 'Campo "Género" en xls'
setting.gender_excel.description: 'Habilitado activa la columna "Género" en los informes xls de descarga'
setting.code.name: 'Campo "Código" en xls'
setting.code.description: 'Habilitado activa la columna "Código" en los informes xls de descarga'
setting.finished_chapters.name: 'Capítulos finalizados'
setting.finished_chapters.description: 'Habilitado activa los "Capítulos finalizados" en los informes xls de descarga'
setting.zoom_cliente_id.name: 'Id del cliente de Zoom'
setting.zoom_cliente_id.description: 'Id del cliente de Zoom--necesario para utilizar la API de zoom'
setting.zoom_cliente_secret.name: 'Client Secret de Zoom'
setting.zoom_cliente_secret.description: 'clave del cliente de Zoom--necesario para utilizar la API de zoom'
setting.zoom_account_id.name: 'Id de cuenta de Zoom'
setting.zoom_account_id.description: 'Número de cuenta del cliente de Zoom--necesario para utilizar la API de zoom'
setting.zoom_email.name: 'Email de Zoom'
setting.zoom_email.description: 'correo del cliente de Zoom--necesario para utilizar la API de zoom'
setting.clickmeeting_api_key.name: 'Llave de API de ClickMeeting'
setting.clickmeeting_api_key.description: 'Id del cliente de ClickMeeting-- necesario para utilizar el API de ClickMeeting'
setting.clikmeeting_dirbase.name: 'Directorio base de ClickMeeting'
setting.clikmeeting_dirbase.description: 'Dirección del servidor de ClickMeeting'
setting.clikmeeting_events_paralel.name: 'Eventos paralelos de ClickMeeting'
setting.clikmeeting_events_paralel.description: 'Cantidad de eventos paralelos contratados'
setting.plugnmeet_serverurl.name: 'Url del servidor de plugNmeet'
setting.plugnmeet_serverurl.description: 'Dirección del servidor de plugNmeet'
setting.plugnmeet_api_key.name: 'Llave de API de plugNmeet'
setting.plugnmeet_api_key.description: 'Id del cliente de plugNmeet'
setting.plugnmeet_secret.name: 'Clave secreta de plugNmeet'
setting.plugnmeet_secret.description: 'Clave del cliente de plugNmeet'
setting.plugnmeet_analyticsurl.name: 'Analiticas URL de plugNmeet'
setting.plugnmeet_analyticsurl.description: 'Dirección del servidor plugNmeet para analiticas'
setting.zoom_urlreports.name: 'Url de reportes de Zoom'
setting.zoom_urlreports.description: 'Dirección donde se guardan los reportes de zoom'
setting.plugnmeet_urlreports.name: 'Url de reportes de plugNmeet'
setting.plugnmeet_urlreports.description: 'Dirección donde se guardan los reportes de plugNmeet'
setting.clickmeeting_urlreports.name: 'Url de reportes de ClickMeeting'
setting.clickmeeting_urlreports.description: 'Dirección donde se guardan los reportes de ClickMeeting'
setting.library_enabled.name: Hemeroteca
setting.library_enabled.description: 'Habilitado activa el modulo de "Hemeroteca" en la plataforma '
setting.library_audio_local.name: 'Audio local'
setting.library_audio_local.description: 'Audio local '
setting.library_audio_path.name: 'Ruta de audio '
setting.library_audio_path.description: 'Ruta de audio '
setting.library_file_path.name: 'Ruta de archivo '
setting.library_file_path.description: 'Ruta de archivo '
setting.library_data_page_size.name: 'Tamaño de página de datos '
setting.library_data_page_size.description: 'Tamaño de página de datos '
setting.library_comments.name: 'Comentarios '
setting.library_comments.description: 'Comentarios '
setting.challenge_loops.name: 'Nº de preguntas por duelo'
setting.challenge_loops.description: 'Número de preguntas por defecto que forman parte de un duelo.'
setting.points_for_win.name: 'Puntos de victoria'
setting.points_for_win.description: 'Puntos que se otorgan por ganar un duelo'
setting.points_for_lose.name: 'Puntos por derrota'
setting.points_for_lose.description: 'Puntos que se restan por perder un duelo'
setting.points_fortie.name: 'Puntos por empate'
setting.points_fortie.description: 'Puntos que se otorgan por empatar en un duelo con aciertos'
setting.points_corrects.name: 'Puntos por empate a cero'
setting.points_corrects.description: 'Puntos que se otorgan por empatar un duelo con cero aciertos'
setting.points_for_left.name: 'Puntos por abandono'
setting.points_for_left.description: 'Puntos otorgados por abandonar el duelo'
setting.total_duels.name: 'Nº de duelos máximo'
setting.total_duels.description: 'Número de duelos máximos disponibles por cada persona participante. '
setting.seconds_per_question.name: 'Segundos disponibles por pregunta'
setting.seconds_per_question.description: 'Tiempo expresado en segundos, disponible para responder cada una de las preguntas.'
setting.user_dni.name: 'DNI de usuario'
setting.user_dni.description: 'Habilitado en la creación/modificación de usuario aparece el campo de "DNI"'
setting.edit_code.name: 'Código de usuario'
setting.edit_code.description: 'Habilitado en la creación/modificación de usuario aparece el campo de "Código"'
setting.stats_acumulative.name: 'Estadísticas acumulativas'
setting.stats_acumulative.description: 'Esto en caso de que se quiera que las estadísticas sean acumulativas'
setting.maximo_fechas.name: 'Rango de fechas máximo'
setting.maximo_fechas.description: 'Número de días máximos permitidos para la consulta de data'
setting.maximo_horas.name: 'Máximo de peticiones por hora'
setting.maximo_horas.description: 'Máximo de peticiones permitidas por hora'
setting.maximo_dia.name: 'Máximo de peticiones por día'
setting.maximo_dia.description: 'Máximo de peticiones permitidas por día'
setting.fundae.name: Fundae
setting.fundae.description: 'Si esto esta habilitado cuando se publique una convocatoria los usuarios tienes que rellenados todos los campos necesarios de la tabla users_extra_fundae'
setting.margen_entrada.name: 'Margen por defecto de entrada'
setting.margen_entrada.description: 'Margen por defecto de entrada, que se usa en el código QR'
setting.margen_salida.name: 'Margen por defecto de salida'
setting.margen_salida.description: 'Margen por defecto de salida, que se usa en el código QR'
setting.registrar_qr.name: 'Registrar con QR Sesión'
setting.registrar_qr.description: 'Si esto esta habilitado las sesiones se registraran con QR'
setting.maximo_alumnos.name: 'Número máximo de alumnos por grupo'
setting.maximo_alumnos.description: 'Número máximo de alumnos por grupo'
setting.min_score.name: 'Puntuación mínima de aprobación'
setting.min_score.description: 'Puntuación mínima de aprobación'
setting.types_action.name: 'Tipos de acción'
setting.types_action.description: 'Tipos de acción'
setting.materiales_convocatoria.name: 'Habilitar la creación de materiales en convocatoria'
setting.materiales_convocatoria.description: 'Habilitar la creación de materiales en convocatoria'
setting.tareas_convocatoria.name: 'Habilitar la creación de tareas en convocatoria'
setting.tareas_convocatoria.description: 'Habilitar la creación de tareas en convocatoria'
setting.minimo_minutos.name: 'Tiempo minimo de inactividad en minutos'
setting.minimo_minutos.description: 'Tiempo minimo de inactividad en minutos, es aplicado para los usuarios que estan en la plataforma'
setting.timezones.name: 'Zonas horarias permitidas en la convocatoria'
setting.timezones.description: 'Zona horarias que pueden ser configuradas la convocatoria'
catalog.1.name: 'Tipos de capítulos'
catalog.1.description: 'Configuración de los tipos de capítulos, que estarán disponible en la plataforma'
catalog.2.name: 'Tipos de cursos'
catalog.2.description: 'Configuración de los tipos de cursos, que estarán disponible en la plataforma'
catalog.3.name: 'Criterios de aprobación'
catalog.3.description: 'Configuración de los criterios de aprobación, que estarán disponible en la plataforma'
catalog.4.name: 'Alertas tutor'
catalog.4.description: 'Configuración de las alertas para los tutores, que estarán disponible en la plataforma'
catalog.5.name: 'Tipos de diplomas'
catalog.5.description: 'Configuración de los tipos de diplomas, que estarán disponible en la plataforma'
catalog.6.name: 'Configuración de cliente en convocatoria'
catalog.6.description: 'Configuración de los pasos a mostrar en la convocatoria'
catalog.7.name: 'Tipos de monedas'
catalog.7.description: 'Configuración de los tipos de monedas, que estarán disponible en la plataforma'
catalog.8.name: 'Grupo de configuraciones'
catalog.8.description: 'Grupo de configuraciones, que estarán disponible en la plataforma'
catalog.9.name: Configuraciones
catalog.9.description: 'Configuraciones por grupo, que estarán disponible en la plataforma'
catalog.10.name: Company
catalog.10.description: 'Compañias de usuarios, que estarán disponible en la plataforma'
catalog.11.name: 'Professional Category'
catalog.11.description: 'Categorías profesionales de los usuarios, que estarán disponible en la plataforma'
catalog.12.name: 'User Work Center'
catalog.12.description: 'Centros de trabajo para los usuarios, que estarán disponible en la plataforma'
catalog.13.name: 'User Work Department'
catalog.13.description: 'Departamentos de trabajo para los usuarios, que estarán disponible en la plataforma'
catalog.14.name: 'User Study Level'
catalog.14.description: 'Niveles de estudio de los usuarios, que estarán disponible en la plataforma'
catalog.15.name: 'Pasos para los diferentes tipos de cursos'
catalog.15.description: 'Configuración de los pasos para los diferentes tipos de cursos, que estarán disponible en la plataforma'
catalog.16.name: 'Tipos de aulas virtuales'
catalog.16.description: 'Tipos de aulas virtuales para los diferentes tipos de cursos, que estarán disponible en la plataforma'
catalog.17.name: 'Tipos de identificación'
catalog.17.description: 'Tipos de identificacion disponibles en la plataforma'
nps_question.text.name: Texto
nps_question.text.descripction: 'Danos tu opinión'
setting.help.user.name: 'Incluir pdf de ayuda en el menú de usuario'
setting.help.user.description: 'Esta ayuda se creo especialmente para iberostar'
catalog.18.name: 'Modalidades convocatorias presenciales'
catalog.18.description: 'Esto es una necesidad especial para Iberostar'
setting.userPolicies_plataforma.name: 'Politicas de privacidad'
setting.userPolicies_plataforma.description: 'Habilitado se activa el modal de "Política de privacidad " en el campus'
setting.course.tab.person.name: 'Estadísticas de curso por persona'
setting.course.tab.stats.name: "Estadísticas generales de curso \n"
setting.course.tab.opinions.name: "Opiniones por curso\n"
setting.documentation.name: Tutoriales
setting.documentation.description: 'Habilitado activa el modulo de "Tutoriales" en la plataforma donde cargar información de interés y asociarla a perfiles de administración.'
setting.user_company.name: Empresas
setting.user_company.description: 'Habilitado activa el modulo de "Empresas" en la plataforma permitiendo crear este filtro y ser asignado a un tutor en la convocatoria.'
setting.pages.name: 'Pie de página'
setting.pages.description: 'Habilitado se activa el "Pie de página" en el campus'
setting.lite_formation.name: 'Grupo de Formación en Estadísticas Generales'
setting.lite_formation.description: 'Grupo de Formación en Estadísticas Generales'
setting.lite_formation.formationHours.name: 'Horas de formación '
setting.lite_formation.formationHours.description: 'Horas totales de formación y horas de formación media por persona'
setting.lite_formation.peopleWithCourses.name: 'Personas con cursos'
setting.lite_formation.peopleWithCourses.description: 'Personas formandose actualmente y personas que han completado al menos un curso'
setting.lite_formation.courseStartedAndFinished.name: 'Cursos iniciados, en proceso y finalizados'
setting.lite_formation.courseStartedAndFinished.description: 'Número de cursos iniciados, en proceso y completados'
setting.lite_formation.requiredCourses.name: 'Cursos obligatorios'
setting.lite_formation.requiredCourses.description: 'Cursos obligatorios asignados a una convocatoria o itinerario'
setting.lite_formation.general.name: General
setting.lite_formation.general.description: General
setting.lite_formation.openedCourses.name: 'Cursos abiertos'
setting.lite_formation.openedCourses.description: 'Cursos voluntarios'
setting.lite_formation.educativeStatus.name: 'Nivel educativo'
setting.lite_formation.educativeStatus.description: 'Estatus formativo por niveles de puntos'
setting.lite_formation.gamifiedPills.name: 'Píldoras gamificadas'
setting.lite_formation.gamifiedPills.description: 'Número de capítulos gamificados, fallos y aciertos en las pruebas gamificadas'
setting.lite_formation.gamifiedTest.name: 'Píldoras test'
setting.lite_formation.gamifiedTest.description: 'Pruebas gamificadas utilizadas y aciertos y fallos por tipo de prueba'
setting.lite_formation.peoplePerformance.name: 'Rendimiento de las personas'
setting.lite_formation.peoplePerformance.description: 'Desempeño de las personas'
setting.lite_formation.coursesByStars.name: 'Cursos por puntuación'
setting.lite_formation.coursesByStars.description: 'Valoración de los cursos por estrellas'
setting.lite_formation.structureAndHotel.name: 'Departamentos y hoteles'
setting.lite_formation.structureAndHotel.description: 'Porcentaje por colectivo'
setting.lite_formation.schoolFinishedAndProgress.name: 'Escuela Terminados y en progreso'
setting.lite_formation.schoolFinishedAndProgress.description: 'Escuela con más participación, cursos en proceso y finalizados'
setting.lite_formation.coursesBySchool.name: 'Cursos por escuela'
setting.lite_formation.coursesBySchool.description: 'Número de cursos por categoría'
setting.lite_formation.coursesByDepartment.name: 'Cursos por departamento'
setting.lite_formation.coursesByDepartment.description: 'Creación de cursos por departamento'
setting.lite_formation.usersMoreActivesByCourses.name: 'Usuarios más activos por cursos'
setting.lite_formation.usersMoreActivesByCourses.description: 'Personas más y menos activas de los cursos completados'
setting.lite_evolution.name: 'Grupo de Evolución en Estadísticas Generales'
setting.lite_evolution.description: 'Grupo de Evolución en Estadísticas Generales'
setting.lite_evolution.trainedPerson.name: 'Personas formadas'
setting.lite_evolution.trainedPerson.description: 'Personas que han completado al menos un curso'
setting.lite_evolution.startedCourses.name: 'Cursos iniciados'
setting.lite_evolution.startedCourses.description: 'Cursos iniciados'
setting.lite_evolution.proccessCourses.name: 'Cursos en proceso'
setting.lite_evolution.proccessCourses.description: 'Cursos en proceso'
setting.lite_evolution.finishedCourses.name: 'Cursos terminados'
setting.lite_evolution.finishedCourses.description: 'Cursos finalizados'
setting.lite_evolution.segmentedHours.name: 'Segmentación de horas'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Usuarios nuevos que han finalizado un curso'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Personas nuevas en la plataforma que han completado al menos un curso'
setting.lite_demography.name: 'Grupo de Demografía en Estadísticas Generales'
setting.lite_demography.description: 'Grupo de Demografía en Estadísticas Generales'
setting.lite_demography.usersBySexAndAge.name: 'Usuarios por género y edad'
setting.lite_demography.usersBySexAndAge.description: 'Usuarios por sexo y edad'
setting.lite_demography.ageDistribution.name: 'Distribución por edad'
setting.lite_demography.ageDistribution.description: 'Distribución por edad'
setting.lite_demography.deviceDistribution.name: 'Distribución por dispositivo'
setting.lite_demography.deviceDistribution.description: 'Distribución por dispositivos'
setting.lite_demography.usersByCountries.name: 'Distribución por paises'
setting.lite_demography.usersByCountries.description: 'Distribución por paises'
setting.lite_activity.name: 'Grupo de Actividad en Estadísticas Generales'
setting.lite_activity.description: 'Grupo de Actividad en Estadísticas Generales'
setting.lite_activity.activityInfo.name: 'Información de actividad'
setting.lite_activity.activityInfo.description: 'Personas activas en el portal, personas registradas,  personas que han accedido al menos una vez, en los últimos 30 días, personas desactivadas y personas que nunca han entrado en la plataforma'
setting.lite_activity.accessDays.name: 'Acceso por días'
setting.lite_activity.accessDays.description: 'Días de acceso'
setting.lite_activity.platformAccessByHours.name: 'Acceso por plataforma y horas'
setting.lite_activity.platformAccessByHours.description: 'Horarios de acceso a la plataforma por día y hora (mapa de calor)'
setting.lite_activity.courseStartTime.name: 'Distribución por horas de comienzo de los cursos'
setting.lite_activity.courseStartTime.description: 'Horas inicio de cursos '
setting.lite_activity.courseEndTime.name: 'Distribución por horas de finalización de los cursos'
setting.lite_activity.courseEndTime.description: 'Horas finalización de cursos (mapa calor)'
setting.lite_activity.coursesStartedVsFinished.name: 'Cursos comenzados versus cursos finalizados'
setting.lite_activity.coursesStartedVsFinished.description: 'Cursos empezados vs cursos finalizados'
setting.lite_activity.usersMoreActivesByActivity.name: 'Usuarios más activos'
setting.lite_activity.usersMoreActivesByActivity.description: 'Personas más y menos activas y su tiempo de uso en la plataforma'
setting.lite_itinerary.name: 'Grupo de Itinerarios en Estadísticas Generales'
setting.lite_itinerary.description: 'Grupo de Itinerarios en Estadísticas Generales'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Itinerarios Iniciados y finalizados'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Itinerarios empezados y finalizados'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Itinerarios completados por paises'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Itinerarios completados por paises'
setting.survey.hide_empty_comment.name: 'Ocultar comentario vacío'
setting.survey.hide_empty_comment.description: 'Habilitado oculta las opiniones sin comentario '
setting.survey.show_only_ratings.name: 'Mostrar solo valoración'
setting.survey.show_only_ratings.description: 'Habilitado solo muestra en campus la valoración de estrella sin comentarios independientemente de tener comentario escrito o no'
app.survey.post_nps.enabled.name: Autopublicación
app.survey.post_nps.enabled.description: 'Habilitado el comentario se publica automáticamente, deshabilitado necesita la validación del administrador'
setting.lite_evolution.segmentedHours.description: Horas
setting.course.tab.person.description: 'Habilitado se activa el apartado de "Personas" a nivel de detalle de un curso.'
setting.course.showDeactivatedCourses.name: 'Visualización de cursos desactivados (Campus)'
setting.course.showDeactivatedCourses.description: 'Habilitado se visualizan los cursos desactivados (en gris) en el campus'
catalog.19.name: 'Traducciones administrador'
catalog.19.description: 'Traducciones de administrador'
setting.lenguage.platform: 'Idiomas del administrador'
setting.module.announcement.name: Convocatoria
setting.module.announcement.description: 'Habilitado activa el modulo de "Convocatorias" en la plataforma '
course.diploma.index: 'Índice de contenidos'
setting.zip.day_available_until.name: 'Días disponibles'
setting.zip.day_available_until.description: 'Cantidad de días disponibles antes de que zip sea borrado de manera automática.'
catalog.20.name: 'Campos extras convocatoria'
course.diploma.filters: 'Activar filtros adicionales en reporte de diplomas'
setting.lenguage.platform.description: 'Idiomas disponibles en el panel de administrador'
translations_admin.title1: 'Formación asignada'
translations_admin.title2: 'Formación adicional'
translations_admin.title3: 'Cursos asignados'
translations_admin.title4: 'Cursos voluntarios'
setting.course.tab.stats.description: 'Habilitado se activa el apartado de "Estadísticas" a nivel de detalle de un curso.'
setting.course.tab.options.description: 'Habilitado se activa el apartado de "Opiniones" a nivel de detalle de un curso.'
course.diploma.index.description: 'Habilitado se activa el apartado de "Diplomas" a la hora de crear/modificar un curso'
setting.use.filter_in_ranking.name: 'Utilizar filtros en el ranking de usuario'
setting.use.filter_in_ranking.description: 'Permite seleccionar en el menú los filtros de las categorías con las que un usuario desea compararse. Si esta opción está desactivada, el usuario se comparará por defecto con todos los filtros disponibles en la plataforma'
setting.use.include_only_first_category_name: 'Mostrar únicamente la primera categoría del filtro en el ranking'
setting.use.include_only_first_category_description: 'Si está activo, solo se muestra la primera vinculación del usuario. De lo contrario, se muestran todas las categorías asociadas. Por ejemplo, si la categoría es "país", el usuario podría estar vinculado tanto a España como a Nicaragua.'
setting.email_support_error.name: 'Correo de soporte para errores'
setting.email_support_error.description: 'Correos a los que se les enviaran las incidencias de la plataforma'
setting.export.task.slot_quantity.name: 'Cantidad de slots para tareas por usuario'
setting.export.task.slot_quantity.description: 'Número de slots disponibles para procesamiento de tareas de exportación por usuario.'
setting.export.task.long_running_type_tasks.name: 'Tipos de tareas de larga duración'
setting.export.task.long_running_type_tasks.description: 'Lista de tipos de tareas que son consideradas de larga duración para exportación.'
setting.export.zip_task.slot_quantity.name: 'Cantidad de slots para tareas zip por usuario'
setting.export.zip_task.slot_quantity.description: 'Número de slots disponibles para procesamiento de tareas de compresión zip por usuario.'
setting.export.zip_task.long_running_type_tasks.name: 'Tipos de tareas zip de larga duración'
setting.export.zip_task.long_running_type_tasks.description: 'Lista de tipos de tareas zip que son consideradas de larga duración.'
setting.export.task.user_pending_max_count_task.name: 'Máximo de tareas pendientes por usuario'
setting.export.task.user_pending_max_count_task.description: 'Cantidad máxima de tareas pendientes que un usuario puede tener en cola.'
setting.export.task.timeout.name: 'Tiempo límite para tareas'
setting.export.task.timeout.description: 'Tiempo máximo en segundos antes de que una tarea de exportación sea considerada como expirada.'
setting.export.zip_task.timeout.name: 'Tiempo límite para tareas zip'
setting.export.zip_task.timeout.description: 'Tiempo máximo en segundos antes de que una tarea de compresión zip sea considerada como expirada.'
setting.export.task.timeout_seconds.name: 'Tiempo de expiración para tareas en estado TIMEOUT'
setting.export.task.timeout_seconds.description: 'Tiempo máximo en segundos después del cual una tarea en estado TIMEOUT ya no se considera como en ejecución.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'Es el diploma personalizado para Novomatic'
app.announcement.managers.sharing.name: 'Compartir una convocatoria con otros managers'
app.announcement.managers.sharing.description: 'Compartir una convocatoria con otros managers'
