message_api.content.scorm_correctly: 'Scorm a bien démarré'
message_api.content.token_not_send: 'Token non envoyé'
message_api.content.error_get_content: 'Une erreur s''est produite lors de la tentative de démarrage du chapitre :'
message_api.content.scorm_user_not_access: 'L''utilisateur n''a pas accès à ce cours'
message_api.content.scorm_not_content: 'Le chapitre n''est pas un paquet de contenu'
message_api.base.credentials_no_correct: 'Les informations d''identification saisies ne sont pas correctes.'
message_api.base.authorized_access: 'Accès non autorisé'
message_api.base.user_successfully: 'L''utilisateur s''est déconnecté avec succès'
message_api.base.error_login: 'Une erreur s''est produite lors de la tentative de connexion de l''utilisateur - Error :'
message_api.base.authorized_token_fail: 'Vous n''avez pas pu accéder via SSO. Si vous n''avez pas encore enregistré et activé votre utilisateur sur la plateforme, vous devez le faire pour permettre ce type d''accès SSO.'
message_api.base.user_not_exists_in_ad: 'Informations d''identification incorrectes ou l''utilisateur n''existe pas dans Active Directory'
message_api.controller.error_get_course: 'Une erreur s''est produite lors de la tentative d''enregistrement de l''utilisateur - Error :'
message_api.controller.chapter_init: 'Chapitre correctement lancé'
message_api.controller.error_chapter_init: 'Une erreur s''est produite lors de la tentative de démarrage du chapitre :'
message_api.controller.error_chapter_update: 'Chapitre correctement mis à jour'
message_api.controller.help_text: 'Les textes d''aide sont récupérés avec succès'
message_api.controller.course_not_found: 'Aucun cours trouvé'
message_api.controller.user_not_access: 'L''utilisateur n''a pas accès à ce cours'
message_api.controller.evaluation_course: 'Évaluation soumise'
message_api.controller.training_itinerary: 'Votre parcours de formation'
message_api.controller.continue_training: 'Formation continue'
message_api.message.not_found: 'Message non trouvé'
message_api.message.not_access: 'Accès non autorisé'
message_api.scorm.init: 'Scorm a bien démarré'
message_api.scorm.not_package: 'Le chapitre n''est pas un paquet SCORM'
message_api.scorm.button_add_scorm: 'Ajouter le paquet scorm'
message_api.scorm.button_delete_scorm: 'Supprimer le paquet scorm'
message_api.scorm.title_package: 'Paquet Scorm'
message_api.scorm.upload_file_scorm: 'Sélectionnez le fichier .zip'
message_api.scorm.info_upload_scorm: 'Vous devez charger le paquet scorm'
message_api.notification.not_found: 'Notification non trouvée'
message_api.player.no_content: 'Ce chapitre n''a pas de contenu'
message_api.alert.minimal_question: 'Ce jeu nécessite un minimum de %number% de questions.'
message_api.alert.image_puzzle: 'Le puzzle doit avoir une image'
message_api.alert.question_correct: 'A cette question, vous devez ajouter une réponse correcte'
message_api.alert.chapter_content: 'Ce chapitre manque de contenu'
message_api.alert.pdf: 'Ce chapitre a besoin d''un pdf'
message_api.alert.video: 'Ce chapitre manque de contenu'
message_api.alert.slider: 'Ce chapitre contient un minimum de %nombre% d''images.'
message_api.alert.scorm: 'Ce chapitre a besoin d''un paquet scorm'
message_api.alert.question_hidden: 'Cette question ne doit contenir qu''une seule réponse et elle doit être correcte.'
message_api.alert.minimal_pair: 'Ce chapitre exige un minimum de %number% de couples.'
message_api.announcement.call_for_aplications: 'Appel à candidatures'
message_api.alert.course_content_incomplete: 'Il manque des informations dans les chapitres, veuillez réviser et ajouter du contenu afin de poursuivre la création de l''appel à propositions.'
message_api.diploma.supered: 'Pour vaincre'
message_api.diploma.granted: 'Attribué à'
message_api.diploma.date: Date
message_api.diploma.diploma: Diplôme
message_api.diploma.trato: M/Ms
message_api.diploma.nif: 'avec numéro d''identification fiscale'
message_api.diploma.services: 'qui travaille pour la société'
message_api.diploma.cif: 'avec numéro de TVA'
message_api.diploma.evaluation: 'A réussi l''action de formation avec une évaluation positive.'
message_api.diploma.code: 'Code AF/GROUP'
message_api.diploma.between: 'Pendant les jours'
message_api.diploma.duration: 'avec une durée totale de'
message_api.diploma.fomartionType: 'heures en mode formation'
message_api.diploma.courseContent: 'Contenu du cours à la page suivante'
message_api.diploma.signatureSeal: 'Signature et cachet de l''entité responsable <br>de la formation.'
message_api.diploma.signatureDate: 'Date d''émission'
message_api.diploma.acreditation: Accréditation
message_api.diploma.signature: 'Signature du participant'
message.api.diploma.type_course_default: 'Apprentissage en ligne'
message.api.diploma.dates: 'Date du %dateFrom% au %dateTo%'
message.api.diploma.hours: '{0} %count% heures|{1} %count% heure|]1,Inf] %count% heures'
message.api.diploma.duration: 'Durée de l''accord'
message.api.diploma.with_dni: 'avec %nomIdentification% %dni% %dni%'
message_api.announcement.cancelation_announcement: 'Annulation de l''appel %course%'
message_api.help_category.delete_error: 'Impossible de supprimer la catégorie %categoryName%, elle a un contenu lié'
