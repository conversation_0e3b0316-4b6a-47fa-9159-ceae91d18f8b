menu.title_platform: 'Kampus szkoleniowy'
complete: Zak<PERSON>ńczono
Show: Zobacz
Edit: Modyfikacja
Remove: Usunąć
Delete: Usuń
total: Ł<PERSON><PERSON>nie
'Yes': Tak
'No': Nie
Actions: Działania
Clear: Limpiar
'No results found': 'Nie znaleziono wyników'
Configuration: Konfiguracja
Limit: Limit
Close: Zamknij
Save: Zapisz
'Save and create other': 'Utwórz i dodaj kolejny'
'Save changes': '<PERSON>ap<PERSON>z zmiany'
'Save and keep editing': 'Zapisz i kontynuuj edycję'
state: Stan
create: Utwórz
cancelar: Anuluj
back: 'Powrót do'
add: Dodaj
no_content: '<PERSON><PERSON> zawartoś<PERSON>'
no_result: 'Nie znaleziono żadnych wyników'
configure_simulator: 'Konfiguracja symulatora'
edit_configure_simulator: 'Edytuj konfigurację'
configure_success: 'Konfiguracja powiodła się'
save_success: 'Rejestracja została pomyślnie zapisana'
error_success: 'Wystą<PERSON>ł błąd podczas zapisywania rekordu'
configure_completed: 'Konfiguracja zakończona'
'Created At': Utworzony
'Created By': 'Stworzony przez'
'Created by': 'Stworzony przez'
'Updated At': Aktualizacja
'Updated By': 'Zaktualizowane przez'
'Deleted By': 'Usunięty przez'
'Deleted At': Usunięto
menu.courses_managment.title: 'Zarządzanie kursami'
menu.courses_managment.Segments: Segmenty
menu.courses_managment.categories: Kategorie
menu.courses_managment.level: Poziomy
menu.courses_managment.courses: Kursy
menu.courses_managment.announcements: Konwokacja
menu.courses_managment.nps_question: 'Preguntas NPS'
menu.courses_managment.opinion_course: 'Opinie na temat kursu'
menu.help_managment.title: 'Zarządzanie pomocą'
menu.help_managment.content_help: 'Pomoc dotycząca treści'
menu.help_managment.categories_help: 'Pomoc w kategorii'
menu.users_managment.title: 'Zarządzanie użytkownikami'
menu.users_managment.users: Użytkownicy
menu.users_managment.managers: Menedżerowie
menu.users_managment.filter: Filtry
menu.news.title: Wiadomości
menu.stats.title: Statystyki
menu.stats.export: 'Narzędzie Excel'
menu.users.edit_profile: 'Edytuj profil'
form.label.delete: Usuń
action.save: 'Zapisz teraz'
common_areas.created_at: Utworzony
common_areas.updated_at: Aktualizacja
common_areas.deleted_at: Aktualizacja
common_areas.created_by: 'Stworzony przez'
common_areas.updated_by: 'Zaktualizowane przez'
common_areas.actions: Działania
common_areas.basic_information: 'Podstawowe informacje'
common_areas.edit: Edytuj
common_areas.delete: Usuń
common_areas.name: Liczba
common_areas.image: Obrazy
common_areas.state: Stan
common_areas.create: Utwórz
common_areas.save: Zapisz
common_areas.back_list: 'Powrót do listy'
course_category.label_in_singular: 'Kategoria kursu'
course_category.label_in_plural: 'Kategorie kursów'
course_category.configureFields.category_name: 'Nazwa Kategoria'
course_category.configureFields.category_order: 'Kategoria zamówienia'
course_category.configureFields.translations: Tłumaczenia
course.label_in_singular: Kurs
course.label_in_plural: Kursy
course.back_to_course: 'Powrót do kursu'
course.configureFields.basic_information: 'Podstawowe informacje'
course.configureFields.code: Kod
course.configureFields.name: Liczba
course.configureFields.description: Opis
course.configureFields.basic: Podstawowy
course.configureFields.access_level: 'Poziom dostępu'
course.configureFields.clone: Klon
course.configureFields.open: Otwarty
course.configureFields.open_visible: 'Widoczny na otwartym kampusie'
course.configureFields.active: Aktywny
course.configureFields.categories: Kategorie
course.configureFields.profesional_categories: 'Kategorie zawodowe'
course.configureFields.image: Obrazy
course.configureFields.chapter: Rozdział
course.configureFields.translation: Tłumaczenie
course.configureFields.general_information: 'Informacje ogólne'
course.configureFields.segment: Segment
course.configureFields.category: Kategoria
course.configureFields.thumbnail_url: 'Adres url miniatury'
course.configureFields.locale: Lokalizacja
course.configureFields.all_seasons: 'Wszystkie sezony'
course.configureFields.chapters: Rozdziały
course.configureFields.seasons: 'Pory roku'
course.configureFields.courses_translate: Tłumaczenia
course.configureFields.add_chapter: 'Rozdział zbiorczy'
course.configureFields.no_seasons: 'W sezonie'
course.configureFields.add_seasons: 'Sezon zbiorczy'
course.configureFields.add_annuncement: 'Dodaj połączenie'
course.configureFields.question_modal_translate: 'czy naprawdę chcesz przetłumaczyć ten kurs?'
course.configureFields.content_modal_translate: 'Ta czynność spowoduje utworzenie kopii kursu, która posłuży jako przewodnik do tłumaczenia na inny język.'
course.configureFields.translate_already: 'Ten kurs ma już tłumaczenie na ten język'
course.configureFields.tag_description: 'Oddziel etykiety, naciskając klawisz Enter'
course.configureFields.new: Nowość
course.configureFields.add_material: 'Dodaj materiał'
course.configureFields.add_task: 'Dodaj zadanie'
course.configureFields.task: Zadania
course.season_add: 'Sezon został poprawnie dodany'
course.season_update: 'Sezon został poprawnie zaktualizowany'
course.season_add_error: 'Wystąpił błąd podczas dodawania sezonu'
course.panel.class: 'Szczegóły kursu'
chapter.label_in_plural: Rozdziały
chapter.configureFields.title: Tytuł
chapter.configureFields.course: Kurs
chapter.configureFields.type: Typ
chapter.configureFields.season: Sezon
chapter.configureFields.description: Opis
chapter.configureFields.image: Obrazy
chapter.configureFields.image_file: 'Obraz archiwalny'
chapter_type.description.1: '<p>Rozdział dotyczący Scorma jest bardzo interesujący:</p><p>Pozwala nam na przesyłanie szerokiej gamy treści generowanych za pomocą innych narzędzi, na przykład dokumentów, treści interaktywnych, a nawet gier.</p>'
chapter_type.description.2: '<p>Jest to jeden z najbardziej wszechstronnych rozdziałów.</p><p>Po lewej stronie wyświetlane są wprowadzone tytuły, które służą jako indeks do szybkiego wyszukiwania treści i ułatwiają czytanie.</p>'
chapter_type.description.3: '<p>Jest to gra quizowa, która dodaje element losowy, ponieważ musisz ukończyć segmenty koła ruletki, aby ją przejść.</p><p>Opiera się na tworzeniu baterii pytań, aby wzmocnić wiedzę, której się nauczyłeś. Możesz wprowadzić dowolną liczbę pytań, a także dołączyć do nich obrazy.</p><p>Aby gra działała poprawnie, zaleca się dołączenie co najmniej 10 pytań. </p>'
chapter_type.description.4: '<p>Ta gra zawiera serię pytań, które zawierają dodatkowy czynnik ryzyka. Po każdym pytaniu uczestnicy mają możliwość pozostania i zachowania aktualnego wyniku lub podjęcia ryzyka udzielenia odpowiedzi na dodatkowe pytanie w celu uzyskania większej liczby punktów. Jednak w przypadku udzielenia nieprawidłowej odpowiedzi wszystkie punkty zgromadzone do tego momentu przepadają.</p>'
chapter_type.description.5: '<p>Jest to najbardziej klasyczny rozdział gry.</p><p>Pomysł polega na stworzeniu baterii pytań w celu utrwalenia zdobytej wiedzy. Możesz wprowadzić nieograniczoną liczbę pytań wraz z obrazkiem i tylko jedną poprawną odpowiedzią.</p>'
chapter_type.description.6: '<p>Sortuj i obracaj elementy, aż znajdą się we właściwej pozycji i orientacji, w przeciwnym razie nie będą do siebie pasować.</p><p>Na górze znajdują się cztery segmenty, które odpowiadają czasowi dostępnemu na ukończenie układanki. Kiedy segment czasu się kończy, zadawane jest jedno z wprowadzonych przez nas pytań. Poprawne udzielenie odpowiedzi daje więcej czasu na rozwiązanie zagadki. Ostateczny wynik będzie zależał od kombinacji czasu potrzebnego do ukończenia samej łamigłówki, liczby poprawnych odpowiedzi na pytania i liczby pominiętych pytań.</p>'
chapter_type.description.7: '<p>Ta gra stanowi łamigłówkę opartą na obrazku. Aby ją rozwiązać, musisz uważnie wybrać odpowiednie litery, zanim skończy się czas.</p>'
chapter_type.description.8: '<p>Ponieważ format PDF jest szeroko stosowany do różnych rodzajów treści, takich jak protokoły lub podręczniki, rozdziały PDF są bardzo interesujące, ponieważ pozwalają nam ponownie wykorzystać materiał, który został już opublikowany.</p>'
chapter_type.description.9: '<p>Zasoby audiowizualne mają ogromny potencjał pedagogiczny, przyciągają, przykuwają uwagę i wzbudzają ciekawość.</p><p>Platforma pozwala nam wybrać sposób wprowadzenia wideo poprzez "url" lub wybierając plik, który znajduje się na naszym komputerze. W tym drugim przypadku możemy dołączyć plik z napisami.</p>'
chapter_type.description.10: '<p>Rozdział typu suwak z obrazami.</p>'
chapter_type.description.11: '<p>Gra polega na dopasowywaniu słów, z których każde odpowiada literze na kole. Czasami rozwiązaniem będzie słowo zaczynające się na daną literę, a czasami będzie ono po prostu zawierało tę literę.</p>'
chapter_type.description.12: '<p>W tej grze zostanie ci zadana seria pytań w formie tekstu, obrazu lub kombinacji obu w formie stwierdzeń. Istnieją dwie możliwe odpowiedzi "Prawda" lub "Fałsz" i tylko jedna z nich jest poprawna. Czas na rozwiązanie gry jest ograniczony.</p>'
chapter_type.description.13: '<p>W tej grze będziesz musiał rozwiązać zagadkę przed upływem czasu. Wskazówka będzie ukryta za rozmytym obrazem, który będzie stopniowo wyostrzany w miarę postępów w grze. Oprócz obrazu, dostępna jest również dodatkowa pomoc w postaci tekstu.</p>'
chapter_type.description.14: '<p>W tej klasycznej grze musisz układać elementy, przeciągając klocki w odpowiedniej kolejności. Różnorodność możliwości sprawia, że idealnie nadaje się do ćwiczeń matematycznych i innych wyzwań edukacyjnych. Idealna do tworzenia testów sprawdzających umiejętności rozumowania i porządkowania.</p>'
chapter_type.description.15: '<p>Ta gra jest idealna do treningu pamięci i koncentracji. Celem jest znalezienie wszystkich par pasujących do siebie kart. Położenie kart jest tworzone losowo, więc każda rozgrywka będzie inna.</p>'
chapter_type.description.16: '<p>W tej grze otrzymasz serię słów, fraz lub pojęć, które musisz skojarzyć z odpowiednią rodziną lub grupą pokazaną poniżej. Przetestujesz swoje umiejętności kojarzenia i szybkość umysłu, ścigając się z czasem.</p>'
chapter_type.description.17: '<p>W tej grze gramatycznej i edukacyjnej celem jest wypełnienie luk w zdaniach odpowiednimi słowami, aby sprawdzić swoje umiejętności językowe i gramatyczne. Ale to nie wszystko! Ta gra jest wszechstronna i może być używana do wielu innych celów edukacyjnych.</p>'
chapter_type.description.18: '<p>W tej grze zostanie ci przedstawione pytanie lub zagadka do rozwiązania. Zadanie będzie polegało na dokładnym przeanalizowaniu zagadki i użyciu podanych liter, aby znaleźć prawidłowe słowo. Ale uważaj, ponieważ czas jest ograniczony, co oznacza, że będziesz musiał być szybki i dokładny, aby wygrać.</p>'
chapter_type.description.19: '<p>W tej grze będziesz musiał odgadnąć ukryte słowo w maksymalnie sześciu próbach. Każda próba będzie polegała na wprowadzeniu prawidłowego słowa, a po każdej próbie kolor kwadratów zmieni się, aby pokazać, które litery są prawidłowe, a które również znajdują się we właściwej pozycji.</p>'
chapter_type.description.20: '<p>Ta gra polega na znajdowaniu ukrytych słów w wyszukiwaniu wyrazów. Celem jest zaznaczenie sekwencji liter poziomo, pionowo lub ukośnie. Słowa można znaleźć w obu kierunkach, od lewej do prawej lub od prawej do lewej. Jeśli sekwencja jest częścią ukrytego słowa, zostanie uznana za poprawną odpowiedź.</p>'
chapter_type.description.21: '<p>Podczas odtwarzania wideo wstawiane są interaktywne pytania, które wymagają od widza zwrócenia uwagi na treść wideo w celu udzielenia poprawnej odpowiedzi. Krótko mówiąc, połączenie mocy wideo z interaktywnością quizu zapewnia skuteczne i angażujące doświadczenie edukacyjne.</p>'
chapter_type.add.1: 'Dodaj scorm'
chapter_type.add.2: 'Dodaj zawartość'
chapter_type.add.3: 'Tworzenie gry'
chapter_type.add.4: 'Tworzenie gry'
chapter_type.add.5: 'Utwórz quiz'
chapter_type.add.6: 'Tworzenie gry'
chapter_type.add.7: 'Tworzenie gry'
chapter_type.add.8: 'Zbiorczy plik PDF'
chapter_type.add.9: 'Dodaj wideo'
chapter_type.add.10: 'Dodaj slider'
chapter_type.add.11: 'Tworzenie gry'
chapter_type.add.12: 'Tworzenie gry'
chapter_type.add.13: 'Tworzenie gry'
chapter_type.add.14: 'Tworzenie gry'
chapter_type.add.15: 'Tworzenie gry'
chapter_type.add.16: 'Tworzenie gry'
chapter_type.add.17: 'Tworzenie gry'
chapter_type.add.18: 'Tworzenie gry'
chapter_type.add.19: 'Tworzenie gry'
chapter_type.add.20: 'Tworzenie gry'
chapter_type.add.21: 'Tworzenie quizu wideo'
chapter_type.all: Wszystkie
chapter_type.content: Treść
chapter_type.games_test: Ocena
chapter_type.description_test: 'Opis testu'
chapter_type.type: 'Typ rozdziału'
chapter.add_pdf: 'Dodaj pdf'
chapter.chapter.show_video: 'Obejrzyj wideo'
chapter.message_pdf_success: 'Plik pdf został pomyślnie dodany'
chapter.message_pdf_error: 'Wystąpił błąd podczas zapisywania pliku pdf'
chapter.chapter.materials: Materiały
chapter.chapter.show_pdf: 'Wyświetl pdf'
announcements.label_in_singular: Konwokacja
announcements.label_in_plural: Ogłoszenia
announcements.configureFields.courses: Kursy
announcements.configureFields.start_at: Start
announcements.configureFields.finish_at: Koniec
announcements.configureFields.called: nazywany
announcements.configureFields.subsidized: Subsydiowane
announcements.configureFields.subsidizer: Technik
announcements.configureFields.subsidizer_entity: 'Podmiot subsydiujący'
announcements.configureFields.subsidized_announcement: 'Zaproszenie do składania wniosków o dotacje'
announcements.configureFields.max_users: 'Maksymalna liczba użytkowników'
announcements.configureFields.formative_action_type: 'Rodzaj działania szkoleniowego'
announcements.configureFields.format: Format
announcements.configureFields.total_hours: 'Łączna liczba godzin'
announcements.configureFields.place: Miejsce
announcements.configureFields.training_center: 'Centrum szkoleniowe'
announcements.configureFields.training_center_address: 'Adres ośrodka szkoleniowego'
announcements.configureFields.training_center_nif: 'Centrum szkoleniowe NIF'
announcements.configureFields.training_center_phone: 'Numer telefonu centrum szkoleniowego'
announcements.configureFields.training_center_email: 'E-mail centrum szkoleniowego'
announcements.configureFields.training_center_teacher_dni: 'Ośrodek szkolenia nauczycieli DNI'
announcements.configureFields.called_user: 'Użytkownicy o nazwie'
announcements.configureFields.search: Wyszukiwanie
announcements.configureFields.announcement_for: 'Paragraf dotyczący konwokacji'
announcements.configureFields.search_user_title: 'Wyszukiwanie użytkowników w celu wezwania do składania wniosków'
announcements.configureFields.placeholder_search_user: 'Wyszukiwanie użytkowników'
announcements.configureFields.placeholder_search_category: 'Wyszukiwanie według kategorii'
announcements.configureFields.placeholder_search_department: 'Wyszukiwanie według działu'
announcements.configureFields.placeholder_search_center: 'Wyszukiwanie według centrum'
announcements.configureFields.placeholder_search_country: 'Wyszukiwanie według kraju'
announcements.configureFields.placeholder_search_division: 'Wyszukiwanie według działu'
announcements.configureFields.result_found: 'Znaleziono wyniki'
announcements.configureFields.clear_result: 'Wyraźne wyniki'
announcements.configureFields.error_already_called_user: 'Błąd: Użytkownik został już wywołany!'
announcements.configureFields.error_already_called_user_date: 'Błąd: Użytkownik został już wywołany w podobnym zakresie dat!'
announcements.configureFields.notified: Zgłoszony
announcements.configureFields.content_course: 'Treść kursu'
announcements.configureFields.report: Raport
announcements.configureFields.title_report: 'Raport studentów'
announcements.configureFields.direction: Adres
announcements.configureFields.telephone: Telefon
announcements.configureFields.nif: NIF
announcements.configureFields.tutor: Nauczyciel
announcements.configureFields.apt: Apt
announcements.configureFields.time_total: 'Całkowity czas'
question.label_in_singular: Pytanie
question.label_in_plural: Pytania
question.configureFields.question: Pytanie
question.configureFields.random: Aleatory
question.configureFields.answers: Odpowiedzi
question.configureFields.image_file: 'Obraz pliku'
question.configureFields.question_for: 'Pytania do'
question.configureFields.image_for: 'Obraz dla'
question.configureFields.add_image_puzzle: 'Dodaj obrazek układanki'
question.configureFields.add_question: 'Utwórz pytanie'
question.configureFields.see_image: 'Wyświetl obraz'
content.label_in_singular: Treść
content.label_in_plural: Zawartość
content.configureFields.title: Tytuł
content.configureFields.content: Treść
content.configureFields.position: Pozycja
content.configureFields.add_content: 'Dodaj zawartość'
content.configureFields.content_for: 'Zawartość dla'
question_nps.label_in_singular: 'Pytanie NPS'
question_nps.label_in_plural: 'Pytania NPS'
question_nps.configureFields.type: Typ
question_nps.configureFields.position: Pozycja
question_nps.configureFields.question: Pregunta
question_nps.configureFields.course: Kurs
question_nps.configureFields.name_question: 'Pytanie o nazwę'
question_nps.configureFields.translations: Tłumaczenia
opinions.label_in_singular: Opinia
opinions.label_in_plural: Opinie
opinions.configureFields.course: Kurs
opinions.configureFields.question: Pregunta
opinions.configureFields.to_post: publikować
opinions.configureFields.value: Wartość
opinions.configureFields.valoration: Wycena
help_category.label_in_singular: 'Pomoc w kategorii'
help_category.label_in_plural: 'Kategorie pomocy'
help_category.configureFields.category_name: 'Nazwa kategorii'
help_category.configureFields.translations: Tłumaczenia
help_text_content.label_in_singular: 'Pomoc dotycząca treści'
help_text_content.label_in_plural: 'Pomoc dotycząca zawartości'
help_text_content.configureFields.category: Kategoria
help_text_content.configureFields.title: Tytuł
help_text_content.configureFields.text: Tekst
help_text_content.configureFields.translations: Tłumaczenia
user.label_in_singular: Użytkownik
user.label_in_plural: Użytkownicy
user.configureFields.division: Podział
user.configureFields.country: Kraj
user.configureFields.category: Kategoria
user.configureFields.departament: Dział
user.configureFields.center: Centro
user.configureFields.gender: Płeć
user.configureFields.first_name: Liczba
user.configureFields.last_name: Nazwisko
user.configureFields.code: Kod
user.configureFields.password: Hasło
user.configureFields.change_password: 'Zmiana hasła'
user.configureFields.courses: Kursy
user.configureFields.extra: Dodatkowy
user.configureFields.announcements: Ogłoszenia
user.configureFields.extra_fields: 'Dodatkowe pola'
user.configureFields.avatar_image: 'Obraz profilu'
user.configureFields.new_password: 'Nowe hasło'
user.configureFields.birthdate: 'Data urodzenia'
user.configureFields.edit_user: 'Edytuj użytkownika'
user.configureFields.user_data: 'Dane użytkownika'
user.configureFields.stats: Statystyki
user.configureFields.chapter: Rozdziały
user.configureFields.ratio_course: 'Stosunek kursów do osób'
user.configureFields.avg_stars: 'Średnia gwiazda'
user.configureFields.time: Pogoda
user.configureFields.chapter_time: 'Czas poświęcony na rozdział'
user.configureFields.available: dostępny
user.configureFields.messages: Wiadomości
user.configureFields.login_history: 'Historia logowania'
user.configureFields.started_at: Start
user.configureFields.finished_at: Koniec
user.configureFields.time_spent: 'Czas spędzony'
user.configureFields.content_viewed: 'Przeglądana zawartość'
user.configureFields.interaction_with_teacher: 'Interakcje z nauczycielem'
user.configureFields.course_content: 'Treść kursu'
user.configureFields.content_type: 'Typ zawartości'
user.configureFields.finished: Zakończono
user.configureFields.teacher_interaction: 'Interakcje z nauczycielami'
user.configureFields.date: Data
user.configureFields.sender: Remitent
user.configureFields.recipient: Odbiorca
user.configureFields.subject: Przedmiot
user.configureFields.questions: Pytania
user.configureFields.chapter_type: 'Rodzaj rozdziałów'
user.configureFields.finished_chapter_types: 'Rodzaje ukończonych rozdziałów'
user.configureFields.button_validate: Walidacja
user.configureFields.open: 'Kampus Abierto'
user.configureFields.computer: Komputer
user.configureFields.mobile: Mobilny
user.configureFields.tablet: Tablet
user.manage.assign_data: 'Przypisywanie danych'
user.gender.m: Mężczyzna
user.gender.f: Kobieta
user.configureFields.time_title: 'Poświęcenie na dzień'
user.configureFields.interaction_in_forum: 'Interakcje na forum'
user.configureFields.email: E-mail
user.configureFields.fullname: 'Imię i nazwisko'
user.filtersRequired: 'Wybierz co najmniej jeden filtr'
stats.general_stats: 'Ogólne statystyki'
stats.total_times_spent: 'Całkowity czas spędzony'
stats.users_activity: 'Aktywność użytkownika'
stats.users_active_last_30: 'Aktywa w ciągu ostatnich 30 dni'
stats.users_inactive_last_30: 'Brak aktywności w ciągu ostatnich 30 dni'
stats.users_never_login: 'Nigdy nie weszli'
stats.daily_chapter: 'Ukończone rozdziały dzienne'
stats.finished_chapters: 'Ukończone rozdziały'
stats.daily_course: 'Ukończone kursy dzienne'
stats.finished_courses: 'Ukończone kursy'
stats.daily_login: 'Codzienne logowanie'
stats.daily_login_tooltip: Logowanie
stats.all_courses: 'Wszystkie kursy'
stats.all_countries: 'Wszystkie kraje'
stats.all_centers: 'Wszystkie ośrodki'
stats.all_categories: 'Wszystkie kategorie'
stats.all_departament: 'Wszystkie działy'
stats.all_gender: 'Wszystkie gatunki'
stats.all_divisions: 'Wszystkie dywizje'
stats.filters: Filtry
stats.filter_by: 'Filtruj według'
stats.modal_close: Zamknij
stats.clear_filters: 'Czyste filtry'
stats.apply_filters: 'Stosowanie filtrów'
stats.export_title: 'Eksport danych'
stats.export.start_date: 'Data rozpoczęcia'
stats.export.end_date: 'Data końcowa'
stats.export.filename: 'Nazwa pliku'
stats.export.request_date: 'Data złożenia wniosku'
stats.export.available_until: 'Dostępne do'
stats.export.loading_data: 'Ładowanie danych'
stats.export.no_data: 'Brak dostępnych danych'
stats.export.download_file: 'Pobierz plik'
stats.export.abort_export_request: 'Anulowanie aplikacji eksportu'
stats.export.view_details: 'Wyświetl szczegóły'
stats.export.reset_form: 'Resetowanie pól'
stats.export.error_start_date: 'Data początkowa nie może być późniejsza niż data końcowa.'
stats.export.export_error: 'Wystąpił błąd podczas generowania raportu.'
stats.export.export_success: 'Raport został pomyślnie dodany do kolejki pobierania.'
stats.export.export_dir: 'Statystyka / narzędzie Excel'
stats.devices_login: 'Logowanie do urządzenia'
stats.distribution_ages: 'Rozkład wieku'
stats.generation_babyboom: BabyBoom
stats.generation_x: 'Pokolenie X'
stats.generacion_milenials: Milenialsi
stats.generacion_z: 'Pokolenie Z'
stats.title_information_user: 'Informacje o użytkowniku'
stats.title_information_content: 'Informacje o zawartości'
stats.title_information_courses: 'Informacje o kursie'
stats.title_information_chapter: 'Informacje o rozdziale'
stats.distribution_country: 'Dystrybucja według krajów'
stats.title_finish_m: zakończony
stats.title_made: przeprowadzony
stats.title_made_f: przeprowadzony
stats.chapter_day: dni
stats.chaper_hours: godziny
stats.chapter_minutes: minuty
stats.chapter_total: OGÓŁEM
stats.chapter_media: MEDIA
stats.content_active: aktywa
stats.content_active_f: aktywny
stats.totalLogin: Łącznie
stats.access: Dostęp
stats.uniqueLogin: Wyjątkowy
stats.at_least_one_course_finished: 'Przeszkoleni użytkownicy'
stats.top_rated_courses: 'Najwyżej oceniane kursy'
stats.lowest_rated_courses: 'Najniżej oceniane kursy'
stats.most_completed_courses: 'Najwięcej ukończonych kursów'
stats.users_more_actives: 'Najbardziej aktywni użytkownicy'
stats.users_less_actives: 'Mniej aktywnych użytkowników'
stats.accumulative.title: 'Ewolucyjny i kumulatywny'
stats.accumulative.trained: 'Przeszkoleni pojedynczy użytkownicy'
stats.accumulative.new: Nowość
stats.accumulative.accumulated: Skumulowane
stats.accumulative.chart: Wykres
stats.accumulative.logins: Logowanie
stats.accumulative.courses: Kursy
stats.accumulative.courses_started: 'Rozpoczęte kursy'
stats.accumulative.courses_finished: 'Ukończone kursy'
stats.accumulative.ratings: Oceny
stats.accumulative.time: 'Zainwestowany czas (w godzinach)'
stats.accumulative.filters: 'Dystrybucja filtrów'
'stats. daily_posts': 'Codzienne posty na forum'
stats.most_active_threads: 'Najbardziej aktywne wątki'
stats.most_active_users: 'Najbardziej aktywni użytkownicy'
stats.forum_post_messages_count: Wiadomości
stats.forum_post_title: Tytuł
task.status.pending: 'W toku'
task.status.in_progress: 'W Progreso'
task.status.success: Zakończono
task.status.failure: Błąd
security.login_button_login: Enter
security.login_button_create_account: 'Utwórz konto'
security.login_title: 'Wprowadź swoje dane'
security.login_remember_me: Recordarme
security.login_question_password: 'zapomniałeś hasła?'
security.button_register: Rejestr
security.button_exist_accoutn: 'Mam już konto'
security.button_account: 'Zaakceptuj warunki'
security.first_name: Liczba
security.last_name: Apellido
security.password: Hasło
security.repeat_password: 'Powtórz hasło'
security.register: Registro
security.remembered_the_password: 'czy pamiętasz swoje hasło?'
security.button_send_email: 'Wyślij e-mail'
security.reset_your_password: 'Resetowanie hasła'
security.text_reset_password: 'Wprowadź swój adres e-mail, a my wyślemy Ci link do zresetowania hasła.'
course_level.label_in_singular: Poziom
course_level.label_in_plural: Poziomy
component_video.add_package_video: 'Dodaj pakiet wideo'
component_video.edit_package_video: 'Edycja pakietu wideo'
component_video.type: Typ
component_video.url_video: 'Adres URL filmu'
component_video.file_subtitle: 'Plik z napisami'
component_video.button_save: Guardar
component_video.text_content_subtitle_video: 'Ten film ma już napisy. Jeśli dodasz nowe napisy, zostaną one zastąpione starymi.'
component_video.upload_file_video: 'Wybierz plik wideo'
component_video.preparing_file: 'Chwila, przygotowuję plik'
component_video.package_video: 'Pakiet wideo'
component_video.optimizing_video: 'Wideo jest obecnie optymalizowane i będzie dostępne wkrótce'
component_video.text_good: Dobrze
filter_category.label_in_singular: 'Kategoria filtra'
filter_category.label_in_plural: 'Kategorie filtrów'
filter_category.configureFields.name: Liczba
filter.label_in_singular: Filtr
filter.label_in_plural: Filtry
filter.configureFields.name: Liczba
filter.configureFields.action_add: 'Dodaj filtr'
filter.extras.no_filters: 'Brak przypisanych filtrów'
filter.extras.loadings: Cargando...
filter.extras.no_filter_selected: 'Nie wybrano żadnego filtra'
filter.extras.no_filter_assigned: 'Brak filtrów do przypisania'
news.form.title: Tytuł
news.form.text: Tekst
help.pdf.general: ADMIN_ES
help.video.general: '549279910'
segment_category.label_in_singular: 'Segment kategorii'
segment_category.label_in_plural: 'Segment kategorii'
segment_category.configureFields.name: Liczba
course_segmente.label_in_singular: Segment
course_segmente.label_in_plural: Segmenty
course_segmente.configureFields.name: Liczba
course_segmente.configureFields.action_add: 'Segment zagregowany'
documentation.label: Dokumentacja
documentation.title: Tytuł
documentation.description: Opis
documentation.type: Typ
documentation.file: Archiwum
documentation.locale: Język
pdf.downloadable: 'Do pobrania'
itinerary.label_in_singular: 'Plan podróży'
itinerary.label_in_plural: Trasy
itinerary.name: Liczba
itinerary.description: Opis
itinerary.tab.courses: Kursy
itinerary.tab.users: Użytkownicy
itinerary.no_courses: 'Do planu podróży nie dodano żadnych kursów'
itinerary.no_users: 'Żaden użytkownik nie został dodany do planu podróży'
itinerary.saving_courses: 'Zapisywanie kursów'
itinerary.find_available_courses: 'Wyszukaj dostępne kursy'
itinerary.find_selected_courses: 'Wyszukaj wybrane kursy'
itinerary.course.position_updated: 'Pozycja kursu została zaktualizowana'
itinerary.course.update_warning: 'Kursy ścieżki zostaną zaktualizowane'
itinerary.user.add_success: 'Użytkownik został pomyślnie dodany'
itinerary.user.remove_success: 'Użytkownik został pomyślnie usunięty'
itinerary.user.confirm_delete: 'Użytkownik utraci dostęp do planu podróży'
itinerary.user.confirm_delete_all: 'Użytkownicy utracą dostęp do planu podróży'
itinerary.manager.add_success: 'Menedżer został pomyślnie dodany'
itinerary.manager.remove_success: 'Menedżer został pomyślnie usunięty'
itinerary.manager.edit_manager: 'Menedżerowie Editar'
itinerary.manager.find_managers: 'Menedżerowie Buscar'
itinerary.manager.confirm_delete: 'Menedżer utraci dostęp do planu podróży'
itinerary.manager.confirm_delete_all: 'Menedżerowie stracą dostęp do planu podróży'
itinerary.filter.added: 'Filtr dodany do planu podróży'
itinerary.filter.removed: 'Filtr usunięty z planu podróży'
itinerary.total_courses: 'Kursy ogółem'
common_areas.cancel: Anuluj
common_areas.add_all: 'Dodaj wszystko'
common_areas.remove_all: 'Usuń wszystko'
user_filter.modify_users: 'Modyfikowanie użytkowników'
user_filter.find_by: Szukaj
common_areas.total: Łącznie
common_areas.confirm_delete: "<p style=\"font-size: 14px;\">&lt;p&gt;&lt;b&gt;<span>Czy naprawdę chcesz usunąć ten element?</span>&lt;/b&gt;&lt;br&gt;</p>\n<p style=\"font-size: 14px;\"><span>Tego działania nie można cofnąć.</span>&lt;/p&gt;</p>"
common_areas.confirm_save: 'czy naprawdę chcesz oszczędzać?'
challenges: Pojedynki
challenges.random: Aleatory
challenges.question: Pytanie
challenges.correct: Prawidłowo
challenges.answer1: 'Odpowiedź 1'
challenges.answer2: 'Odpowiedź 2'
challenges.answer3: 'Odpowiedź 3'
challenges.answer4: 'Odpowiedź 4'
challenges.answer5: 'Odpowiedź 5'
challenges.answer6: 'Odpowiedź 6'
material_course.configureFields.type: 'Typ pliku'
material_course.configureFields.save: 'Materiał był przechowywany prawidłowo'
material_course.configureFields.type_1: Pdf
material_course.configureFields.type_2: Wideo
material_course.configureFields.type_3: Zip/Rar
material_course.configureFields.type_4: Obrazy
material_course.configureFields.type_5: 'Pakiety biurowe'
material_course.configureFields.type_6: 'Blok notatek'
material_course.configureFields.file: Archiwum
material_course.configureFields.no_material: 'Brak dodatkowych materiałów'
material_course.configureFields.question_delete: 'czy naprawdę chcesz usunąć ten materiał?'
material_course.configureFields.question_decition: 'Działania tego nie można później cofnąć'
material_course.configureFields.delete: 'Materiał Smudge'
material_course.placeholder.file: 'Wybierz plik'
material_course.download: Pobierz
taskCourse.configureFields.noFile: 'Nie dodano żadnych plików'
taskCourse.configureFields.question_delete: 'czy naprawdę chcesz usunąć ten plik?'
taskCourse.labelInSingular: 'Praca domowa'
taskCourse.labelInPlural: Zadania
taskCourse.configureFields.dateDelivery: 'Data dostawy'
taskCourse.configureFields.startDate: 'Data rozpoczęcia'
taskCourse.configureFields.visible: Widoczny
taskCourse.configureFields.senTask: 'Zadanie zostało wysłane'
taskCourse.configureFields.senTaskUser: 'Wysłane do'
taskCourse.configureFields.addFile: 'Dodaj plik'
taskCourse.configureFields.state_0: 'W toku'
taskCourse.configureFields.state_1: 'Dostarczone do'
taskCourse.configureFields.state_2: 'W trakcie przeglądu'
taskCourse.configureFields.state_3: Odrzucono
taskCourse.configureFields.state_4: Zatwierdzony
taskCourse.configureFields.files_attachment: Załączniki
taskCourse.configureFields.sendComment: 'Komentarz został wysłany'
taskCourse.configureFields.stateTask: 'Zadanie zmieniło status'
taskCourse.configureFields.history: Historyczny
component_game.true_or_false: 'Prawda/Fałsz lub Kategoryzuj'
component_game.adivina_imagen: 'Odgadnij obraz'
component_game.ordenar_menorMayor: 'Kolejność od najmniejszego do największego'
component_game.parejas: 'W parach'
component_game.rouletteWheel: 'Kółko na listy'
component_game.categorized: Kategoryzuj
component_game.fillgaps: 'Wypełnianie luk'
component_game.guessword: 'Listy zamówień'
component_game.wordle: 'Sekretne słowo'
component_game.lettersoup: 'Zupa z liter'
component_game.videoquiz: 'Quiz wideo'
games.letterwheel: 'Kółko na listy'
games.opciones: 'Wybierz opcję'
games.categorize: Kategoryzuj
games.optiones_empty: 'Musisz dodać co najmniej dwie opcje w celu kategoryzacji.'
games.validate_add_categorize: 'Musisz edytować pole oświadczenia, wybrać poprawną odpowiedź lub wybrać obraz'
games.add_category: 'Dodaj opcję'
games.add_categories: 'Dodaj grupę lub rodzinę'
games.add_word: 'Dodaj słowo'
games.words: Słowa
games.edit_option: 'Opcja edycji'
games.text_common.answer: Odpowiedź
'games.text_common:correct': Prawidłowo
games.text_common.time: Pogoda
games.text_common.word: Słowo
games.text_common.no_questions: 'Brak pytań'
games.text_common.text_question: 'Tekst pytania'
games.text_common.word_question: 'Słowo pytania'
games.text_common.message_guess_word_question: 'Należy wprowadzić tekst pytania'
games.text_common.message_guess_word_word: 'Musisz wpisać słowo z pytania'
games.text_common.message_guess_word_time: 'Należy wprowadzić czas dla pytania'
games.text_common.message_guess_word_answer: 'Odpowiedź musi zawierać tylko jedno słowo'
games.text_common.select_image: 'Wybierz obraz'
games.text_common.ilustre_category: 'Obraz ilustrujący kategorię'
games.text_common.ilustre_question: 'Obraz ilustrujący pytanie'
games.text_common.message_higher_lower: 'Utwórz słowa, które mają pojawić się w grze, a następnie ułóż je w dowolnej kolejności. Kolejność można zmienić, przeciągając słowa.'
games.validate_memory_match: 'Tytuł lub obraz nie zostały jeszcze dodane'
games.help: Pomoc
games.validate_hidden_image: 'Pytanie, rozwiązanie i obrazek są obowiązkowe'
games.fillgap.title: 'jak zbudować grę?'
games.fillgap.message: 'W polu "Dodaj zdanie lub lukę" możesz zaprojektować strukturę gry i zdecydować, czy tekst będzie częścią zdania, czy luką. Po dodaniu luki będzie ona wyświetlana na niebiesko, aby można ją było łatwo zidentyfikować.'
games.fillgap.result_question: 'Wynik meczu'
games.fillgap.word: 'Dodaj zdanie lub lukę'
games.fillgap.add_filler: 'Agregar frase'
games.fillgap.add_gap: 'Dodaj lukę'
games.fillgap.new_option: 'Nowa opcja'
games.fillgap.validate_save: 'Musisz dodać co najmniej jedno zdanie, jedną lukę i dwa wybory'
games.videoquiz.message_validate_answer: 'Musisz dodać co najmniej dwie odpowiedzi, a poprawna odpowiedź nie może być pusta'
games.videoquiz.time_video: 'Czas wideo'
games.videoquiz.savell_all_changes: 'Zapisz wszystkie zmiany'
games.videoquiz.validate_to_add_question: 'Musisz mieć co najmniej jedno pytanie, aby móc zapisać zmiany'
games.videoquiz.validate_letter_soup: 'Wydaje się, że brakuje ci tytułu lub słów'
chapter_type.1: Scorm
chapter_type.2: Treść
chapter_type.3: Ruletka
chapter_type.4: 'Podwójnie lub wcale'
chapter_type.5: Quiz
chapter_type.6: Puzzle
chapter_type.7: 'Sekretne słowo'
chapter_type.8: Pdf
chapter_type.9: Wideo
chapter_type.10: Suwak
chapter_type.11: 'Koła listowe'
chapter_type.12: 'Prawda czy fałsz'
chapter_type.13: Zagadka
chapter_type.14: 'Od najwyższego do najniższego'
chapter_type.15: Pary
chapter_type.16: 'gdzie to pasuje?'
chapter_type.17: 'Wypełniacz luk'
chapter_type.18: 'Listy polecające'
chapter_type.19: Enigma
chapter_type.20: 'Zupa alfabetyczna'
chapter_type.21: 'Quiz wideo'
menu.users.exit_impersonate: 'Wyjście z podszywania się'
menu.forum: Otwór
course.export: 'Kursy eksportowe'
course.export.confirm: 'czy naprawdę chcesz eksportować kursy?'
announcements.configureFields.opinions: Opinie
announcements.configureFields.no_messages: 'Brak wiadomości'
announcements.configureFields.info_max_users: 'Maksymalna liczba użytkowników, z którymi można nawiązać połączenie, wynosi:'
announcements.configureFields.annoucement_all: 'Zwołanie wszystkich'
question_nps.configureFields.source: 'Zgłoś się do'
user.actions.impersonate: 'Podszywanie się'
user.show_cv: 'Wyświetl CV'
user.delete_cv: 'Usuń CV'
stats.export.download_file_pdf: 'Pobierz PDF'
stats.export.download_file_xlsx: 'Pobierz Excel'
stats.segmented.title: 'Segmentowane statystyki'
filter.removed_filter: 'Filtr %s został pomyślnie usunięty.'
filter.added_filter: 'Filtr %s został pomyślnie dodany.'
filter.all_removed: 'Filtry zostały usunięte'
filter.all_added: 'Dodano filtry'
itinerary.chart.users: 'osób ukończyło plan podróży'
itinerary.chart.users_process: 'w toku'
itinerary.chart.users_incomplete: 'bez uruchamiania'
itinerary.chart.users_title: ' osoby przydzielone z'
itinerary.chart.total_time: 'Całkowity łączny czas'
itinerary.chart.avg_time: 'Średni czas na osobę'
itinerary.chart.by_country: 'Trasy według krajów'
itinerary.chart.by_hotel: 'Trasy według ośrodków'
itinerary.chart.by_department: 'Trasy według działów'
itinerary.chart.by_grouping: 'Trasy według grup'
itinerary.users_assign: 'Ludziom przydzielono ten plan podróży'
itinerary.users.progress: 'Postęp w planie podróży'
itinerary.users.download_user: 'Pobierz Excel'
itinerary.courses.selected: 'Wybrane kursy'
itinerary.status.completed: Zakończono
itinerary.status.started: 'W toku'
itinerary.status.unstarted: 'Bez startu'
segmented_stats.title1: 'Osoby przeszkolone'
segmented_stats.title2: Godziny
segmented_stats.title3: Kursy
segmented_stats.title4: Dostęp
segmented_stats.distribution_by_country: 'Dystrybucja według krajów'
segmented_stats.structure: Struktura
segmented_stats.hotel: Hotel
segmented_stats.by_department: 'Przez dział'
segmented_stats.by_school: 'Przez szkołę'
segmented_stats.total_hours: 'Łączna liczba godzin'
segmented_stats.total_avg: 'Średnia liczba godzin'
segmented_stats.structure_avg: 'Średnia struktura'
segmented_stats.structure_total: 'Całkowita struktura'
segmented_stats.hotel_avg: 'Średni hotel'
segmented_stats.hotel_total: 'Hotel ogółem'
segmented_stats.avg: Średnia
segmented_stats.courses_started: 'Rozpoczęte kursy'
segmented_stats.courses_finished: 'Ukończone kursy'
segmented_stats.total_courses_started: 'Łączna liczba rozpoczętych kursów'
segmented_stats.total_courses_finished: 'Łączna liczba ukończonych kursów'
segmented_stats.access_totals: 'Łączna liczba dostępów'
segmented_stats.access_uniques: 'Pojedyncze logowanie'
segmented_stats.certificates: Dyplomy
segmented_stats.total_certificates: 'Łączna liczba przyznanych dyplomów'
library.createdAtView: 'Utworzone przez {email} w dniu {date} o godzinie {time}'
library.no_text_provided: 'Nie wprowadzono żadnego tekstu'
library.maximum_allowed_size_exceeded: '%s: Przekroczono maksymalną dozwoloną liczbę znaków.'
library.category.created: 'Kategoria została pomyślnie utworzona'
library.category.updated: 'Kategoria została pomyślnie zaktualizowana'
library.category.deleted: 'Kategoria została pomyślnie usunięta'
library.category.activated: 'Kategoria została pomyślnie aktywowana'
library.category.deactivated: 'Kategoria została pomyślnie dezaktywowana'
library.library.updated: 'Biblioteka została pomyślnie zaktualizowana'
library.library.created: 'Biblioteka została pomyślnie utworzona'
library.library.deleted: 'Biblioteka została pomyślnie usunięta'
library.library.name_required: 'Nazwa jest obowiązkowa i nie może zawierać więcej niż 100 znaków.'
library.library.type_required: 'Pole "typ" jest wymagane'
library.library.link_required: 'Jeśli typem jest "LINK", należy podać prawidłowy adres URL.'
forum.configureFields.thread: Wątek
forum.configureFields.message: Wiadomość
forum.configureFields.comment: Komentarz
forum.configureFields.title_modal_add: 'Dodaj forum'
forum.configureFields.title_modal_edit: 'Edytuj forum'
course_press.label_in_singular: 'Kurs stacjonarny'
course_press.label_in_plural: 'Kursy na miejscu'
menu.courses_managment.course_sections: Sekcje
common.write: 'Napisz coś'
common_areas.accept: Akceptuj
common_results: wyniki
games.answers: 'Dodaj odpowiedź'
games.text_common.order_ramdom: 'Sortuj losowo'
games.puzzle.description_cropper: 'Następnie wybierz obszar obrazu, który zostanie użyty do utworzenia układanki.'
games.validation_truefalse.question_or_image: 'Musisz wpisać pytanie lub wybrać obraz'
games.help.write_question: 'Napisz pytanie'
games.help.write_word: 'Napisz słowo'
games.help.write_title: 'Pisanie tytułu'
games.help.write_answer: 'Napisz odpowiedź'
games.true: Prawda
games.false: Fałsz
games.edit_video_quiz: 'Wyświetlanie i edytowanie quizu wideo'
games.delete_video_quiz: 'Usuń quiz wideo'
game.feedback.title: 'Aktywacja informacji zwrotnej'
game.feedback.title_positive: 'W przypadku sukcesu'
game.feedback.title_negative: 'W przypadku awarii (opcjonalnie)'
announcements.common.group: Grupa
announcements.common.action_denomination: 'Tytuł Działanie'
announcements.common.modality: Modalność
announcements.common.place_of_instruction: 'Miejsce dostawy'
announcements.common.collaboration_type: 'Rodzaj współpracy'
announcements.common.provider: Dostawca
announcements.common.provider_cif: 'Numer VAT Dostawca'
announcements.observations.costs: Koszty
announcements.observations.course_status: 'Status kursu'
announcements.observations.comunicado_fundae: 'Komunikat FUNDAE'
announcements.observations.comunicado_abilitia: 'Ogłoszenie dla ABILITII'
announcements.observations.economic_module: 'Moduł ekonomiczny'
announcements.observations.travel_and_maintenance: 'Przemieszczenie i konserwacja'
announcements.observations.provider_cost: 'Dostawca kosztów'
announcements.observations.hedima_management_cost: 'Koszt zarządzania HEDIMA (10%)'
announcements.observations.travel_and_maintenance_cost: 'Koszty podróży i utrzymania'
announcements.observations.total_cost: 'Całkowity koszt'
announcements.observations.final_pax: 'Finał PAX'
announcements.observations.maximum_bonus: 'Maksymalny bonus (końcowy PAX)'
announcements.observations.subsidized_amount: 'Bonus Import'
announcements.observations.private_amount: 'Import prywatny'
announcements.observations.provider_invoice_number: 'Nr faktury Dostawca'
announcements.observations.hedima_management_invoice_number: 'Faktura nr HEDIMA Management'
announcements.observations.invoice_status: 'Faktura stanowa'
announcements.observations.observations: Uwagi
announcements.observations.observation: Obserwacja
announcements.course.no_chapter: 'Ten kurs nie ma rozdziałów, ponieważ jest to kurs stacjonarny'
announcements.formativeActionTypes.intern: Wewnętrzny
announcements.formativeActionTypes.extern: Zewnętrzne
announcements.formativeActionTypes.session_congress: Zewnętrzne
common_areas.confirm_file_upload: 'Czy na pewno chcesz przesłać dokument(y)?'
common_areas.confirm_file_delete: 'Czy na pewno usunąć?'
course_section.label_in_singular: Sekcja
course_section.label_in_plural: Sekcje
course_section.configureFields.name: Liczba
course_section.configureFields.description: Opis
course_section.configureFields.active: Activa
course_section.configureFields.sort: Zamówienie
course_section.configureFields.translations: Tłumaczenia
course_section.configureFields.section_name: 'Nazwa sekcji'
course_section.configureFields.categories: Kategorie
user.roles.administrator: Administrator
user.roles.user: Użytkownik
user.roles.tutor: Nauczyciel
user.roles.subsidizer: Inspektor
user.roles.manager: Kierownik
user.roles.manager_editor: 'Menedżer - Redaktor'
user.roles.team_manager: 'Menedżer zespołu'
survey.label_in_plural: Ankiety
course.configureFields.is_main: 'Ten kurs będzie wykorzystywał wyłącznie własne pytania do oceny'
global.error: 'Wystąpił błąd. Spróbuj ponownie później'
quiz.configureFields.title_creation: 'Tworzenie pytań'
quiz.configureFields.question: 'Wypowiedzenie pytania'
quiz.configureFields.question_placeholder: 'Napisz oświadczenie dotyczące pytania'
quiz.configureFields.question_delete: 'czy naprawdę chcesz wyeliminować to pytanie?'
rouletteWord.configureFields.statement: Oświadczenie
rouletteWord.configureFields.answer: Odpowiedź
rouletteWord.configureFields.type_0: 'Zacznij od litery'
rouletteWord.configureFields.type_1: 'Zawiera list'
rouletteWord.configureFields.error.statement.max: 'Instrukcja nie może przekraczać ${max} znaków.'
rouletteWord.configureFields.error.statement.empty: 'Oświadczenie nie może być puste'
rouletteWord.configureFields.error.answer.max: 'Odpowiedź nie może przekraczać {max} znaków'
rouletteWord.configureFields.error.answer.empty: 'Odpowiedź nie może być pusta'
rouletteWord.configureFields.error.answer.starts: 'Odpowiedź musi zaczynać się od litery'
rouletteWord.configureFields.error.answer.includes: 'Odpowiedź musi zawierać literę'
rouletteWord.response.update_letter: 'Dane zostały poprawnie zaktualizowane'
rouletteWord.response.delete_letter: 'Dane zostały pomyślnie usunięte'
trueorFalse.configureFields.true: Prawidłowo
trueorFalse.configureFields.false: Fałszywy
enigma.configureFields.title_creation: 'Tworzenie Enigmy'
puzzle.configureFields.save_image: 'Obraz został pomyślnie zapisany'
puzzle.configureFields.select_correct_answer: 'Należy wybrać jedną poprawną odpowiedź'
puzzle.configureFields.recomendation: Zalecenie
puzzle.configureFields.recomendation_dimentions: '<p>Zalecamy <span class="text-primary"><b>minimalne wymiary 1024 pikseli na stronę</b></span> i <span class="text-primary"><b>maksymalne wymiary 2000 pikseli na stronę</b></span></p>'
puzzle.configureFields.recomendation_description: '<p>Format układanki jest <span class="text-primary"><b>kwadratowy</b></span>, jeśli więc wybierzemy obraz o innych proporcjach, na przykład obraz krajobrazowy, narzędzie pozwoli nam przyciąć, wybierając żądany obszar.</p>'
hiddenword.configureFields.title: 'Tworzenie ukrytego słowa'
hiddenword.configureFields.answers_title: 'Ukryte słowo'
hiddenword.configureFields.answers_placeholder: 'Napisz ukryte słowo'
categorize.configureFields.title_group: 'Grupy lub rodziny'
fillgaps.configureFields.title: 'Tworzenie fraz'
fillgaps.configureFields.fillgap: Wydrążony
fillgaps.configureFields.fillgaps: Otwory
fillgaps.configureFields.type_list: Wykaz
fillgaps.configureFields.type_drag: Przeciąganie
guesword.configureFields.word_title: 'Słowo nieuporządkowane'
guesword.configureFields.word_title_placeholder: 'Napisz słowo, które będzie wyświetlane jako nieuporządkowane'
guesword.configureFields.solution: Rozwiązanie
guesword.configureFields.solution_placeholder: 'Napisz rozwiązanie gry'
guesword.configureFields.help_placeholder: 'Napisz pomoc do gry'
pairs.configureFields.title: 'Tworzenie gry'
pairs.configureFields.placeholder_title: 'Napisz oświadczenie'
pairs.configureFields.create_game: 'Tworzenie gier'
chapter_type.description.22: '<p>Idealne rozwiązanie do tworzenia dynamicznych i atrakcyjnych treści w kursach lub pigułkach szkoleniowych, prezentujące informacje wizualnie i z szeroką gamą interakcji opartych na tekście, obrazach, filmach, dźwięku, łączach multimedialnych, interaktywnych kartach, połączonych scenach itp.</p> '
chapter_type.add.22: 'Tworzenie interaktywnych treści'
games.videoquiz_exist_question: 'Istnieje już pytanie utworzone w tym samym przedziale czasowym.'
chapter_type.22: VCMS
video.configureFields.title: 'Tworzenie quizów wideo'
video.configureFields.add_question: 'Dodaj pytanie'
Next: Następny
hours: Godziny
minutes: Minuty
seconds: Sekundy
field_required: 'Pole wymagane'
field_invalid: 'Nieprawidłowe pole'
field_invalid_format: 'Nieprawidłowy format'
remaining_characters: 'Pozostałe znaki'
minimiun_characters: 'Minimalna liczba znaków'
menu.home.title: 'Przejdź do obszaru użytkownika'
course.season.type.sequential: 'Nawigacja sekwencyjna'
course.season.type.free: 'Nawigacja swobodna'
course.season.type.exam: 'Tryb sprawdzania'
games.fillgap.add_fillgap: 'Dodawanie luk przez kliknięcie określonych słów'
course_section.configureFields.hideCategoryName: 'Ukryj nazwę kategorii'
user.roles.super_administrator: SuperAdministrador
settings.menu.label: Konfiguracja
settings.header.title: Konfiguracja
setting.menu.general: Ogólne
setting.menu.catalog: Katalogi
share: Udział
report.announcement.participants: Uczestnicy
report.announcement.groupCode: 'Kod grupy'
report.announcement.enterpriseProfile: 'Profil firmy'
report.announcement.file: Plik
report.announcement.totalStudents: 'Studenci ogółem'
report.announcement.enterpriseCIF: 'Numer VAT firmy'
report.announcement.advisor: Nauczyciel
course.stats.started: Rozpoczęty
course.stats.ended: Zakończono
course.stats.total_time: 'Całkowity czas'
course.stats.avg_time: 'Całkowity czas'
course.stats.minutes: minuty
course.stats.minute: minuta
course.stats.hours: godziny
course.stats.hour: czas
course.stats.second: drugi
course.stats.seconds: sekundy
question.configureFields.quantity_max_question: 'Maksymalna liczba pytań do wyświetlenia:'
user.configureFields.available_courses: 'Dostępne kursy'
user.configureFields.available_chapter: 'Dostępny rozdział'
user.configureFields.courses_stats.finished: zakończony
user.configureFields.courses_stats.started: rozpoczęty
user.configureFields.courses_stats.available: dostępny
user.configureFields.courses_stats.sent_messages: 'wysłany do'
user.configureFields.courses_stats.received_messages: otrzymany
user.configureFields.courses_stats.others: Inne
user.configureFields.permissions: Zezwolenia
course.configureFields.segments: Segmenty
stats.roles: Role
course.configureFields.language: Język
game.feedback.wrong: 'Przykład: Wow!'
chapter_type.description.23: '<p>Odgrywanie ról to aktywność, w której uczestnicy wcielają się i działają jako fikcyjne postacie, często w określonym otoczeniu lub kontekście. Podczas odgrywania ról uczestnicy tymczasowo przyjmują osobowość, cechy i zachowania postaci, które przedstawiają, wchodząc w interakcje ze sobą zgodnie z okolicznościami i stworzonym wyimaginowanym środowiskiem. Praktyka ta jest stosowana w różnych kontekstach, takich jak gry, terapia, symulacje edukacyjne i zajęcia rekreacyjne, w celu wspierania kreatywności, empatii, rozwiązywania problemów i badania hipotetycznych sytuacji.</p>'
password.uppercase: 'Wymagane 1 lub więcej znaków pisanych wielkimi literami'
password.number: 'Wymagane 1 lub więcej cyfr numerycznych'
password.minimum: 'Hasło musi składać się z co najmniej %s znaków.'
password.disable_3_consecutive_chars: 'Powtarzanie znaku więcej niż 3 razy z rzędu jest niedozwolone.'
password.lowercase: 'Wymagane 1 lub więcej małych liter.'
chapter_type.23: Roleplay
chapter_type.add.23: 'Tworzenie roleplay'
password.special_characters: 'Wymagany 1 lub więcej znaków specjalnych'
roleplay.status.failure: Zawiodłeś
roleplay.status.success: Przeszedłeś
user.configureFields.locale: Język
course.created: 'Kurs został pomyślnie zapisany'
question.configureFields.do_all_questions: 'Wykorzystaj wszystkie pytania'
announcements.news.start_announcement: 'Kurs %course% jest tuż za rogiem!'
announcements.news.finish_announcement: 'Kurs %course% dobiegł końca!'
user.configureFields.dni: DNI
course_section.configureFields.section_aditional: 'Dodatkowe szkolenie'
report.announcement.time_conexion: 'Czas połączenia'
report.announcement.init_finish: 'Początek i koniec'
report.annnouncement.conexions: Połączenia
report.annnouncement.chat_tutor: 'Czat z nauczycielem'
report.annnouncement.first_conexion: 'Pierwsze połączenie'
report.annnouncement.last_conexion: 'Ostatnie połączenie'
generic_token.assistance.success: 'Twoja obecność została pomyślnie zarejestrowana'
generic_token.assistance.user_not_in_group: 'Użytkownik nie należy do grupy sesji'
chat.notification.number_of_messages: 'Masz % nieprzeczytanych wiadomości'
certificate.notification.available: 'Dyplom zwołania kursu %s dostępny do pobrania'
user_fields_fundae.title: 'Dodatkowe pola FUNDAE'
user_fields_fundae.social_security_number: 'Numer ubezpieczenia społecznego'
user_fields_fundae.gender: Płeć
user_fields_fundae.email_work: 'Poczta służbowa'
user_fields_fundae.birthdate: 'Data urodzenia'
user_fields_fundae.dni: DNI
user_fields_fundae.contribution_account: 'Rachunek składek'
user_fields_fundae.incapacity: 'Niezdolność do pracy'
user_fields_fundae.victim_of_terrorism: 'Ofiara terroryzmu'
user_fields_fundae.gender_violence: 'Ofiara przemocy ze względu na płeć'
fundae_assistance_template.main_title: 'KONTROLA OBECNOŚCI NA SZKOLENIACH'
fundae_assistance_template.action_type: 'NAZWA DZIAŁANIA SZKOLENIOWEGO'
fundae_assistance_template.action_code: 'KOD DZIAŁANIA'
fundae_assistance_template.group: GRUPA
fundae_assistance_template.start_at: 'DATA ROZPOCZĘCIA'
fundae_assistance_template.finish_at: 'DATA KONIEC'
fundae_assistance_template.main_formation_teacher: 'TRENER/MENEDŻER DS. SZKOLEŃ'
fundae_assistance_template.session_number: 'SESJA NR.'
fundae_assistance_template.date: DATA
fundae_assistance_template.morning_afternoon: 'JUTRO/PO POŁUDNIU'
fundae_assistance_template.signed: Podpisano
fundae_assistance_template.info_signed_person: 'Trener/Przedstawiciel ds. szkoleń.'
fundae_assistance_template.assistance_data: 'Dane uczestników'
fundae_assistance_template.signatures: PODPISY
fundae_assistance_template.observations: UWAGI
fundae_catalogs.main_page.title: 'Katalogi FUNDAE'
fundae_catalogs.user_company.label_in_plural: Firmy
fundae_catalogs.user_company.label_in_singular: Firma
fundae_catalogs.user_professional_category.label_in_plural: 'Kategorie zawodowe'
fundae_catalogs.user_professional_category.label_in_singular: 'Kategoria profesjonalna'
fundae_catalogs.user_study_level.label_in_plural: 'Poziomy studiów'
fundae_catalogs.user_study_level.label_in_singular: 'Poziom studiów'
fundae_catalogs.user_work_center.label_in_plural: 'Miejsca pracy'
fundae_catalogs.user_work_center.label_in_singular: 'Miejsca pracy'
fundae_catalogs.user_work_department.label_in_plural: 'Działy robocze'
fundae_catalogs.user_work_department.label_in_singular: 'Wydział Pracy'
fundae_catalogs.fields.state.title: Stan
fundae_catalogs.fields.state.active: Aktywny
fundae_catalogs.fields.state.inactive: Nieaktywny
excel.userAnnouncement.sheet1.title: 'Informacje ogólne'
excel.userAnnouncement.sheet1.colum1: 'Liczba tras'
excel.userAnnouncement.sheet1.colum2: 'Liczba użytkowników posiadających plany podróży'
excel.userAnnouncement.sheet1.colum3: 'Liczba kursów w ścieżkach'
excel.userAnnouncement.sheet2.title: 'Trasy katalogowe'
excel.userAnnouncement.sheet2.colum1: Id
excel.userAnnouncement.sheet2.colum2: 'Nazwy tras'
excel.userAnnouncement.sheet2.colum3: Dział
excel.userAnnouncement.sheet2.colum4: Kategoria
excel.userAnnouncement.sheet2.colum5: 'Przydzielone kursy'
excel.userAnnouncement.sheet2.colum6: 'Przydzielone osoby'
excel.userAnnouncement.sheet2.colum7: 'Ludzie ukończyli plan podróży'
excel.userAnnouncement.sheet2.colum8: 'Ludzie w procesie'
excel.userAnnouncement.sheet2.colum9: Nierozpoczynający
excel.userAnnouncement.sheet2.colum10: 'CZAS CALKOWITY AKUMULOWANY'
excel.userAnnouncement.sheet2.colum11: 'REDNI CZAS NA OSOBĘ'
excel.userAnnouncement.sheet3.title: 'Kursy według trasy'
excel.userAnnouncement.sheet3.colum1: Id
excel.userAnnouncement.sheet3.colum2: 'Nazwy tras'
excel.userAnnouncement.sheet3.colum3: 'Nazwa kursu'
excel.userAnnouncement.sheet3.colum4: Zakończono
excel.userAnnouncement.sheet3.colum5: 'W toku'
excel.userAnnouncement.sheet3.colum6: 'Bez startu'
course.message_saved: 'Zapisany kurs'
chapter.configureFields.create_chapter: 'Utwórz rozdział'
user_filter.assign_manual: 'Przypisz ręcznie'
user_filter.assign_filters: 'Przypisywanie według filtrów'
user.configureFields.configureLocale: 'Ustawienia języka'
user.configureFields.configureLocaleAdmin: 'Konfiguracja języka panelu administracyjnego'
user.configureFields.configureLocaleCampus: 'Ustaw język dla kampusu'
stats.export.configsheet.title: Konfiguracja
stats.export.configsheet.content_title: 'Ogólny raport statystyczny'
stats.export.configsheet.content_period: 'Okres objęty ubezpieczeniem (data końcowa)'
stats.export.configsheet.content_filters: 'Filtry aktywne'
stats.export.configsheet.content_period_from: Od
stats.export.configsheet.content_period_to: Do
stats.export.datasheet.title: Dane
stats.export.filter.category: Kategoria
stats.export.filter.departament: Dział
stats.export.filter.gender: Płeć
stats.export.filter.activeUsers: Użytkownicy
stats.export.filter.activeUsers_val_yes: Aktywa
stats.export.filter.activeUsers_val_no: Nieaktywny
stats.export.filter.course_full_title: 'Kurs 100%'
stats.export.filter.course_full_val_yes: Tak
stats.export.filter.course_full_val_no: Nie
stats.export.filter.course_full_descr: '(Uwzględnij tylko kursy, które zostały ukończone we wskazanym okresie)'
stats.export.filter.course_intime_title: 'Żywotność kursu w okresie'
stats.export.filter.course_intime_val_yes: Tak
stats.export.filter.course_intime_val_no: Nie
stats.export.filter.course_intime_descr: '(Uwzględnij tylko kursy, które rozpoczęły się i zakończyły we wskazanym okresie)'
stats.export.filter.course_started_in_period_title: 'Kurs rozpoczęty w zakresie dat'
stats.export.filter.course_started_in_period_val_yes: Tak
stats.export.filter.course_started_in_period_val_no: Nie
stats.export.filter.course_finished_in_period_title: 'Kurs ukończony w zakresie dat'
stats.export.filter.course_finished_in_period_val_yes: Tak
stats.export.filter.course_finished_in_period_val_no: Nie
stats.export.filter.customFilters: Spersonalizowane
stats.content_allusers: 'Wszyscy użytkownicy'
stats.content_inactive: Nieaktywny
stats.content_inactive_f: Nieaktywny
itinerary.user.assign_manual: 'Przypisz ręcznie'
itinerary.user.assign_filter: ' Przypisywanie według filtrów'
itinerary.user.modify_users: 'Ręczne przypisywanie osób'
itinerary.user.filter_find_by: 'Szukaj według'
common_areas.close: Zamknij
itinerary.chart.avg_time_active: '(Łącznie aktywne)'
itinerary.chart.avg_time_all: '(Łącznie przydzielone)'
itinerary.courses.modify: 'Przypisywanie kursów'
itinerary.courses.appliedfilter: ' wyświetlane kursy (filtrowane według'
itinerary.users.appliedfilter: ' wyświetlane osoby (filtrowane według'
itinerary.courses.available: 'Dostępne kursy'
excel.userAnnouncement.sheet1.colum2b: 'Liczba unikalnych użytkowników posiadających plany podróży'
excel.userAnnouncement.sheet1.colum3b: 'Liczba unikalnych kursów w ścieżkach'
common_areas.select_choice: 'Wybierz opcję'
chapter_type.24: LTI
chapter_type.description.24: LTI
lti_chapter.title: 'Rozdział LTI'
lti_chapter.add: 'Dodaj rozdział LTI'
lti_chapter.edit: 'Edycja rozdziału LTI'
lti_chapter.identifier: 'Identyfikator LTI'
lti_chapter.identifier_required: 'Wymagany identyfikator LTI'
chapter_type.add.24: 'Dodaj rozdział LTI'
categoryFilter.label: 'Filtry kategorii'
categoryFilter.title: 'Kategorie filtrów'
user.configureFields.localeCampus: 'Język kampusu'
global.bulk.sheetValidation.error_tab_1: 'Pierwsza zakładka nie nazywa się "Lista formacji".'
global.bulk.sheetValidation.error_tab_2: 'Druga zakładka nie nosi nazwy "Uczestnicy".'
stats.export.user_creation: 'Filtrowanie użytkowników według daty utworzenia'
stats.export.users_export_title: 'Statystyki użytkownika'
chapter_type.validation_course: "\nRozdziału nie można usunąć, ponieważ niektórzy użytkownicy zarejestrowali już w nim aktywność."
user.configureFields.courses_stats.notstarted: nierozpoczęty
course.configureFields.created_at: 'Data utworzenia'
course.configureFields.translate: Tłumaczenie
messages.configureFields.timezone: 'Strefa czasowa'
user.email: 'Adres e-mail'
course.diploma.index: 'Spis treści'
menu.stats.reports.diplomas: 'Raporty i dyplomy'
itinerary.succes.download: 'Raport dotyczący ścieżki jest obecnie przetwarzany i można go znaleźć w zakładce "Raporty i dyplomy"'
user.diploma.generate: 'Generowanie dyplomów'
filters.placeholder: 'Wyszukiwanie według typu'
filters.remove_all: 'Usuń wszystko'
filters.add_all: 'Dodaj wszystko'
announcement.report_group_resume_individual: 'Indywidualne podsumowanie wykonawcze'
announcement.report_downloaded_diploma: 'Pobrany dyplom'
announcements.configureFields.code: 'Nazwa połączenia'
task.status.review: 'W trakcie przeglądu'
task.status.error: 'Błąd systemu'
email.error.subject: 'Błąd w %context% (ID: %id%) - Środowisko: %appName% - Błąd w %context% (ID: %id%) - Środowisko: %appName%'
email.error.subject_no_id: 'Błąd w %context% - Środowisko: %appName%'
email.error.title: 'Błąd wykonania zadania'
email.error.environment: Środowisko
email.error.context: Kontekst
email.error.task_id: 'Identyfikator zadania'
email.error.error_details: 'Szczegóły błędu'
email.error.error_message: 'Komunikat o błędzie'
email.error.error_line: Linia
email.error.error_file: Archiwum
email.error.additional_info: 'Dodatkowe informacje'
email.error.regards: 'Z wyrazami szacunku'
email.error.team: 'Zespół %appName%'
email.zombie.subject: 'Zadanie w stanie ZOMBIE w %context% (ID: %id%) - Środowisko: %appName% (ID: %id%) - Środowisko: %appName% (ID: %id%)'
email.zombie.title: 'Powiadomienie o zadaniu w stanie ZOMBIE'
email.zombie.environment: Środowisko
email.zombie.context: Kontekst
email.zombie.task_id: 'Identyfikator zadania'
email.zombie.marked_as: 'został oznaczony jako'
email.zombie.zombie_status: ZOMBIE
email.zombie.timeout_reason: 'ponieważ przekroczył dozwolony czas wykonania'
email.zombie.check_details: 'Sprawdź panel administracyjny lub konsolę, aby uzyskać więcej szczegółów i podejmij odpowiednie działania'
email.zombie.additional_info: 'Dodatkowe informacje'
email.zombie.regards: 'Z wyrazami szacunku'
email.zombie.team: 'Zespół %appName%'
season.delete: 'Nie można usunąć tego sezonu, obecnie zawiera on powiązane rozdziały'
delete.season.chapters.users: "Nie można usunąć sezonu %seasonName%,\n                     rozdziały (%ChaptesTitles%) mają aktywność użytkownika"
delete.season.danger: 'Sezon nie może zostać wyeliminowany'
stats.task.queued: 'Przyklejona prośba o zadanie'
itinerary.delete.confirm.validation: 'Obecnie plan podróży jest aktywny, aby kontynuować działanie, należy wyłączyć plan podróży'
itinerary.delete.confirm.title: 'czy naprawdę chcesz usunąć plan podróży?'
course.publish.message.active: 'Opublikowany kurs'
course.publish.message.unactive: 'Kurs oznaczony jako niepublikowany'
itinerary.delete.error: 'Plan podróży nie może zostać usunięty'
courser.chaperts.orders.succes: 'Pomyślnie zaktualizowano zamówienie rozdziału'
course.publish.message.unactive.chapters: 'Nie można opublikować, ponieważ kurs jest niekompletny'
course.undelete.message: 'Kurs nie może zostać usunięty, ponieważ zawiera przypisaną zawartość'
user.roles.creator: Twórca
