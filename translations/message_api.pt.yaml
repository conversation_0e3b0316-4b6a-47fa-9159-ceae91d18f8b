message_api.content.scorm_correctly: 'A tempestade começou correctamente'
message_api.content.token_not_send: 'Ficha não enviada'
message_api.content.error_get_content: 'Ocorreu um erro ao tentar iniciar um capítulo:'
message_api.content.scorm_user_not_access: 'O utilizador não tem acesso a este curso'
message_api.content.scorm_not_content: 'O Capítulo não é um pacote de Conteúdos'
message_api.base.credentials_no_correct: 'As credenciais introduzidas não são correctas.'
message_api.base.authorized_access: 'Os dados de acesso não são válidos'
message_api.base.user_successfully: 'Logout do utilizador com sucesso'
message_api.base.error_login: 'Ocorreu um erro ao tentar iniciar a sessão do utilizador - Erro:'
message_api.base.authorized_token_fail: 'Não foi possível ter acesso via SSO. Se você ainda não tiver registrado e ativado seu usuário na plataforma, você deverá fazê-lo para habilitar este tipo de acesso SSO.'
message_api.base.user_not_exists_in_ad: 'As credenciais incorrectas ou utilizador não existem no Active Directory'
message_api.controller.error_get_course: 'Ocorreu um erro ao tentar registar o utilizador - Erro:'
message_api.controller.chapter_init: 'Capítulo correctamente iniciado'
message_api.controller.error_chapter_init: 'Ocorreu um erro ao tentar iniciar um capítulo: '
message_api.controller.error_chapter_update: 'Capítulo correctamente actualizado'
message_api.controller.help_text: 'Ajuda Textos recuperados com sucesso'
message_api.controller.course_not_found: 'Nenhum curso encontrado'
message_api.controller.user_not_access: 'O utilizador não tem acesso a esta disciplina'
message_api.controller.evaluation_course: 'Avaliação apresentada'
message_api.controller.training_itinerary: 'Minha formação'
message_api.controller.continue_training: 'Mais formação de interesse'
message_api.message.not_found: 'Mensagem não encontrada'
message_api.message.not_access: 'Acesso não permitido'
message_api.scorm.init: 'A tempestade começou correctamente'
message_api.scorm.not_package: 'O Capítulo não é um pacote SCORM'
message_api.scorm.button_add_scorm: 'Adicionar pacote de scorm'
message_api.scorm.button_delete_scorm: 'Eliminar pacote de scorm'
message_api.scorm.title_package: 'Pacote Scorm'
message_api.scorm.upload_file_scorm: 'Seleccionar ficheiro .zip'
message_api.scorm.info_upload_scorm: 'Deve carregar o pacote do scorm package'
message_api.notification.not_found: 'Notificação não encontrada'
message_api.player.no_content: 'Este capítulo não tem conteúdo'
message_api.alert.minimal_question: 'Este jogo tem um mínimo de %number% de perguntas.'
message_api.alert.image_puzzle: 'O quebra-cabeça deve ter uma imagem'
message_api.alert.question_correct: 'A esta pergunta você deve acrescentar uma resposta correta'
message_api.alert.chapter_content: 'Este capítulo carece de conteúdo'
message_api.alert.pdf: 'Este capítulo precisa de um pdf'
message_api.alert.video: 'Este capítulo carece de conteúdo'
message_api.alert.slider: 'Este capítulo tem um mínimo de %número% de imagens.'
message_api.alert.scorm: 'Este capítulo precisa de um pacote de Scorm'
message_api.alert.question_hidden: 'Esta pergunta deve conter apenas uma resposta e deve estar correta'
message_api.alert.minimal_pair: 'Este capítulo exige um mínimo de %número% de pares.'
message_api.announcement.call_for_aplications: Convocação
message_api.alert.course_content_incomplete: 'Faltam informações nos capítulos. É favor rever e acrescentar conteúdos para poder prosseguir com a criação do convite à apresentação de propostas.'
message_api.diploma.supered: 'Para a superação'
message_api.diploma.granted: 'Atribuído a'
message_api.diploma.date: Data
message_api.diploma.diploma: Diploma
message_api.diploma.trato: Sr./Sra.
message_api.diploma.nif: 'com número de identificação fiscal'
message_api.diploma.services: 'que presta serviços à Empresa'
message_api.diploma.cif: 'com número de IVA'
message_api.diploma.evaluation: 'Passou a ação de formação com uma avaliação positiva.'
message_api.diploma.code: 'Código AF/GRUPO'
message_api.diploma.between: 'Durante os dias'
message_api.diploma.duration: 'com uma duração total de'
message_api.diploma.fomartionType: 'horas no modo de formação'
message_api.diploma.courseContent: 'Conteúdo do curso na página seguinte'
message_api.diploma.signatureSeal: 'Assinatura e carimbo da entidade responsável pela realização da formação.'
message_api.diploma.signatureDate: 'Data de emissão'
message_api.diploma.acreditation: Acreditação
message_api.diploma.signature: 'Assinatura do participante'
message.api.diploma.type_course_default: 'Educação eletrónica'
message.api.diploma.dates: 'Data de %dateFrom% a %dateTo%'
message.api.diploma.hours: '{0} %count% horas|{1} %count% horas|]1,Inf] %count% horas'
message.api.diploma.duration: Duração
message.api.diploma.with_dni: 'com %nameIdentification% %dni%'
message_api.announcement.cancelation_announcement: 'Anulação da chamada %course%'
message_api.help_category.delete_error: 'Não é possível eliminar a categoria %categoryName%, pois tem conteúdo associado'
