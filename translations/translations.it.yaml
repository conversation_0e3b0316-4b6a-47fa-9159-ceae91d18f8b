challenges.challenge_title: '<PERSON><PERSON> della sfida'
challenges.no_images: 'Nessuna immagine'
challenges.import_questions_to_excel: 'Domand<PERSON> sull''importazione di Excel'
challenges.import_from_excel: 'Importazione da Excel'
challenges.guest: Ospite
challenges.add_user: 'Aggiungi utente'
challenges.performance: Prestazioni
challenges.points: Punti
challenges.downloadtemplate: 'Scarica il modello'
challenges.import: Importazione
observations.addfiles: 'Aggiungi file'
challenges.challenges: Sfida
questions.manageresponse: 'Gestire le risposte'
questions.see_answers: 'Vedere le risposte'
clone_course.title: 'volete davvero duplicare questo corso?'
clone_course.content: 'Questa azione crea una copia dei contenuti del corso.'
clone_course.clonarcourse: 'Corso di clonazione'
fundae_catalogs.user_professional_category.not_label_in_plural: 'Non esistono categorie professionali'
menu.users_managment.nomanagers: 'Nessun manager assegnato'
message_api.forcingdisplaymenu: 'Forzare la visualizzazione del menu'
messages.notcontainchapter: 'Questo capitolo non ha contenuto'
messages.nologinuser: 'Utente non connesso'
messages.seesummary: 'vedi riepilogo'
messages.e_learning: E-learning
messages.editchallenges: 'Modifica sfida'
messages.challengeindex: 'Indice delle sfide'
messages.createnew: 'Creare un nuovo'
messages.createnewchallenge: 'Creare una nuova sfida'
messages.infgroup: 'Informazioni sul gruppo'
message_api.scorm.score_system_desc: 'Per impostazione predefinita, l''azione formativa sarà considerata superata se l''utente "Completa o "Passa" tutta l''attività. Se l''azione formativa ha un sistema di punteggio per essere superata, attivare la seguente opzione:'
message_api.scorm.score_system_label: 'Punteggio minimo (tra 0 e 100)'
message_api.scorm.allow_reset_desc: 'si desidera poter consultare nuovamente l''azione formativa una volta completata?'
message_api.scorm.allow_reset_label: 'Consentire il reset'
message_api.scorm.config_label: 'Configurazione:'
message_api.scorm.config_desc: 'L''attività è completata quando:'
