const { exec } = require('child_process');

class MakeAfterWebpackPlugin {
    apply(compiler) {
        compiler.hooks.afterEmit.tap('MakeAfterWebpackPlugin', () => {
            exec('make set-diploma', (err, stdout, stderr) => {
                if (err) {
                    console.error(`Error ejecutando make: ${stderr}`);
                    process.exit(1);
                }
                console.log(stdout);
            });
        });
    }
}

module.exports = MakeAfterWebpackPlugin;