#index:
#    path: /
#    controller: App\Controller\DefaultController::index
#app.swagger_ui:
# path: /api/doc
#  methods: GET
#  defaults: { _controller: nelmio_api_doc.controller.swagger_ui }

kms_froala_editor:
  resource: '@KMSFroalaEditorBundle/Resources/config/routing.yml'
  prefix:   /froalaeditor

api_refresh_token:
  path:       /api/token/refresh

v2_campus_api:
  resource: '../src/V2/Infrastructure/config/routes/campus.yaml'
  prefix:   /api/v2

v2_admin_api:
    resource: '../src/V2/Infrastructure/config/routes/admin.yaml'
    prefix:   /api/v2/admin

v2_common_api:
  resource: '../src/V2/Infrastructure/config/routes/common.yaml'
  prefix: /api/v2
