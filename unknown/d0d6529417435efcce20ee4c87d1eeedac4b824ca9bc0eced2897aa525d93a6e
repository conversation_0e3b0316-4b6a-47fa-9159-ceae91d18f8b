<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChapterType;
use App\Entity\ChapterTypeTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ChapterType|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChapterType|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChapterType[]    findAll()
 * @method ChapterType[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChapterTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChapterType::class);
    }

    public function getChoices(): array
    {
        $choices = [];

        $chapterTypes = $this->findBy([], ['name' => 'ASC']);
        foreach ($chapterTypes as $chapterType) {
            if ($chapterType->isActive()) {
                $choices[$chapterType->getNormalized()] = $chapterType->getId();
            }
        }

        return $choices;
    }

    public function getChoicesAttr($local = null): array
    {
        $choices = [];

        $chapterTypes = $this->findBy([], ['name' => 'ASC']);
        foreach ($chapterTypes as $chapterType) {
            if ($chapterType->isActive()) {
                /** @var ChapterTypeTranslation $translation */
                $translation = $chapterType->translate($local);
                $description = $translation->getDescription();
                $name = $translation->getName();

                if (empty($description)) {
                    $description = $chapterType->getDescription();
                }

                if (empty($name)) {
                    $name = $chapterType->getName();
                }

                $choices[$chapterType->getNormalized()] = [
                    'icon' => $chapterType->getIcon(),
                    'type' => $chapterType->getType(),
                    'name' => $name,
                    'thumbnail' => $chapterType->getThumbnail(),
                    'video' => $chapterType->getVideo(),
                    'videoEn' => $chapterType->getVideoEn(),
                    'code' => $chapterType->getCode(),
                    'description' => $description,
                    'locale' => $local
                ];
            }
        }

        return $choices;
    }
}
