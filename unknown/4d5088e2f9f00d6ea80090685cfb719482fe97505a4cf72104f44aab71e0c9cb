<?php

declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\Course;
use App\Entity\User;
use App\Service\Permission\CoursePermissionService;
use App\Service\SettingsService;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use Doctrine\ORM\NonUniqueResultException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Core\User\UserInterface;

class CourseVoter extends Voter
{
    public const PUBLISH = 'COURSE_PUBLISH';
    public const CREATE = 'COURSE_CREATE';
    public const UPDATE = 'COURSE_UPDATE';
    public const DELETE = 'COURSE_DELETE';
    public const CREATE_SEASON = 'COURSE_CREATE_SEASON';
    public const EDIT_SEASON = 'COURSE_EDIT_SEASON';
    public const DELETE_SEASON = 'COURSE_DELETE_SEASON';

    public function __construct(private Security $security, private SettingsService $settings, private CoursePermissionService $coursePermissionService)
    {
    }

    protected function supports(string $attribute, $subject): bool
    {
        return \in_array($attribute, [
            self::PUBLISH,
            self::CREATE,
            self::UPDATE,
            self::DELETE,
            self::CREATE_SEASON,
            self::EDIT_SEASON,
            self::DELETE_SEASON,
        ], true) && $subject instanceof Course;
    }

    /**
     * @throws InfrastructureException
     * @throws NonUniqueResultException
     */
    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof UserInterface) {
            return false;
        }

        $result = match ($attribute) {
            self::PUBLISH => $this->canPublish($user),
            self::CREATE => $this->coursePermissionService->canCreate($user),
            self::UPDATE => $this->coursePermissionService->canEdit($user, $subject),
            self::DELETE => $this->coursePermissionService->canDelete($user, $subject),
            self::CREATE_SEASON ,
            self::EDIT_SEASON,
            self::DELETE_SEASON => $this->coursePermissionService->canManageCourseSeason(user: $user, course: $subject),
            default => false,
        };

        return $result;
    }

    private function canPublish(UserInterface $user): bool
    {
        if ($this->security->isGranted(User::ROLE_ADMIN)) {
            return true;
        }

        $neededRole = $this->settings->get('app.permissions.manager.canPublish') ?
            User::ROLE_MANAGER :
            'ROLE_MANAGER_EDITOR';

        return $this->security->isGranted($neededRole);
    }
}
