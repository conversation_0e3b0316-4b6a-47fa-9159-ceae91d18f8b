<template>
  <div class="ItemCard" :class="{ 'selected': isSelected }">
    <div class="body">
      <div class="item-name"><b>{{ name }}</b></div>
      <div class="item-description"> {{ info }}</div>
    </div>
    <div class="controls">
      <Spinner v-if="isLoading"/>
      <span v-else-if="isSelected" class="selected">
        {{ selectedText }}
      </span>
      <button v-else class="btn btn-sm btn-primary" @click="handleClick">
        {{ buttonText }}
      </button>
    </div>
  </div>
</template>

<script>
import Spinner from "../../../../admin/components/base/Spinner.vue";
export default {
  name: "ItemCard",
  components: { Spinner },
  props: {
    name: {
      type: String,
      required: true
    },
    info: {
      type: String
    },
    buttonText: {
      type: String,
      required: true
    },
    isSelected: {
      type: <PERSON>olean,
      default: false
    },
    selectedText: {
      type: String,
      default: () => {
        return 'Already selected';
      }
    }
  },
  data() {
    return {
      isLoading: false,
    }
  },
  methods: {
    handleClick() {
      this.isLoading = true;
      this.$emit('itemSelected');
    }
  },
  watch: {
    isSelected(newVal) {
      this.isLoading = false;
    },
  }
}
</script>
<style scoped lang="scss">
.ItemCard {
  border: solid 1px #e2e2e2;
  background-color: #fff;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: .75rem;

  .body {
    display: flex;
    flex-direction: column;

    .item-description {
      color: #999;
    }
  }

  .controls {
    display: flex;
    justify-content: center;
    align-items: center;

    &:deep(.loader) {
      margin: 0 1.5rem;
      font-size: 20px;
    }
  }
}

.selected {
  opacity: .8;
}
</style>