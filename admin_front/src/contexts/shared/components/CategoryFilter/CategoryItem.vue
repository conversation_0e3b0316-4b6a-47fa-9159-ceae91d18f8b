<template>
  <div
    class="CategoryItem"
    @click="emitActive"
  >
    <span :class="{ disabled: item.disabled, selected: item.selected }">{{ item.name }}</span>
  </div>
</template>

<script setup>
import { CategoryFilterOptionsModel } from '@/contexts/shared/models/categoryFilterOptions.model.js'

const emit = defineEmits(['setActive'])
const props = defineProps({
  item: { type: [CategoryFilterOptionsModel, Object], default: () => ({}) },
  disabled: { type: Boolean, default: false },
})

function emitActive() {
  if (props.disabled || props.item.disabled) {
    return null
  }
  emit('setActive')
}
</script>

<style scoped lang="scss">
.CategoryItem {
  font-weight: bold;
  cursor: pointer;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: none;

  .selected {
    color: var(--color-primary);
  }

  .disabled {
    color: var(--color-neutral-mid-dark);
    text-decoration: line-through;
  }
}
</style>
