export class CategoryFilterOptionsModel {
  constructor({ id = 0, name = '', key = '', status = '', disabled = false, selected = false } = {}) {
    this.id = id || 0
    this.name = name || ''
    this.key = key || `cf_option_${this.id}`
    this.status =
      ['default', 'warning', 'danger', 'info', 'success'].find((AllowedStatus) => AllowedStatus === status) || 'default'
    this.disabled = disabled || false
    this.selected = selected || false
    this.isUpdating = false
  }

  updateSelected(value = false) {
    this.selected = value
  }

  setUpdateStatus(value = false) {
    this.isUpdating = value
  }
}
