import { defineStore } from 'pinia'
import { ref } from 'vue'
import { UserProfileModel } from '@/contexts/users/models/userProfile.model.js'
import ApiService from '@/core/services/api.service.js'
import { USER_API_ROUTES } from '@/contexts/users/constants/users.constants.js'
import { toast } from 'vue3-toastify'

export const useUserProfileStore = defineStore('userProfileStore', () => {
  const isLoading = ref(false)
  const loadingError = ref(false)
  const userData = ref(new UserProfileModel())

  async function loadProfile(id = 0) {
    userData.value = new UserProfileModel()
    loadingError.value = false
    isLoading.value = true
    const parsedUrl = ApiService.setParams(USER_API_ROUTES.PROFILE.INIT_DATA, { id })
    const { error, data } = await ApiService.get(parsedUrl)
    isLoading.value = false
    if (error) {
      loadingError.value = true
      return toast.error(error.message)
    }
    userData.value = new UserProfileModel(data)
  }

  return { isLoading, loadingError, userData, loadProfile }
})
