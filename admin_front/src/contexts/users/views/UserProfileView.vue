<template>
  <div class="UserProfileView">
    <LayoutPageTitle
      :links="links"
      :name="$t('ANNOUNCEMENT.ERROR_TITLES.USER_PROFILES')"
    />
    <BaseSpinner v-if="isLoading" />
    <NotFound
      v-else-if="loadingError"
      route-name="users"
    />
    <div
      v-else
      class="profileContent"
    >
      <LayoutPageActions v-if="userData?.showActions">
        <BaseButton
          v-if="userData?.actions?.deletable"
          type="danger"
          @click="() => console.log('Action 1')"
        >
          {{ $t('COMMON.CLEAR') }}
        </BaseButton>
        <BaseButton
          v-if="userData?.actions?.impersonate"
          @click="() => console.log('Action 1')"
        >
          {{ $t('ANNOUNCEMENT.STUDENTTAB.IMPERSONATE') }}
        </BaseButton>
        <BaseButton
          v-if="userData?.actions?.editable"
          @click="() => console.log('Action 1')"
        >
          {{ $t('COMMON.EDIT') }}
        </BaseButton>
      </LayoutPageActions>
      <UserInfoView :user="userData" />
      <BaseTabs
        v-bind="tabsConfig"
        @change="(tab) => (tabsConfig.current = tab)"
      >
        <Component
          :is="currentTabView"
          v-bind="tabContent"
          v-on="tabEmitsHandler"
        />
      </BaseTabs>
    </div>
  </div>
</template>

<script setup>
import { useUserProfileComposable } from '@/contexts/users/composables/userProfile.composable.js'
import LayoutPageTitle from '@/contexts/shared/components/LayoutPageTitle.vue'
import BaseSpinner from '@/contexts/shared/components/BaseSpinner.vue'
import NotFound from '@/contexts/shared/components/NotFound.vue'
import UserInfoView from '@/contexts/users/components/profile/UserInfoView.vue'
import BaseTabs from '@/contexts/shared/components/BaseTabs.vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import LayoutPageActions from '@/contexts/shared/components/LayoutPageActions.vue'

const { tabsConfig, currentTabView, tabContent, tabEmitsHandler, links, isLoading, loadingError, userData } =
  useUserProfileComposable()
</script>

<style scoped lang="scss">
.UserProfileView {
  .profileContent {
    min-height: 90svh;
    display: grid;
    grid-template-rows: auto 1fr;
  }

  :deep(.buttonContainer) {
    text-align: right;
  }
}
</style>
