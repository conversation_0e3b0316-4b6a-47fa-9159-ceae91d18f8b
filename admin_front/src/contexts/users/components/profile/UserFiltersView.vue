<template>
  <div class="UserFiltersView">
    <div class="buttonContainer">
      <BaseButton @click="emit('open')">{{ $t('COMMON.EDIT') }}</BaseButton>
    </div>
    <FormGroup
      icon="tag"
      :title="$t(title)"
    >
      {{ $t(description) }}
      <div class="filterListContainer">
        <FilterBadges
          v-for="item in filterList"
          :key="item.key"
          :item="item"
        />
      </div>
    </FormGroup>

    <BaseModal
      v-if="filterModal.open"
      size="l"
      :title="$t(modalTitle)"
      @close="emit('close')"
    >
      <CategoryFilters
        :options="filterModal.options"
        @set-category="(data) => console.log(data)"
        @add="(data) => console.log(data)"
        @remove="(data) => console.log(data)"
        @add-all="(data) => console.log(data)"
        @remove-all="(data) => console.log(data)"
      />
    </BaseModal>
  </div>
</template>

<script setup>
import FormGroup from '@/contexts/shared/components/FormGroup.vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import FilterBadges from '@/contexts/users/components/profile/FilterBadges.vue'
import BaseModal from '@/contexts/shared/components/BaseModal.vue'
import CategoryFilters from '@/contexts/shared/components/CategoryFilter/CategoryFilters.vue'

const emit = defineEmits(['close', 'open'])
defineProps({
  filterList: { type: Array, default: () => [] },
  title: { type: String, default: 'ANNOUNCEMENT_OBSERVATION.FILTER_INFO' },
  modalTitle: { type: String, default: 'ANNOUNCEMENT_OBSERVATION.FILTER_INFO' },
  description: { type: String, default: 'USERS.FORM.FILTER1_DESCRIPTION' },
  filterModal: { type: Object, default: () => ({}) },
})
</script>
<style scoped lang="scss">
.UserFiltersView {
  .filterListContainer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1rem 0;
  }
}
</style>
