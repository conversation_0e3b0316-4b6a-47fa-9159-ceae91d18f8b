import { computed, inject, onMounted, ref } from 'vue'
import { PageTitleModel } from '@/contexts/shared/models/pageTitle.model.js'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { userFormStore } from '@/contexts/users/stores/userForm.store.js'
import { storeToRefs } from 'pinia'
import { useTimeZoneListStore } from '@/contexts/shared/stores/timezone.store.js'
import { useRoleListStore } from '@/contexts/shared/stores/role.store.js'
import { useCompanyListStore } from '@/contexts/shared/stores/company.store.js'
import { hasErrors } from '@/contexts/shared/utils/validator.utils.js'
import SelectOptionModel from '@/contexts/shared/models/selectOption.model.js'

export function useFormComposable() {
  const { loadUserData, loadExtraData, createUser, updateUser } = userFormStore()
  const { userData, successSave, extraData, isLoading, loadingError } = storeToRefs(userFormStore())
  const i18n = useI18n()
  const route = useRoute()
  const router = useRouter()
  const links = ref([])

  function loadPreData() {
    const { loadTimeZoneList } = useTimeZoneListStore()
    const { loadRoleList } = useRoleListStore()
    // const { loadCompanyList } = useCompanyListStore()

    loadTimeZoneList().catch()
    loadRoleList().catch()
    // loadCompanyList().catch()
  }

  async function initPageConfig(id = 0) {
    links.value = [new PageTitleModel({ id: 1, title: i18n.t('USER.LABEL_IN_PLURAL'), name: 'users' })]
    await loadUserData(id)
    await loadExtraData()
    userData.value.updateExtraFields(extraData.value)
    if (!loadingError.value)
      links.value.push(
        new PageTitleModel({ id: 2, title: i18n.t(userData.value.id ? 'USER.UPDATE_USER' : 'USER.CREATE_USER') })
      )
    title.value = userData.value.id ? 'USER.UPDATE_USER' : 'USER.CREATE_USER'
  }

  const { timeZoneOptions } = storeToRefs(useTimeZoneListStore())
  const { roleOptions } = storeToRefs(useRoleListStore())
  const { companyOptions } = storeToRefs(useCompanyListStore())
  const { localeOptions: locales } = inject('LayoutProps')
  const localeOptions = computed(() =>
    (locales.value.campus || []).map(({ label, value }) => new SelectOptionModel({ label, value }))
  )

  function validateForm() {
    userData.value.setErrors()
    const schema = userData.value.getValidationSchema()
    const errors = hasErrors(userData.value.getPayload(), schema)
    if (errors.length)
      userData.value.setErrors(errors.reduce((acc, cur) => ({ ...acc, [cur.errorKey]: i18n.t(cur.message) }), {}))
    return !!errors.length
  }

  async function submitForm() {
    const hasErrors = validateForm()
    if (hasErrors) {
      return null
    }

    const payload = userData.value.getPayload()
    if (payload?.id) await updateUser(userData.value.id, payload)
    else await createUser(payload)
  }

  async function submitAndReset() {
    await submitForm()
    if (successSave.value) {
      await initPageConfig()
    }
  }

  async function submitAndRedirect() {
    await submitForm()
    if (successSave.value) setTimeout(() => router.push({ name: 'users' }).catch(), 500)
  }

  const title = ref('')
  onMounted(async () => {
    loadPreData()
    await initPageConfig(+route.params?.id || 0)
  })

  return {
    userData,
    links,
    title,
    timeZoneOptions,
    roleOptions,
    companyOptions,
    localeOptions,
    isLoading,
    loadingError,
    submitAndReset,
    submitAndRedirect,
  }
}
