<template>
  <div class="AppHeader">
    <div class="link">
      <div id="menuIcon"></div>
      <span>{{ $t('COMMON.HEADER.TITLE') }}</span>
      <div id="searchContainer"></div>
    </div>
    <div class="actions">
      <div
        v-on-click-outside="handlingUserPopupClose"
        class="userInfo"
        @click="openUserPopup = !openUserPopup"
      >
        <Icon icon="user-circle" />
        <span>{{ user.fullName }}</span>
        <HeaderUserPopup
          v-show="openUserPopup"
          :full-name="user.fullName"
        />
      </div>
      <div
        v-show="route.name !== ROUTE_NAMES.NOT_FOUND"
        class="langSelector"
        @click="openLangForm = true"
      >
        <Icon icon="globe" />
        <HeaderLangForm
          v-show="openLangForm"
          @close="handlingLangFormClose"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { inject, ref } from 'vue'
import HeaderUserPopup from '@/contexts/layout/components/HeaderUserPopup.vue'
import HeaderLangForm from '@/contexts/layout/components/HeaderLangForm.vue'
import { useRoute } from 'vue-router'
import { ROUTE_NAMES } from '@/core/constants/router.constants.js'

const openUserPopup = ref(false)
function handlingUserPopupClose() {
  if (!openUserPopup.value) {
    return null
  }
  openUserPopup.value = false
}

const openLangForm = ref(false)
function handlingLangFormClose() {
  if (!openLangForm.value) {
    return null
  }
  openLangForm.value = false
}

const user = inject('user')
const route = useRoute()
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.AppHeader {
  width: clamp(300px, 100svw, 1700px);
  margin: 0 auto;
  padding: 2rem 1rem 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: space-between;
  color: var(--header-text-color);
  user-select: none;

  .link {
    color: var(--header-link-color);
    gap: 0.5rem 2rem;
    flex-wrap: wrap;
  }

  .link,
  .actions,
  .userInfo {
    display: flex;
    align-items: center;
  }

  .actions {
    gap: 1.5rem;
    margin-inline: auto 0;
  }

  .userInfo,
  .langSelector {
    position: relative;
  }

  .userInfo {
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
  }

  .langSelector {
    background-color: var(--color-neutral-mid-dark);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;

    &:hover {
      background-color: var(--color-neutral-dark);
    }
  }

  #searchContainer {
    width: 250px;
  }

  #menuIcon {
    display: none;
  }

  @media #{breakpoint.$breakpoint-sm} {
    #menuIcon {
      display: initial;
    }
  }
}
</style>
