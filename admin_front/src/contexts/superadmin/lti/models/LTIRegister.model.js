import Joi from 'joi'

export default class LTIRegisterModel {
  constructor({ id = '', name = '', client_id = '' } = {}) {
    this.id = id || ''
    this.name = name || ''
    this.clientId = client_id || ''
    this.isUpdating = false
    this.isRemoving = false
    this.setErrors()
  }

  setErrors(errors = {}) {
    this.errors = errors || {}
  }

  getValidationSchema() {
    return Joi.object({
      name: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      client_id: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
    })
  }

  getPayload() {
    return {
      name: this.name,
      client_id: this.clientId,
    }
  }
}
