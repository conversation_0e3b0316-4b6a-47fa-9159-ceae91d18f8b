import Joi from 'joi'

export default class LTIDeploymentModel {
  constructor({ id = '', name = '', deployment_id = '' } = {}) {
    this.id = id || ''
    this.name = name || ''
    this.deploymentId = deployment_id || ''
    this.isUpdating = false
    this.isRemoving = false
    this.setErrors()
  }

  setErrors(errors = {}) {
    this.errors = errors || {}
  }

  getValidationSchema() {
    return Joi.object({
      name: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      deployment_id: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
    })
  }

  getPayload() {
    return {
      name: this.name,
      deployment_id: this.deploymentId,
    }
  }
}
