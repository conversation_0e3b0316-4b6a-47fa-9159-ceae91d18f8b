import Joi from 'joi'

export default class LTIPlatformModel {
  constructor({
    name = '',
    audience = '',
    oidc_authentication_url = '',
    oauth2_access_token_url = '',
    jwks_url = '',
  } = {}) {
    this.name = name || ''
    this.audience = audience || ''
    this.oidcURL = oidc_authentication_url || ''
    this.oauth2TokenUrl = oauth2_access_token_url || ''
    this.jwks = jwks_url || ''
    this.isUpdating = false
    this.setErrors()
  }

  setErrors(errors = {}) {
    this.errors = errors || {}
  }

  getValidationSchema() {
    return Joi.object({
      name: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      audience: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      oidc_authentication_url: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      oauth2_access_token_url: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      jwks_url: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
    })
  }

  getPayload() {
    return {
      name: this.name,
      audience: this.audience,
      oidc_authentication_url: this.oidcURL,
      oauth2_access_token_url: this.oauth2TokenUrl,
      jwks_url: this.jwks,
    }
  }
}
