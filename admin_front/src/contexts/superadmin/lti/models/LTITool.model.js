import Joi from 'joi'

export default class LTIToolModel {
  constructor({
    name = '',
    audience = '',
    oidc_initiation_url = '',
    launch_url = '',
    deep_linking_url = '',
    jwks_url = '',
  } = {}) {
    this.name = name || ''
    this.audience = audience || ''
    this.oidcURL = oidc_initiation_url || ''
    this.launchURL = launch_url || ''
    this.deepLink = deep_linking_url || ''
    this.jwks = jwks_url || ''
    this.isUpdating = false
    this.setErrors()
  }

  setErrors(errors = {}) {
    this.errors = errors || {}
  }

  getValidationSchema() {
    return Joi.object({
      name: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      audience: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      oidc_initiation_url: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      launch_url: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      deep_linking_url: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      jwks_url: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
    })
  }

  getPayload() {
    return {
      name: this.name,
      audience: this.audience,
      oidc_initiation_url: this.oidcURL,
      launch_url: this.launchURL,
      deep_linking_url: this.deepLink,
      jwks_url: this.jwks,
    }
  }
}
