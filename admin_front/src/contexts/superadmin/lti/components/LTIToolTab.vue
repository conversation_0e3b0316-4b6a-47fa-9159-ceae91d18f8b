<template>
  <div class="LTIToolTab">
    <div class="formData">
      <BaseInput
        v-model="innerValue.name"
        name="name"
        :label="$t('NAME')"
        :error="innerValue.errors?.name"
      />
      <BaseInput
        v-model="innerValue.audience"
        name="audience"
        :label="$t('COURSE.AUDIENCE.TITLE')"
        :error="innerValue.errors?.audience"
      />
      <BaseInput
        v-model="innerValue.oidcURL"
        name="oidcURL"
        label="OIDC Init URL"
        placeholder="https://example-valid-url.com"
        :error="innerValue.errors?.oidc_initiation_url"
      />
      <BaseInput
        v-model="innerValue.launchURL"
        name="launchURL"
        label="Launch Url"
        placeholder="https://example-valid-url.com"
        :error="innerValue.errors?.launch_url"
      />
      <BaseInput
        v-model="innerValue.deepLink"
        name="deepLink"
        label="DeepLink Url"
        placeholder="https://example-valid-url.com"
        :error="innerValue.errors?.deep_linking_url"
      />
      <BaseInput
        v-model="innerValue.jwks"
        name="jwks"
        label="JWKS URL"
        placeholder="https://example-valid-url.com/jwks-value"
        :error="innerValue.errors?.jwks_url"
      />
    </div>
    <div class="formButtonContainer">
      <BaseButton @click="emit('save')">{{ $t('LIBRARY.SAVE') }}</BaseButton>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import LTIToolModel from '@/contexts/superadmin/lti/models/LTITool.model.js'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'

const emit = defineEmits(['update:content', 'save'])
const props = defineProps({
  content: { type: [LTIToolModel, Object], default: () => ({}) },
})
const innerValue = computed({
  get: () => props.content,
  set: (newValue) => {
    emit('update:content', newValue)
  },
})
</script>
