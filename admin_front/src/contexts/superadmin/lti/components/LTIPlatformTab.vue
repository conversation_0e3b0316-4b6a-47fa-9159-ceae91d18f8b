<template>
  <div class="LTIPlatformTab">
    <div class="formData">
      <BaseInput
        v-model="innerValue.name"
        name="name"
        :label="$t('NAME')"
        :error="innerValue.errors?.name"
      />
      <BaseInput
        v-model="innerValue.audience"
        name="audience"
        :label="$t('COURSE.AUDIENCE.TITLE')"
        :error="innerValue.errors?.audience"
      />
      <BaseInput
        v-model="innerValue.oidcURL"
        name="oidcURL"
        label="OIDC Auth URL"
        placeholder="https://example-valid-url.com"
        :error="innerValue.errors?.oidc_authentication_url"
      />
      <BaseInput
        v-model="innerValue.oauth2TokenUrl"
        name="oauth2TokenUrl"
        label="OAuth2 Token Url"
        placeholder="https://example-valid-url.com/lti1p3/auth/:identifier/token"
        :error="innerValue.errors?.oauth2_access_token_url"
      />
      <BaseInput
        v-model="innerValue.jwks"
        name="jwks"
        label="JWKS URL"
        placeholder="https://example-valid-url.com/jwks-value"
        :error="innerValue.errors?.jwks_url"
      />
    </div>
    <div class="formButtonContainer">
      <BaseButton @click="emit('save')">{{ $t('LIBRARY.SAVE') }}</BaseButton>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import LTIPlatformModel from '@/contexts/superadmin/lti/models/LTIPlatform.model.js'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'

const emit = defineEmits(['update:content', 'save'])
const props = defineProps({
  content: { type: [LTIPlatformModel, Object], default: () => ({}) },
})
const innerValue = computed({
  get: () => props.content,
  set: (newValue) => {
    emit('update:content', newValue)
  },
})
</script>
