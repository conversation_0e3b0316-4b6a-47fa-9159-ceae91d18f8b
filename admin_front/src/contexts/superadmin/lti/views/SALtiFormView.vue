<template>
  <div class="SALtiFormView">
    <LayoutPageTitle
      name="LTI Config"
      :links="links"
    />
    <BaseSpinner v-if="formData.isLoading" />
    <NotFound
      v-else-if="formData.fails"
      route-name="super-admin-lti"
    />
    <main v-else>
      <header class="container">
        <h3>{{ $t('LTI.HOME.HEADER2') }}: {{ formData.register.clientId }}</h3>
        <BaseBadge
          label="ID"
          type="primary"
          :value="`#${formData.register.id}`"
        />
      </header>
      <BaseTabs
        v-bind="tabsConfig"
        @change="(tab) => (tabsConfig.current = tab)"
      >
        <Component
          :is="currentTabView"
          v-bind="tabContent"
          v-on="tabEmitsHandler"
        />
      </BaseTabs>
    </main>
  </div>
</template>

<script setup>
import NotFound from '@/contexts/shared/components/NotFound.vue'
import BaseSpinner from '@/contexts/shared/components/BaseSpinner.vue'
import BaseTabs from '@/contexts/shared/components/BaseTabs.vue'
import { useLTIFormComposable } from '@/contexts/superadmin/lti/composables/LTIForm.composable.js'
import LayoutPageTitle from '@/contexts/shared/components/LayoutPageTitle.vue'
import BaseBadge from '@/contexts/shared/components/BaseBadge.vue'

const { links, formData, tabsConfig, currentTabView, tabContent, tabEmitsHandler } = useLTIFormComposable()
</script>

<style scoped lang="scss">
.SALtiFormView {
  display: grid;
  main {
    display: grid;
    grid-template-rows: auto 1fr;
  }
  header {
    width: 100%;
    margin: 2rem auto;
    padding-inline: 1rem;
  }

  :deep(.BaseTabs) {
    .formData {
      display: grid;
      gap: 1rem 2rem;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }

    .formButtonContainer {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin: 1rem 0 0;
    }
  }
}
</style>
