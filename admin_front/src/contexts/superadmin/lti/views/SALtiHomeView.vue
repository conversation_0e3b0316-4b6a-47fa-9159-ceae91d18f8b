<template>
  <div class="SALtiHomeView">
    <HomeHeader
      :links="links"
      name="LTI Advantage"
      :image="image"
    >
      <BaseButton @click="createNewRegister"><Icon icon="plus" /> {{ $t('LTI.HOME.NEW') }} </BaseButton>
    </HomeHeader>

    <main>
      <TableContainer>
        <table>
          <thead>
            <tr>
              <th class="id">{{ $t('LTI.HOME.HEADER1') }}</th>
              <th>{{ $t('SUBSCRIPTION.NAME') }}</th>
              <th>{{ $t('LTI.HOME.HEADER2') }}</th>
              <th>{{ $t('PAGES.ACTIONS') }}</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in registerList"
              v-show="!isLoading"
              :key="item.id"
            >
              <td class="id">{{ item.id }}</td>
              <td>{{ item.name }}</td>
              <td>{{ item.clientId }}</td>
              <td>
                <DropdownMenu>
                  <span @click="goToConfiguration(item.id)">{{ $t('LTI.HOME.CONFIG') }}</span>
                  <span @click="updateRegister(item)">{{ $t('EDIT') }}</span>
                  <span
                    class="danger"
                    @click="openRemoveDialog(item)"
                    >{{ $t('DELETE') }}</span
                  >
                </DropdownMenu>
              </td>
            </tr>
            <tr v-if="isLoading || !registerList.length">
              <td colspan="4">{{ $t(isLoading ? 'LOADING' : 'NO_DATA') }}</td>
            </tr>
          </tbody>
        </table>
      </TableContainer>
    </main>

    <BaseModal
      v-if="registerForm.open"
      :title="$t(registerForm.data.id ? 'LTI.HOME.UPDATE' : 'LTI.HOME.NEW')"
      @close="registerForm.open = false"
    >
      <BaseInput
        v-if="registerForm.data.id"
        v-model="registerForm.data.id"
        name="id"
        class="idInput"
        :label="$t('LTI.HOME.HEADER1')"
        disabled
      />
      <div class="formContent">
        <BaseInput
          v-model="registerForm.data.name"
          name="name"
          required
          :label="$t('SUBSCRIPTION.NAME')"
          :error="registerForm.data.errors?.name"
        />
        <BaseInput
          v-model="registerForm.data.clientId"
          name="clientId"
          required
          :label="$t('LTI.HOME.HEADER2')"
          :error="registerForm.data.errors?.client_id"
        />
      </div>
      <div class="buttonContainer">
        <BaseButton @click="submitForm">{{ $t('ALERTIFY.OK') }}</BaseButton>
        <BaseButton
          type="danger"
          @click="registerForm.open = false"
        >
          {{ $t('ALERTIFY.CANCEL') }}
        </BaseButton>
      </div>
    </BaseModal>

    <BaseDialog
      :open-dialog="!!selectedItem.open"
      :title="$t('LTI.HOME.REMOVE')"
      :content="`#${selectedItem?.register?.id}`"
      @confirm="removeRegister"
      @close="selectedItem.open = false"
    />
  </div>
</template>

<script setup>
import HomeHeader from '@/contexts/shared/components/HomeHeader.vue'
import { useLTIHomeComposable } from '@/contexts/superadmin/lti/composables/LTIHome.composable.js'
import image from '@/contexts/superadmin/assets/images/sa_default_home.svg'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import BaseModal from '@/contexts/shared/components/BaseModal.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import DropdownMenu from '@/contexts/shared/components/DropdownMenu.vue'
import BaseDialog from '@/contexts/shared/components/BaseDialog.vue'
import TableContainer from '@/contexts/shared/components/TableContainer.vue'

const {
  links,
  isLoading,
  registerList,
  registerForm,
  selectedItem,
  createNewRegister,
  submitForm,
  updateRegister,
  openRemoveDialog,
  removeRegister,
  goToConfiguration,
} = useLTIHomeComposable()
</script>

<style scoped lang="scss">
.SALtiHomeView {
  main {
    padding: 0 3rem 2rem;
  }
  .idInput {
    margin-bottom: 1rem;
  }
  .formContent {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .buttonContainer {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
  }

  table {
    td,
    th {
      text-align: center;
    }

    td {
      padding: 0.5rem 0;
    }

    .id {
      width: 400px;
    }
  }

  .DropdownMenu {
    margin: 0 auto;
  }
}
</style>
