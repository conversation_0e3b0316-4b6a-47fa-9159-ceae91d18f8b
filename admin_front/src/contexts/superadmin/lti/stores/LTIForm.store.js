import { defineStore } from 'pinia'
import { reactive, ref } from 'vue'
import LTIRegisterModel from '@/contexts/superadmin/lti/models/LTIRegister.model.js'
import LTIPlatformModel from '@/contexts/superadmin/lti/models/LTIPlatform.model.js'
import LTIToolModel from '@/contexts/superadmin/lti/models/LTITool.model.js'
import LTIDeploymentModel from '@/contexts/superadmin/lti/models/LTIDeployment.model.js'
import ApiService from '@/core/services/api.service.js'
import { LTI_API_ROUTES } from '@/contexts/superadmin/lti/constants/LTI.constants.js'
import { toast } from 'vue3-toastify'
import { useI18n } from 'vue-i18n'

export const useLTIFormStore = defineStore('ltiFormStore', () => {
  const i18n = useI18n()
  const formData = reactive({
    isLoading: false,
    fails: false,
    register: new LTIRegisterModel(),
    platform: new LTIPlatformModel(),
    tool: new LTIToolModel(),
    deployments: [],
  })

  function setDataValues(data = {}) {
    formData.register = new LTIRegisterModel({ id: data?.id, name: data?.name, client_id: data?.client_id })
    formData.platform = new LTIPlatformModel(data?.platform)
    formData.tool = new LTIToolModel(data?.tool)
    formData.deployments = (data?.deployments || []).map((deployment) => new LTIDeploymentModel(deployment))
  }

  async function initFormData(registerID = '') {
    formData.isLoading = true
    setDataValues({})
    const parsedUrl = ApiService.setParams(LTI_API_ROUTES.FORM.INIT, { registerID })
    const { error, data } = await ApiService.get(parsedUrl)
    formData.isLoading = false
    if (error) {
      formData.fails = true
      return toast.error(error.message)
    }
    formData.fails = false
    setDataValues(data)
  }
  async function savePlatform(payload = {}) {
    const parsedUrl = ApiService.setParams(LTI_API_ROUTES.FORM.SAVE_PLATFORM, { registerID: formData.register.id })
    const { error } = await ApiService.post(parsedUrl, payload)
    return openToast(error)
  }
  async function saveTool(payload = {}) {
    const parsedUrl = ApiService.setParams(LTI_API_ROUTES.FORM.SAVE_TOOL, { registerID: formData.register.id })
    const { error } = await ApiService.post(parsedUrl, payload)
    return openToast(error)
  }
  const isLoading = ref(false)
  async function loadDeploymentList() {
    isLoading.value = true
    const parsedUrl = ApiService.setParams(LTI_API_ROUTES.FORM.DEPLOYMENT.INIT, {
      registerID: formData.register.id,
    })
    const { error, data } = await ApiService.get(parsedUrl)
    isLoading.value = false
    if (error) {
      return toast.error(error.message)
    }
    formData.deployments = (data || []).map((deployment) => new LTIDeploymentModel(deployment))
  }
  async function createDeployment(payload = {}) {
    const parsedUrl = ApiService.setParams(LTI_API_ROUTES.FORM.DEPLOYMENT.CREATE, {
      registerID: formData.register.id,
    })
    const { error } = await ApiService.post(parsedUrl, payload)
    return openToast(error)
  }
  async function updateDeployment(deploymentID = '', payload = {}) {
    const parsedUrl = ApiService.setParams(LTI_API_ROUTES.FORM.DEPLOYMENT.UPDATE, {
      registerID: formData.register.id,
      deploymentID,
    })
    const { error } = await ApiService.put(parsedUrl, payload)
    return openToast(error)
  }
  async function deleteDeployment(deploymentID = '') {
    const parsedUrl = ApiService.setParams(LTI_API_ROUTES.FORM.DEPLOYMENT.REMOVE, {
      registerID: formData.register.id,
      deploymentID,
    })
    const { error } = await ApiService.delete(parsedUrl)
    if (error) {
      toast.error(error)
      return false
    }
    toast.success(i18n.t('DELETE_SUCCESS'))
    return true
  }

  function openToast(error) {
    if (error) toast.error(error.message)
    else toast.success(i18n.t('CATALOG.SAVED'))
    return !error
  }

  return {
    formData,
    isLoading,
    initFormData,
    savePlatform,
    saveTool,
    loadDeploymentList,
    createDeployment,
    updateDeployment,
    deleteDeployment,
  }
})
