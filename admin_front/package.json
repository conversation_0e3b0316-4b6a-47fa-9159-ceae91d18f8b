{"name": "demo-app", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "bun ./src/core/scripts/server.scripts.js", "compile": "bun ./src/core/scripts/build.scripts.js", "build-favicon": "bun ./src/core/scripts/favicon.scripts.js", "locales": "bun ./src/core/scripts/locales.scripts.js", "new-client": "bun ./src/core/scripts/clientsFactory.scripts.js", "format": "bun ./src/core/scripts/format.scripts.js", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "tests": "bun ./src/core/scripts/tests.scripts.js", "prettier": "prettier --write src/"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@vuepic/vue-datepicker": "^11.0.2", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "4", "vue3-select-component": "^0.11.7", "vue3-toastify": "^0.2.8"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.2", "@vue/eslint-config-prettier": "^10.2.0", "axios": "^1.9.0", "cross-env": "^7.0.3", "eslint": "^9.25.1", "eslint-plugin-vue": "^10.1.0", "glob": "^11.0.2", "globals": "^16.0.0", "inquirer": "^12.6.0", "inquirer-autocomplete-prompt": "^2.0.0", "joi": "^17.13.3", "jsdom": "^26.1.0", "miragejs": "^0.1.48", "prettier": "^3.5.3", "sass": "^1.87.0", "sass-embedded": "^1.87.0", "vite": "^6.3.1", "vitest": "^3.1.3"}, "msw": {"workerDirectory": ["public"]}}