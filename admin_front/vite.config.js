import vue from '@vitejs/plugin-vue'
import { defineConfig, loadEnv } from 'vite'
import { fileURLToPath, URL } from 'node:url'
import { loadClientConfig } from './src/core/scripts/clients.scripts.js'

export default defineConfig(async ({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const clientConfig = await loadClientConfig(env.VITE_CLIENT)
  const BASE_DIR = (env?.VITE_BASE_URL || 'app').replace(/(^\/|\/$)/gi, '')

  return {
    base: `/${BASE_DIR}/`,
    define: {
      $settings: { ...clientConfig, BASE_DIR: `${BASE_DIR}`, START_MOCK_SERVER: env.START_MOCK_SERVER },
      __VUE_PROD_DEVTOOLS__: false,
    },
    plugins: [vue()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    build: {
      outDir: `../public/${BASE_DIR}`,
      rollupOptions: {
        output: {
          manualChunks: function (id) {
            if (id.includes('node_modules')) {
              const file = id.toString().split('node_modules/')[1].split('/')[0].toString()
              if (file.includes('fortawesome')) {
                return 'vendor_icons'
              }
              if (file.includes('vue')) {
                return 'vendor_vue'
              }
              return 'vendor'
            }
          },
        },
      },
    },
    test: {
      environment: 'jsdom',
    },
  }
})
