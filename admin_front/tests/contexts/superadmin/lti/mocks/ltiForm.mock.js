import { LTI_API_ROUTES } from '@/contexts/superadmin/lti/constants/LTI.constants.js'
import { Response } from 'miragejs'

const availableData = [
  { id: '590df130-fdce-4301-8113-9b5f2baacd6a', name: 'Test Name 2', client_id: 'testName2' },
  { id: 'dc6bb80f-1f3d-4945-b52a-63087c136522', name: 'Test Name 1', client_id: 'testName1' },
  { id: 'd7960785-48a0-4086-a10e-ff0d3194fdbe', name: 'Test Name 3', client_id: 'testName3' },
  { id: 'e81d66da-4db5-4bdf-b8dd-df9e610ab58a', name: 'Test Name 4', client_id: 'testName4' },
  { id: 'a1e368a1-36a6-4f04-9910-bea5889a958a', name: 'Test Name 5', client_id: 'testName5' },
]

const deploymentList = [
  {
    id: '6bb8dc0f-1f3d-4945-b52a-13652263087c',
    name: 'Deployment test 1',
    deployment_id: 'DT1-136522',
  },
  {
    id: '590df130-1f3d-4945-b52a-bea5889a958a',
    name: 'Deployment test 2',
    deployment_id: 'DT2-a5889a9',
  },
  {
    id: '3194fdbf-1f3d-4945-b52a-63087c136522',
    name: 'Deployment test 3',
    deployment_id: 'DT3-087c1',
  },
  {
    id: '9a958a0f-1f3d-4945-b52a-9b5f2baacd6a',
    name: 'Deployment test 4',
    deployment_id: 'DT4-9b5f2baa',
  },
]

export default [
  {
    url: LTI_API_ROUTES.FORM.INIT,
    callback: (_, request) => {
      const { registerID } = request.params
      const found = availableData.find((register) => register.id === registerID)

      if (!found)
        return new Response(
          403,
          {},
          {
            data: 'Register not found',
            error: true,
          }
        )

      return {
        data: {
          id: found.id,
          name: found.name,
          client_id: found.client_id,
          platform: {
            name: 'Name',
            audience: 'Value audience',
            oidc_authentication_url: 'http://example-valid-url.com',
            oauth2_access_token_url: 'http://example-valid-url.com/lti1p3/auth/{identifier}/token',
            jwks_url: 'http://example-valid-url.com/jwks-value',
          },
          tool: {
            name: 'Name',
            audience: 'Value audience',
            oidc_initiation_url: 'http://example-valid-url.com',
            launch_url: 'http://example-valid-url.com',
            deep_linking_url: 'http://example-valid-url.com',
            jwks_url: 'http://example-valid-url.com/jwks-value',
          },
          deployments: deploymentList,
        },
      }
    },
    method: 'get',
  },
  {
    url: LTI_API_ROUTES.FORM.SAVE_PLATFORM,
    callback: () => {
      return {}
    },
    method: 'post',
  },
  {
    url: LTI_API_ROUTES.FORM.SAVE_TOOL,
    callback: () => {
      return {}
    },
    method: 'post',
  },
  {
    url: LTI_API_ROUTES.FORM.DEPLOYMENT.INIT,
    callback: () => ({ data: deploymentList }),
    method: 'get',
  },
  {
    url: LTI_API_ROUTES.FORM.DEPLOYMENT.CREATE,
    callback: () => {
      return {}
    },
    method: 'post',
  },
  {
    url: LTI_API_ROUTES.FORM.DEPLOYMENT.UPDATE,
    callback: () => {
      return {}
    },
    method: 'put',
  },
  {
    url: LTI_API_ROUTES.FORM.DEPLOYMENT.REMOVE,
    callback: () => {
      return {}
    },
    method: 'delete',
  },
]
