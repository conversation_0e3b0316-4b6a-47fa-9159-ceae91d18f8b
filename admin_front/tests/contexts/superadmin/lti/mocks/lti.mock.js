import { LTI_API_ROUTES } from '@/contexts/superadmin/lti/constants/LTI.constants.js'

export default [
  {
    url: LTI_API_ROUTES.HOME.LIST,
    callback: () => {
      return {
        data: [
          { id: 'dc6bb80f-1f3d-4945-b52a-63087c136522', name: 'Test Name 1', client_id: 'testName1' },
          { id: '590df130-fdce-4301-8113-9b5f2baacd6a', name: 'Test Name 2', client_id: 'testName2' },
          { id: 'd7960785-48a0-4086-a10e-ff0d3194fdbe', name: 'Test Name 3', client_id: 'testName3' },
          { id: 'e81d66da-4db5-4bdf-b8dd-df9e610ab58a', name: 'Test Name 4', client_id: 'testName4' },
          { id: 'a1e368a1-36a6-4f04-9910-bea5889a958a', name: 'Test Name 5', client_id: 'testName5' },
        ],
      }
    },
    method: 'get',
  },
  {
    url: LTI_API_ROUTES.HOME.CREATE,
    callback: () => {
      return {}
    },
    method: 'post',
  },
  {
    url: LTI_API_ROUTES.HOME.UPDATE,
    callback: () => {
      return {}
    },
    method: 'put',
  },
  {
    url: LTI_API_ROUTES.HOME.REMOVE,
    callback: () => {
      return {}
    },
    method: 'delete',
  },
]
