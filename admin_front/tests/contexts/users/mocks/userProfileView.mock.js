import { USER_API_ROUTES } from '@/contexts/users/constants/users.constants.js'
import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export default [
  {
    url: USER_API_ROUTES.PROFILE.INIT_DATA,
    callback: (_, request) => {
      const { id } = request.params
      const roles = Object.values(USER_ROLE_LIST)
      return {
        status: 200,
        error: false,
        data: id
          ? {
              id,
              name: 'user',
              last_name: 'test',
              email: '<EMAIL>',
              avatar: 'https://picsum.photos/200/300',
              language: 'es',
              code: `UR${id}`,
              company: 'Example',
              zone: 'Europe/Madrid',
              dni: '*********',
              open_campus: false,
              extra: [
                { label: 'Campo extra 1', key: 'field_1', value: 'Test init value 1' },
                { label: 'Campo extra 2', key: 'field_2', value: 'Test init value 2' },
              ],
              roles: roles
                .filter((_, index) => index > Math.min(Math.floor(Math.random() * roles.length) - 1, roles.length - 1))
                .map((role) => role),
              actions: {
                allow_login_as: true,
                deletable: true,
                editable: true,
              },
            }
          : {},
      }
    },
    method: 'get',
  },
]
