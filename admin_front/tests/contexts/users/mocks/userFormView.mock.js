import { USER_API_ROUTES } from '@/contexts/users/constants/users.constants.js'
import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export default [
  {
    url: USER_API_ROUTES.FORM.INIT_DATA,
    callback: (_, request) => {
      const { id } = request.params
      const roles = Object.values(USER_ROLE_LIST)
      return {
        status: 200,
        error: false,
        data: id
          ? {
              id,
              name: 'user',
              last_name: 'test',
              email: '<EMAIL>',
              avatar: 'https://picsum.photos/200/300',
              language: 'es',
              code: `UR${id}`,
              company: 1,
              zone: 'Europe/Madrid',
              dni: '*********',
              open_campus: false,
              extra: [
                { key: 'field_1', value: 'Test init value 1' },
                { key: 'field_2', value: '' },
                { key: 'field_3', value: 'Test init value 3' },
              ],
              roles: roles
                .filter((_, index) => index > Math.min(Math.floor(Math.random() * roles.length) - 1, roles.length - 1))
                .map((role) => ({ id: role })),
            }
          : {},
      }
    },
    method: 'get',
  },
  {
    url: USER_API_ROUTES.FORM.EXTRA_FIELDS,
    callback: () => ({
      status: 200,
      error: false,
      data: [
        { key: 'field_1', label: 'Campo extra 1' },
        { key: 'field_2', label: 'Campo extra 2' },
        { key: 'field_3', label: 'Campo extra 3' },
      ],
    }),
    method: 'get',
  },
  {
    url: USER_API_ROUTES.FORM.NEW_USER,
    callback: () => ({
      status: 200,
      error: false,
      data: {},
    }),
    method: 'post',
  },
  {
    url: USER_API_ROUTES.FORM.UPDATE_USER,
    callback: () => ({
      status: 200,
      error: false,
      data: {},
    }),
    method: 'put',
  },
]
