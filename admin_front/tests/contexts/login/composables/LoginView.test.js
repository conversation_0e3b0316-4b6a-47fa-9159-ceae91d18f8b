import { beforeEach, describe, it } from 'vitest'
import { useLoginView } from '@/contexts/login/composables/LoginView.composable.js'
import { setActivePinia, createPinia } from 'pinia'
import { useAuth } from '@/contexts/shared/composables/auth.composable.js'

describe('[LoginView/composable]', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })
  const { login, loginData } = useLoginView()
  describe('LoginForm', () => {
    it('Should throw email error', async () => {
      loginData.payload = { email: '', password: '12345678' }
      await login()
      return 'email' in loginData.errors
    })
    it('Should throw password error', async () => {
      loginData.payload = { email: '', password: '123' }
      await login()
      return 'password' in loginData.errors
    })
    it('Should success login fails', async () => {
      loginData.payload = { email: '<EMAIL>', password: 'password1234' }
      await login()
      const { isAuth } = useAuth()
      return !isAuth.value
    })
    it('Should success login', async () => {
      loginData.payload = { email: import.meta.env.VITE_TEST_USER, password: import.meta.env.VITE_TEST_PASS }
      await login()
      const { isAuth } = useAuth()
      return isAuth.value
    })
  })
})
