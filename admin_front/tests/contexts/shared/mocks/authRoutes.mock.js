import { Response } from 'miragejs'
import { REFRESH_TOKEN_URL } from '@/core/constants/general.constant.js'

export default [
  {
    url: '/login',
    callback: (_, request) => {
      const { email, password } = JSON.parse(request.requestBody)
      if (email === import.meta.env.VITE_TEST_USER && password === import.meta.env.VITE_TEST_PASS)
        return {
          status: 200,
          error: false,
          data: {
            token:
              'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i7zlNfjPH3v_RhOlhY7U0dF07WZfbKMcN3gKiYJIIJ9Kdo1FTuCr_KxFowU91LgM62ZYtF5Jk_qupS9cywX1QKhBzE_t_j02LLFX4yeRZ-tGkHrYZSAJ7O5yUrB7Mn1SKNvXO4OQ69Ztoixn9f18yuic-3NBCwKvaV37diO4F8m0Y9fdQrQn0tIej5f2wP2z3iDxTuM_gsDu920jnjvjt0M1kJhwQBCXU15TWym4iD7zYIY2qn0JNYJ3wRBztPbHardOfXuLBqZ61DA2EpORYbvJnSUYewclhG0BciRXVF54lGEKW1PkQl5vOR57G3j-cn-Rf7gcRaxlk4yk6aKrDHuom8_w_WIRA11-MzqJbEAu1suBbdBr2t_vhw3iiFBc6kyWg1_9fAsUlsxdd_l6brAn1j1wmelsVJFg72IzRx3A_QpOjBu700hUFZTcTSRi2pLn88HMHOvubX6LW1LLtoWaD-9re9OpxmHbZpzoaT2LChXuYpwSOIqT13kXyZ4DhyhlpApFlubKf4Na-AZkG5Imvwh4sxSyulrqXk9TL2zr4P3kq9dATZFjPtdcxhgeo01NKEERHBWOzg2hR6KZ3YoksGUYbuxuX6BWFjBOBb5kmqmk86FULSDYBMH0ee7tZTkibR9H3sPM4HLM0kYDAZAHaN47nweGXNs_ttGP_vM',
            refresh_token: 'token_refresh_sample',
          },
        }

      return new Response(
        400,
        {},
        {
          status: 401,
          data: 'Las credenciales introducidas no son correctas.',
          error: true,
        }
      )
    },
    method: 'post',
  },
  {
    url: REFRESH_TOKEN_URL,
    callback: () => ({
      status: 200,
      error: false,
      data: {
        token:
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i7zlNfjPH3v_RhOlhY7U0dF07WZfbKMcN3gKiYJIIJ9Kdo1FTuCr_KxFowU91LgM62ZYtF5Jk_qupS9cywX1QKhBzE_t_j02LLFX4yeRZ-tGkHrYZSAJ7O5yUrB7Mn1SKNvXO4OQ69Ztoixn9f18yuic-3NBCwKvaV37diO4F8m0Y9fdQrQn0tIej5f2wP2z3iDxTuM_gsDu920jnjvjt0M1kJhwQBCXU15TWym4iD7zYIY2qn0JNYJ3wRBztPbHardOfXuLBqZ61DA2EpORYbvJnSUYewclhG0BciRXVF54lGEKW1PkQl5vOR57G3j-cn-Rf7gcRaxlk4yk6aKrDHuom8_w_WIRA11-MzqJbEAu1suBbdBr2t_vhw3iiFBc6kyWg1_9fAsUlsxdd_l6brAn1j1wmelsVJFg72IzRx3A_QpOjBu700hUFZTcTSRi2pLn88HMHOvubX6LW1LLtoWaD-9re9OpxmHbZpzoaT2LChXuYpwSOIqT13kXyZ4DhyhlpApFlubKf4Na-AZkG5Imvwh4sxSyulrqXk9TL2zr4P3kq9dATZFjPtdcxhgeo01NKEERHBWOzg2hR6KZ3YoksGUYbuxuX6BWFjBOBb5kmqmk86FULSDYBMH0ee7tZTkibR9H3sPM4HLM0kYDAZAHaN47nweGXNs_ttGP_vM',
        refresh_token: 'token_refresh_sample',
      },
    }),
    method: 'post',
  },
  {
    url: '/logout',
    callback: () => ({
      status: 200,
      error: false,
      data: 'User successfully logout',
    }),
    method: 'get',
  },
]
