import { CATEGORY_FILTER_API_ROUTES } from '@/contexts/shared/constants/categoryFilters.constants.js'

export default [
  {
    url: CATEGORY_FILTER_API_ROUTES.MAIN,
    callback() {
      return {
        data: {
          filterCategories: [
            {
              id: 1,
              name: 'Deparment /ES)',
            },
            {
              id: 2,
              name: '<PERSON><PERSON> (ES)',
            },
            {
              id: 3,
              name: '<PERSON><PERSON> (ES)',
            },
            {
              id: 4,
              name: 'Certification process (ES)',
            },
            {
              id: 5,
              name: 'Tienda (ES)',
            },
            {
              id: 6,
              name: 'zona (ES)',
            },
            {
              id: 7,
              name: '<PERSON><PERSON> (ES)',
            },
            {
              id: 9,
              name: '✌️filtro✌️',
            },
          ],
        },
      }
    },
  },
  {
    url: CATEGORY_FILTER_API_ROUTES.FILTER_DETAILS,
    callback(_, request) {
      const { id } = request.params
      return {
        status: 200,
        error: false,
        data: {
          filters:
            [
              [
                {
                  id: 1,
                  name: 'Departamento 1 (ES)',
                },
                {
                  id: 2,
                  name: 'Departamento 2 (ES)',
                },
                {
                  id: 11,
                  name: 'Store Manager (ES)',
                },
              ],
              [
                {
                  id: 3,
                  name: 'ES',
                },
                {
                  id: 4,
                  name: 'EN',
                },
                {
                  id: 5,
                  name: 'PT',
                },
                {
                  id: 6,
                  name: 'FR',
                },
              ],
              [
                {
                  id: 7,
                  name: 'Norte',
                },
                {
                  id: 8,
                  name: 'Sur',
                },
                {
                  id: 9,
                  name: 'Centro',
                },
              ],
              [
                {
                  id: 10,
                  name: 'Si',
                },
              ],
              [
                {
                  id: 12,
                  name: 'Madrid',
                },
                {
                  id: 13,
                  name: 'Lisboa',
                },
              ],
              [],
              [],
              [
                {
                  id: 15,
                  name: '👍subfiltro👍',
                },
                {
                  id: 16,
                  name: '👍subfiltro👍',
                },
              ],
            ][id - 1] || [],
        },
      }
    },
  },
]
