import { beforeEach, describe, it } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useAuthStore } from '@/contexts/shared/stores/auth.store.js'
import StorageService from '@/core/services/storage.service.js'
import { useAuth } from '@/contexts/shared/composables/auth.composable.js'

describe('[Shared/Stores]', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })
  describe('AuthStore', () => {
    const { isAuth } = useAuth()
    it('should successfully login', async () => {
      const { login } = useAuthStore()
      await login()
      return isAuth
    })
    it('should refresh the token', async () => {
      const { login, refreshToken } = useAuthStore()
      await login({ email: import.meta.env.VITE_TEST_USER, password: import.meta.env.VITE_TEST_PASS })
      const currentToken = `${StorageService.getToken()}`
      await refreshToken({
        headers: {
          'Content-Type': 'application/json',
        },
      })
      return currentToken !== StorageService.getToken()
    })
    it('should successfully logout', async () => {
      const { logout } = useAuthStore()
      await logout()
      return !isAuth
    })
  })
})
